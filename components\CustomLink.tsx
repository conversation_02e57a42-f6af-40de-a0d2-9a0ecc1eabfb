import Link from 'next/link';
import { LinkProps } from 'next/link';
import React from 'react';

interface CustomLinkProps extends LinkProps {
  children: React.ReactNode;
  className?: string;
}

export default function CustomLink({ href, children, ...props }: CustomLinkProps) {
  // Add trailing slash to internal links if they don't already have one
  let formattedHref = href;
  
  if (typeof href === 'string' && 
      href.startsWith('/') && 
      !href.endsWith('/') && 
      !href.includes('.') && 
      !href.includes('#')) {
    formattedHref = `${href}/`;
  }
  
  return (
    <Link href={formattedHref} {...props}>
      {children}
    </Link>
  );
}