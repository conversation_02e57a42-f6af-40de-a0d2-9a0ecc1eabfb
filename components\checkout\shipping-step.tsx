'use client';

import { useState, useEffect } from 'react';
import { Truck, Clock, ArrowLeft } from 'lucide-react';
import { useCart } from '@/lib/context/cart-context';

interface ShippingOption {
  id: string;
  name: string;
  description: string;
  price: number;
  estimatedDays: string;
}

interface CustomerInfo {
  email: string;
  phone: string;
  shippingAddress: {
    firstName: string;
    lastName: string;
    line1: string;
    line2: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  billingAddress: any;
  sameAsBilling: boolean;
}

interface ShippingStepProps {
  customerInfo: CustomerInfo;
  shippingOptions: ShippingOption[];
  setShippingOptions: (options: ShippingOption[]) => void;
  selectedShipping: ShippingOption | null;
  setSelectedShipping: (option: ShippingOption) => void;
  onComplete: () => void;
  onBack: () => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

export default function ShippingStep({
  customerInfo,
  shippingOptions,
  setShippingOptions,
  selectedShipping,
  setSelectedShipping,
  onComplete,
  onBack,
  isLoading,
  setIsLoading
}: ShippingStepProps) {
  const [error, setError] = useState<string | null>(null);
  const [ratesCalculated, setRatesCalculated] = useState(false);
  const { state } = useCart();

  // Calculate shipping rates when component mounts
  useEffect(() => {
    calculateShippingRates();
  }, []);

  const calculateShippingRates = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/calculate-shipping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          address: {
            name: `${customerInfo.shippingAddress.firstName} ${customerInfo.shippingAddress.lastName}`,
            line1: customerInfo.shippingAddress.line1,
            line2: customerInfo.shippingAddress.line2,
            city: customerInfo.shippingAddress.city,
            state: customerInfo.shippingAddress.state,
            postalCode: customerInfo.shippingAddress.postalCode,
            country: customerInfo.shippingAddress.country,
          },
          items: state.items,
          subtotal: state.subtotal
        }),
      });

      const data = await response.json();

      if (data.success) {
        const formattedOptions: ShippingOption[] = data.rates.map((rate: any, index: number) => ({
          id: `shipping_${index}`,
          name: rate.name,
          description: rate.description,
          price: rate.price,
          estimatedDays: rate.estimatedDays,
        }));

        setShippingOptions(formattedOptions);
        setRatesCalculated(true);

        // Auto-select the first (usually cheapest) option
        if (formattedOptions.length > 0) {
          setSelectedShipping(formattedOptions[0]);
        }

        // Auto-scroll to shipping options
        setTimeout(() => {
          const shippingSection = document.querySelector('[data-shipping-options]');
          if (shippingSection) {
            shippingSection.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        }, 100);
      } else {
        setError(data.error || 'Failed to calculate shipping rates');
      }
    } catch (err) {
      setError('Error calculating shipping rates');
      console.error('Shipping calculation error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleShippingSelection = (option: ShippingOption) => {
    setSelectedShipping(option);
  };

  const handleContinue = () => {
    if (!selectedShipping) {
      setError('Please select a shipping option');
      return;
    }
    onComplete();
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-medium flex items-center">
          <Truck size={20} className="mr-2" />
          Shipping Options
        </h2>
        <button
          onClick={onBack}
          className="flex items-center text-gray-600 hover:text-gray-800 text-sm"
        >
          <ArrowLeft size={16} className="mr-1" />
          Back to Customer Info
        </button>
      </div>

      {/* Shipping Address Summary */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <h3 className="font-medium mb-2">Shipping to:</h3>
        <div className="text-sm text-gray-600">
          <p>{customerInfo.shippingAddress.firstName} {customerInfo.shippingAddress.lastName}</p>
          <p>{customerInfo.shippingAddress.line1}</p>
          {customerInfo.shippingAddress.line2 && <p>{customerInfo.shippingAddress.line2}</p>}
          <p>
            {customerInfo.shippingAddress.city}, {customerInfo.shippingAddress.state} {customerInfo.shippingAddress.postalCode}
          </p>
          <p>{customerInfo.shippingAddress.country}</p>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
          <p className="mt-2 text-gray-600">Calculating shipping rates...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <p className="text-red-700 text-sm">{error}</p>
          <button
            onClick={calculateShippingRates}
            className="mt-2 text-red-700 underline text-sm hover:text-red-800"
          >
            Try again
          </button>
        </div>
      )}

      {/* Shipping Options */}
      {ratesCalculated && shippingOptions.length > 0 && (
        <div className="space-y-3" data-shipping-options>
          <h3 className="font-medium mb-4">Choose your shipping method:</h3>
          
          {shippingOptions.map((option) => (
            <label
              key={option.id}
              className={`block p-4 border rounded-lg cursor-pointer transition-colors ${
                selectedShipping?.id === option.id
                  ? 'border-black bg-gray-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <input
                type="radio"
                name="shipping"
                checked={selectedShipping?.id === option.id}
                onChange={() => handleShippingSelection(option)}
                className="sr-only"
              />
              
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="font-medium text-sm mb-1">
                    {option.name}
                  </div>
                  <div className="text-gray-600 text-sm mb-2">
                    {option.description}
                  </div>
                  <div className="flex items-center text-gray-500 text-sm">
                    <Clock size={14} className="mr-1" />
                    <span>{option.estimatedDays}</span>
                  </div>
                </div>
                
                <div className="font-medium text-lg ml-4">
                  {option.price === 0 ? (
                    <span className="text-green-600">FREE</span>
                  ) : (
                    `$${option.price.toFixed(2)}`
                  )}
                </div>
              </div>
            </label>
          ))}
        </div>
      )}

      {/* No Shipping Options */}
      {ratesCalculated && shippingOptions.length === 0 && !isLoading && (
        <div className="text-center py-8">
          <p className="text-gray-600 mb-4">
            No shipping options available for this address.
          </p>
          <button
            onClick={onBack}
            className="text-black underline hover:text-gray-700"
          >
            Update shipping address
          </button>
        </div>
      )}

      {/* Continue Button */}
      {ratesCalculated && shippingOptions.length > 0 && (
        <div className="flex justify-between pt-6 mt-6 border-t">
          <button
            onClick={onBack}
            className="bg-gray-200 text-black font-medium py-3 px-6 rounded-lg hover:bg-gray-300 transition-colors"
          >
            Back
          </button>
          
          <button
            onClick={handleContinue}
            disabled={!selectedShipping}
            className="bg-black text-white font-medium py-3 px-6 rounded-lg hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Continue to Payment
          </button>
        </div>
      )}

      {/* Selection Error */}
      {error && ratesCalculated && (
        <div className="mt-4">
          <p className="text-red-500 text-sm text-center">{error}</p>
        </div>
      )}
    </div>
  );
}
