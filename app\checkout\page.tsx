'use client';

import { useState, useEffect } from 'react';
import { useCart } from '@/lib/context/cart-context';
import { useRouter } from 'next/navigation';
import { helvetica, editorialNew } from '@/app/fonts';
import CustomerInfoStep from '@/components/checkout/customer-info-step';
import ShippingStep from '@/components/checkout/shipping-step';
import PaymentStep from '@/components/checkout/payment-step';
import cx from 'classnames';

// Checkout steps
const STEPS = {
  CUSTOMER_INFO: 1,
  SHIPPING: 2,
  PAYMENT: 3
};

interface CustomerInfo {
  email: string;
  phone: string;
  shippingAddress: {
    firstName: string;
    lastName: string;
    line1: string;
    line2: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  billingAddress: {
    firstName: string;
    lastName: string;
    line1: string;
    line2: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  sameAsBilling: boolean;
}

interface ShippingOption {
  id: string;
  name: string;
  description: string;
  price: number;
  estimatedDays: string;
}

export default function CheckoutPage() {
  const { state } = useCart();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(STEPS.CUSTOMER_INFO);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    email: '',
    phone: '',
    shippingAddress: {
      firstName: '',
      lastName: '',
      line1: '',
      line2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'US'
    },
    billingAddress: {
      firstName: '',
      lastName: '',
      line1: '',
      line2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'US'
    },
    sameAsBilling: true
  });
  const [shippingOptions, setShippingOptions] = useState<ShippingOption[]>([]);
  const [selectedShipping, setSelectedShipping] = useState<ShippingOption | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [cartLoaded, setCartLoaded] = useState(false);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  // Mark cart as loaded after a brief delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setCartLoaded(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // Redirect if cart is empty after it's loaded
  useEffect(() => {
    if (cartLoaded && state.items.length === 0) {
      console.log('Cart is empty, redirecting to home');
      router.push('/');
    }
  }, [cartLoaded, state.items.length, router]);

  const handleStepComplete = (step: number) => {
    if (step === STEPS.CUSTOMER_INFO) {
      // Validate customer info and proceed to shipping
      setCurrentStep(STEPS.SHIPPING);
    } else if (step === STEPS.SHIPPING) {
      // Proceed to payment
      setCurrentStep(STEPS.PAYMENT);
    }
  };

  // Show loading while cart is loading
  if (!cartLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
          <p className="mt-4 text-gray-600">Loading checkout...</p>
        </div>
      </div>
    );
  }

  // Show empty cart message if cart is loaded but empty
  if (cartLoaded && state.items.length === 0) {
    // Check if we just completed an order
    const justCompletedOrder = sessionStorage.getItem('order-just-completed');

    if (justCompletedOrder) {
      // Clear the flag and redirect to success page
      sessionStorage.removeItem('order-just-completed');
      console.log('🔄 Redirecting to success page after order completion:', justCompletedOrder);
      router.replace(`/success?order_id=${justCompletedOrder}`);
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
            <p className="mt-4 text-gray-600">Redirecting to order confirmation...</p>
          </div>
        </div>
      );
    }

    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className={cx(editorialNew.className, "text-2xl mb-4")}>Your cart is empty</h1>
          <button
            onClick={() => router.push('/')}
            className="bg-black text-white px-6 py-3 rounded font-helvetica"
          >
            Continue Shopping
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={cx(helvetica.className, "min-h-screen bg-gray-50 relative")}>
      {/* Payment Processing Overlay */}
      {isProcessingPayment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className="bg-white p-8 rounded-lg shadow-lg text-center max-w-md mx-4">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-black mb-4"></div>
            <h3 className={cx(editorialNew.className, "text-xl font-normal mb-2")}>
              Processing Your Order
            </h3>
            <p className="text-gray-600 text-sm">
              Please don't close this window. We're confirming your payment and preparing your order...
            </p>
          </div>
        </div>
      )}

      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className={cx(editorialNew.className, "text-3xl font-normal mb-2")}>
            Checkout
          </h1>
          <div className="flex justify-center space-x-8 text-sm">
            <div className={`flex items-center ${currentStep >= STEPS.CUSTOMER_INFO ? 'text-black' : 'text-gray-400'}`}>
              <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs mr-2 ${
                currentStep >= STEPS.CUSTOMER_INFO ? 'bg-black text-white' : 'bg-gray-300'
              }`}>1</span>
              Customer Info
            </div>
            <div className={`flex items-center ${currentStep >= STEPS.SHIPPING ? 'text-black' : 'text-gray-400'}`}>
              <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs mr-2 ${
                currentStep >= STEPS.SHIPPING ? 'bg-black text-white' : 'bg-gray-300'
              }`}>2</span>
              Shipping
            </div>
            <div className={`flex items-center ${currentStep >= STEPS.PAYMENT ? 'text-black' : 'text-gray-400'}`}>
              <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs mr-2 ${
                currentStep >= STEPS.PAYMENT ? 'bg-black text-white' : 'bg-gray-300'
              }`}>3</span>
              Payment
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {currentStep === STEPS.CUSTOMER_INFO && (
              <CustomerInfoStep 
                customerInfo={customerInfo}
                setCustomerInfo={setCustomerInfo}
                onComplete={() => handleStepComplete(STEPS.CUSTOMER_INFO)}
                isLoading={isLoading}
              />
            )}
            
            {currentStep === STEPS.SHIPPING && (
              <ShippingStep 
                customerInfo={customerInfo}
                shippingOptions={shippingOptions}
                setShippingOptions={setShippingOptions}
                selectedShipping={selectedShipping}
                setSelectedShipping={setSelectedShipping}
                onComplete={() => handleStepComplete(STEPS.SHIPPING)}
                onBack={() => setCurrentStep(STEPS.CUSTOMER_INFO)}
                isLoading={isLoading}
                setIsLoading={setIsLoading}
              />
            )}
            
            {currentStep === STEPS.PAYMENT && (
              <PaymentStep
                customerInfo={customerInfo}
                selectedShipping={selectedShipping}
                onBack={() => setCurrentStep(STEPS.SHIPPING)}
                isLoading={isLoading}
                setIsLoading={setIsLoading}
                setIsProcessingPayment={setIsProcessingPayment}
              />
            )}
          </div>

          {/* Order Summary Sidebar */}
          <div className="lg:col-span-1">
            <OrderSummary 
              items={state.items}
              subtotal={state.subtotal}
              selectedShipping={selectedShipping}
            />
          </div>
        </div>
      </div>
    </div>
  );
}



function OrderSummary({ items, subtotal, selectedShipping }: any) {
  const total = subtotal + (selectedShipping?.price || 0);
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm sticky top-8">
      <h3 className="text-lg font-medium mb-4">Order Summary</h3>
      
      {/* Items */}
      <div className="space-y-3 mb-4">
        {items.map((item: any) => (
          <div key={item.id} className="flex justify-between text-sm">
            <span>{item.title} × {item.quantity}</span>
            <span>${(item.price * item.quantity).toFixed(2)}</span>
          </div>
        ))}
      </div>
      
      {/* Totals */}
      <div className="border-t pt-4 space-y-2">
        <div className="flex justify-between text-sm">
          <span>Subtotal:</span>
          <span>${subtotal.toFixed(2)}</span>
        </div>
        {selectedShipping && (
          <div className="flex justify-between text-sm">
            <span>Shipping:</span>
            <span>${selectedShipping.price.toFixed(2)}</span>
          </div>
        )}
        <div className="flex justify-between font-medium text-lg border-t pt-2">
          <span>Total:</span>
          <span>${total.toFixed(2)}</span>
        </div>
      </div>
    </div>
  );
}
