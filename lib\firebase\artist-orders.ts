import { 
  collection, 
  query, 
  where, 
  orderBy, 
  getDocs, 
  doc, 
  updateDoc, 
  serverTimestamp,
  getDoc 
} from 'firebase/firestore';
import { db } from './config';

export interface ArtistOrder {
  id: string;
  type: 'regular' | 'custom';
  orderNumber?: string;
  
  // Basic order info
  customerEmail: string;
  customerName?: string;
  customerPhone?: string;
  
  // Order details
  items: Array<{
    id?: string;
    name: string;
    title?: string;
    description?: string;
    quantity: number;
    amount: number;
    price?: number;
    imageUrl?: string;
  }>;
  
  subtotal: number;
  shippingCost: number;
  total: number;
  totalAmount?: number; // for custom orders
  
  // Shipping
  shippingAddress?: {
    name?: string;
    firstName?: string;
    lastName?: string;
    line1: string;
    line2?: string;
    city: string;
    state: string;
    postalCode?: string;
    postal_code?: string;
    country: string;
  };
  
  shippingMethod?: {
    id?: string;
    name: string;
    description?: string;
    estimatedDays?: string;
  };
  
  // Payment
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentIntentId?: string;
  
  // Order status
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  
  // Timestamps
  createdAt: any;
  updatedAt?: any;
  paidAt?: any;
  
  // Tracking
  trackingNumber?: string;
  estimatedDelivery?: string;
  
  // Notes
  notes?: string;
  
  // Custom order specific
  invoiceId?: string;
  invoiceTitle?: string;
  invoiceDescription?: string;
  amount?: number; // invoice amount
}

/**
 * Get all orders for an artist (both regular and custom orders)
 */
export async function getArtistOrders(artistId: string): Promise<ArtistOrder[]> {
  try {
    const orders: ArtistOrder[] = [];
    
    // Get regular orders that contain products from this artist
    // Note: This would require orders to have artistId or we need to cross-reference with products
    // For now, we'll focus on custom orders which are easier to track
    
    // Get custom orders for this artist
    const customOrdersRef = collection(db, 'custom_orders');
    const customOrdersQuery = query(
      customOrdersRef,
      where('artistId', '==', artistId),
      orderBy('createdAt', 'desc')
    );
    
    const customOrdersSnapshot = await getDocs(customOrdersQuery);
    
    customOrdersSnapshot.forEach((doc) => {
      const data = doc.data();
      const order: ArtistOrder = {
        id: doc.id,
        type: 'custom',
        customerEmail: data.customerEmail || '',
        customerName: data.customerName || `${data.firstName || ''} ${data.lastName || ''}`.trim(),
        customerPhone: data.customerPhone,
        
        items: data.items ? data.items.map((item: any) => ({
          id: item.id,
          name: item.name || item.title || 'Unnamed Item',
          title: item.title,
          description: item.description || '',
          quantity: item.quantity || 1,
          amount: item.amount || item.price || 0,
          price: item.price,
          imageUrl: item.imageUrl
        })) : [{
          name: data.invoiceTitle || 'Custom Order',
          description: data.invoiceDescription || '',
          quantity: 1,
          amount: data.amount || data.totalAmount || 0
        }],
        
        subtotal: data.amount || data.totalAmount || 0,
        shippingCost: data.shippingCost || 0,
        total: data.totalAmount || (data.amount + (data.shippingCost || 0)),
        
        shippingAddress: data.shippingAddress ? {
          name: data.shippingAddress.name,
          firstName: data.firstName,
          lastName: data.lastName,
          line1: data.shippingAddress.line1,
          line2: data.shippingAddress.line2,
          city: data.shippingAddress.city,
          state: data.shippingAddress.state,
          postalCode: data.shippingAddress.postalCode,
          country: data.shippingAddress.country
        } : undefined,
        
        shippingMethod: data.shippingMethod ? {
          id: data.shippingMethod.id || 'unknown',
          name: data.shippingMethod.name || data.shippingMethod || 'Standard Shipping',
          description: data.shippingMethod.description || data.shippingDescription || '',
          estimatedDays: data.shippingMethod.estimatedDays || data.estimatedDelivery || '3-5 business days'
        } : undefined,
        
        paymentStatus: 'paid', // Custom orders are created after payment
        paymentIntentId: data.paymentIntentId,
        
        status: data.status || 'confirmed',
        
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
        paidAt: data.paidAt,
        
        trackingNumber: data.trackingNumber,
        estimatedDelivery: data.estimatedDelivery,
        notes: data.notes,
        
        // Custom order specific
        invoiceId: data.invoiceId,
        invoiceTitle: data.invoiceTitle,
        invoiceDescription: data.invoiceDescription,
        amount: data.amount
      };
      
      orders.push(order);
    });
    
    return orders;
  } catch (error) {
    console.error('Error getting artist orders:', error);
    throw new Error('Failed to get artist orders');
  }
}

/**
 * Update order status
 */
export async function updateOrderStatus(
  orderId: string, 
  status: ArtistOrder['status'],
  notes?: string,
  trackingNumber?: string
): Promise<void> {
  try {
    // Try custom orders first
    const customOrderRef = doc(db, 'custom_orders', orderId);
    const customOrderSnap = await getDoc(customOrderRef);
    
    if (customOrderSnap.exists()) {
      const updateData: any = {
        status,
        updatedAt: serverTimestamp()
      };
      
      if (notes) updateData.notes = notes;
      if (trackingNumber) updateData.trackingNumber = trackingNumber;
      
      await updateDoc(customOrderRef, updateData);
      return;
    }
    
    // Try regular orders
    const orderRef = doc(db, 'orders', orderId);
    const orderSnap = await getDoc(orderRef);
    
    if (orderSnap.exists()) {
      const updateData: any = {
        status,
        updatedAt: serverTimestamp()
      };
      
      if (notes) updateData.notes = notes;
      if (trackingNumber) updateData.trackingNumber = trackingNumber;
      
      await updateDoc(orderRef, updateData);
      return;
    }
    
    throw new Error('Order not found');
  } catch (error) {
    console.error('Error updating order status:', error);
    throw new Error('Failed to update order status');
  }
}

/**
 * Get order statistics for an artist
 */
export async function getArtistOrderStats(artistId: string) {
  try {
    const orders = await getArtistOrders(artistId);
    
    const stats = {
      total: orders.length,
      pending: orders.filter(o => o.status === 'pending').length,
      confirmed: orders.filter(o => o.status === 'confirmed').length,
      processing: orders.filter(o => o.status === 'processing').length,
      shipped: orders.filter(o => o.status === 'shipped').length,
      delivered: orders.filter(o => o.status === 'delivered').length,
      cancelled: orders.filter(o => o.status === 'cancelled').length,
      totalRevenue: orders.reduce((sum, order) => sum + order.total, 0),
      averageOrderValue: orders.length > 0 ? orders.reduce((sum, order) => sum + order.total, 0) / orders.length : 0
    };
    
    return stats;
  } catch (error) {
    console.error('Error getting artist order stats:', error);
    throw new Error('Failed to get order statistics');
  }
}
