// app/api/send-email/route.ts
import { NextRequest, NextResponse } from "next/server";
import nodemailer from "nodemailer";

// Function to verify reCAPTCHA token
async function verifyCaptcha(token: string) {
  try {
    const response = await fetch(
      `https://www.google.com/recaptcha/api/siteverify?secret=${process.env.RECAPTCHA_SECRET_KEY}&response=${token}`,
      { method: "POST" }
    );
    const data = await response.json();
    return data.success;
  } catch (error) {
    console.error("reCAPTCHA verification error:", error);
    return false;
  }
}

export async function POST(req: NextRequest) {
  try {
    const data = await req.json();
    const { to, subject, captchaToken } = data;

    // Verify captcha
    if (captchaToken) {
      const isValidCaptcha = await verifyCaptcha(captchaToken);
      if (!isValidCaptcha) {
        return NextResponse.json(
          { error: "Invalid captcha. Please try again." },
          { status: 400 }
        );
      }
    }

    // Create HTML content from form data
    const htmlContent = generateEmailContent(data);
    
    // Create plain text version
    const textContent = generatePlainTextContent(data);

    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: Number(process.env.SMTP_PORT),
      secure: Number(process.env.SMTP_PORT) === 465,
      auth: {
        user: process.env.SMTP_USER!,
        pass: process.env.SMTP_PASS!,
      },
    });

    const mailOptions = {
      from: process.env.SMTP_USER,
      to,
      subject,
      text: textContent,
      html: htmlContent,
    };

    const info = await transporter.sendMail(mailOptions);
    return NextResponse.json({ message: "Email sent", info });
  } catch (error: any) {
    console.error("SMTP Error:", error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

function generateEmailContent(data: any): string {
  // Remove internal fields that shouldn't be displayed in the email
  const { to, subject, captchaToken, ...formData } = data;

  return `
    <!DOCTYPE html>
    <html>
      <head>
        <style>
          body { font-family: 'Helvetica', Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #000; color: white; padding: 20px; text-align: center; margin-bottom: 30px; }
          .logo { font-size: 24px; font-weight: bold; }
          .tagline { font-size: 14px; margin-top: 5px; opacity: 0.9; }
          h2 { color: #000; margin-top: 25px; border-bottom: 1px solid #ddd; padding-bottom: 5px; font-size: 18px; }
          .field { margin-bottom: 15px; background: #f9f9f9; padding: 10px; border-radius: 5px; }
          .label { font-weight: bold; color: #000; }
          .value { margin-top: 5px; color: #333; }
          .message-field { background: #e3f2fd; border: 1px solid #2196f3; padding: 15px; border-radius: 5px; margin-top: 20px; }
          .footer { margin-top: 30px; font-size: 12px; color: #666; border-top: 1px solid #ddd; padding-top: 15px; text-align: center; }
          .subject-line { background: #e3f2fd; border: 1px solid #2196f3; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">Milo's Menagerie</div>
            <div class="tagline">Custom 3D Printing & Design</div>
          </div>

          <div class="subject-line">
            <strong>New Contact Form Submission:</strong> ${subject}
          </div>

          <h2>Contact Information</h2>
          <div class="field">
            <div class="label">First Name:</div>
            <div class="value">${formData.firstName || 'Not provided'}</div>
          </div>
          <div class="field">
            <div class="label">Last Name:</div>
            <div class="value">${formData.lastName || 'Not provided'}</div>
          </div>
          <div class="field">
            <div class="label">Email Address:</div>
            <div class="value">${formData.email || 'Not provided'}</div>
          </div>
          <div class="field">
            <div class="label">Phone Number:</div>
            <div class="value">${formData.phone || 'Not provided'}</div>
          </div>

          ${formData.message ? `
          <h2>Message</h2>
          <div class="message-field">
            <div class="value">${formData.message}</div>
          </div>
          ` : ''}

          <div class="footer">
            <p>This inquiry was submitted through the contact form on <strong>milosmenagerie.com</strong></p>
            <p>Respond promptly to provide excellent customer service!</p>
          </div>
        </div>
      </body>
    </html>
  `;
}

function generatePlainTextContent(data: any): string {
  // Remove internal fields that shouldn't be displayed in the email
  const { to, subject, captchaToken, ...formData } = data;

  return `
MILO'S MENAGERIE - NEW CONTACT FORM SUBMISSION
==============================================
Subject: ${subject}

CONTACT INFORMATION
------------------
First Name: ${formData.firstName || 'Not provided'}
Last Name: ${formData.lastName || 'Not provided'}
Email Address: ${formData.email || 'Not provided'}
Phone Number: ${formData.phone || 'Not provided'}

${formData.message ? `
MESSAGE
-------
${formData.message}
` : ''}

--
This inquiry was submitted through the contact form on milosmenagerie.com
Respond promptly to provide excellent customer service!
  `;
}

export async function OPTIONS() {
  return NextResponse.json({}, {
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}
