'use client';

import { useState, useEffect } from 'react';
import { User } from 'firebase/auth';
import { 
  getPendingApplications, 
  getAllApplications, 
  approveArtistApplication, 
  rejectArtistApplication,
  ArtistApplication 
} from '@/lib/firebase/artists';
import { hasPermission } from '@/lib/auth/admin-firestore';
import { helvetica, editorialNew } from '@/app/fonts';
import cx from 'classnames';

interface ArtistApplicationsProps {
  user: User;
  adminUser: any;
}

export default function ArtistApplications({ user, adminUser }: ArtistApplicationsProps) {
  const [applications, setApplications] = useState<ArtistApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'pending' | 'all'>('pending');
  const [processingId, setProcessingId] = useState<string>('');

  // Check if user has permission to approve applications
  const canApproveApplications = hasPermission(adminUser, 'approve_applications') || hasPermission(adminUser, '*');

  useEffect(() => {
    loadApplications();
  }, [filter]);

  const loadApplications = async () => {
    try {
      setLoading(true);
      const applicationsData = filter === 'pending' 
        ? await getPendingApplications() 
        : await getAllApplications();
      setApplications(applicationsData);
    } catch (error) {
      console.error('Error loading applications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (applicationId: string) => {
    if (!canApproveApplications) {
      alert('You do not have permission to approve applications.');
      return;
    }

    setProcessingId(applicationId);
    try {
      await approveArtistApplication(applicationId, user.uid);
      await loadApplications();
      alert('Application approved successfully!');
    } catch (error) {
      console.error('Error approving application:', error);
      alert('Failed to approve application. Please try again.');
    } finally {
      setProcessingId('');
    }
  };

  const handleReject = async (applicationId: string) => {
    if (!canApproveApplications) {
      alert('You do not have permission to reject applications.');
      return;
    }

    const reason = prompt('Please provide a reason for rejection (optional):');
    if (reason === null) return; // User cancelled

    setProcessingId(applicationId);
    try {
      await rejectArtistApplication(applicationId, user.uid, reason || 'No reason provided');
      await loadApplications();
      alert('Application rejected.');
    } catch (error) {
      console.error('Error rejecting application:', error);
      alert('Failed to reject application. Please try again.');
    } finally {
      setProcessingId('');
    }
  };

  if (!canApproveApplications) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">You do not have permission to view artist applications.</p>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className={cx(editorialNew.className, "text-2xl font-bold")}>
          Artist Applications
        </h1>
        
        <div className="flex space-x-2">
          <button
            onClick={() => setFilter('pending')}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              filter === 'pending' 
                ? 'bg-black text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Pending ({applications.filter(a => a.status === 'pending').length})
          </button>
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              filter === 'all' 
                ? 'bg-black text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            All Applications
          </button>
        </div>
      </div>

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto mb-4"></div>
          <p className="text-gray-600">Loading applications...</p>
        </div>
      ) : applications.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-600">
            {filter === 'pending' ? 'No pending applications.' : 'No applications found.'}
          </p>
        </div>
      ) : (
        <div className="grid gap-6">
          {applications.map((application) => (
            <ApplicationCard
              key={application.id}
              application={application}
              onApprove={() => handleApprove(application.id)}
              onReject={() => handleReject(application.id)}
              processing={processingId === application.id}
            />
          ))}
        </div>
      )}
    </div>
  );
}

function ApplicationCard({ 
  application, 
  onApprove, 
  onReject, 
  processing 
}: { 
  application: ArtistApplication;
  onApprove: () => void;
  onReject: () => void;
  processing: boolean;
}) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-lg font-semibold">
            {application.firstName} {application.lastName}
          </h3>
          {application.businessName && (
            <p className="text-gray-600">{application.businessName}</p>
          )}
          <p className="text-sm text-gray-500">{application.email}</p>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(application.status)}`}>
            {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
          </span>
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-4 mb-4">
        <div>
          <h4 className="font-medium text-sm text-gray-700 mb-2">Art Details</h4>
          <p className="text-sm"><strong>Style:</strong> {application.artStyle}</p>
          <p className="text-sm"><strong>Mediums:</strong> {application.artMediums.join(', ')}</p>
          <p className="text-sm"><strong>Experience:</strong> {application.experience}</p>
        </div>
        
        <div>
          <h4 className="font-medium text-sm text-gray-700 mb-2">Business Info</h4>
          <p className="text-sm"><strong>Sales Channels:</strong> {application.currentSalesChannels.join(', ')}</p>
          <p className="text-sm"><strong>Monthly Revenue:</strong> {application.monthlyRevenue}</p>
          <p className="text-sm"><strong>Can Ship:</strong> {application.shippingCapability ? 'Yes' : 'No'}</p>
        </div>
      </div>

      <div className="mb-4">
        <h4 className="font-medium text-sm text-gray-700 mb-2">Application Details</h4>
        <div className="space-y-2">
          <div>
            <p className="text-xs text-gray-500 font-medium">Why join Milo's Menagerie?</p>
            <p className="text-sm text-gray-700">{application.motivation}</p>
          </div>
          <div>
            <p className="text-xs text-gray-500 font-medium">What makes your art unique?</p>
            <p className="text-sm text-gray-700">{application.uniqueValue}</p>
          </div>
        </div>
      </div>

      {application.portfolioLinks && application.portfolioLinks.length > 0 && (
        <div className="mb-4">
          <h4 className="font-medium text-sm text-gray-700 mb-2">Portfolio Links</h4>
          <div className="space-y-1">
            {application.portfolioLinks.filter(link => link.trim()).map((link, index) => (
              <a
                key={index}
                href={link}
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-blue-600 hover:text-blue-800 underline block"
              >
                {link}
              </a>
            ))}
          </div>
        </div>
      )}

      <div className="flex justify-between items-center pt-4 border-t">
        <div className="text-xs text-gray-500">
          Submitted: {application.submittedAt?.toDate?.()?.toLocaleDateString() || 'Recently'}
          {application.reviewedAt && (
            <span className="ml-4">
              Reviewed: {application.reviewedAt?.toDate?.()?.toLocaleDateString()}
            </span>
          )}
        </div>

        {application.status === 'pending' && (
          <div className="flex space-x-2">
            <button
              onClick={onReject}
              disabled={processing}
              className="px-4 py-2 bg-red-500 text-white text-sm rounded hover:bg-red-600 disabled:opacity-50"
            >
              {processing ? 'Processing...' : 'Reject'}
            </button>
            <button
              onClick={onApprove}
              disabled={processing}
              className="px-4 py-2 bg-green-500 text-white text-sm rounded hover:bg-green-600 disabled:opacity-50"
            >
              {processing ? 'Processing...' : 'Approve'}
            </button>
          </div>
        )}

        {application.status === 'rejected' && application.rejectionReason && (
          <div className="text-xs text-red-600">
            <strong>Rejection reason:</strong> {application.rejectionReason}
          </div>
        )}
      </div>
    </div>
  );
}
