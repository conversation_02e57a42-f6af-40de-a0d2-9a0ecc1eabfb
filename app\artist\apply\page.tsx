'use client';

import { useState, useEffect } from 'react';
import { onAuthStateChanged, User, GoogleAuthProvider, signInWithPopup, signOut } from 'firebase/auth';
import { auth } from '@/lib/firebase/config';
import { 
  getArtistByFirebaseUid, 
  submitArtistApplication,
  ArtistApplication,
  Artist
} from '@/lib/firebase/artists';
import { isUserAdmin } from '@/lib/auth/admin-firestore';
import ArtistApplicationForm from '@/components/artist/ArtistApplicationForm';
import { helvetica, editorialNew } from '@/app/fonts';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import cx from 'classnames';

export default function ArtistApplicationPage() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [checkingStatus, setCheckingStatus] = useState(false);
  const [userStatus, setUserStatus] = useState<'new' | 'pending' | 'approved' | 'rejected' | 'admin'>('new');
  const [existingArtist, setExistingArtist] = useState<Artist | null>(null);
  const [existingApplication, setExistingApplication] = useState<ArtistApplication | null>(null);
  const [applicationSubmitted, setApplicationSubmitted] = useState(false);
  const [applicationId, setApplicationId] = useState<string>('');

  // Function to check user's application status
  const checkUserStatus = async (user: User) => {
    setCheckingStatus(true);
    try {
      // First check if user is already an admin (any type of admin)
      const adminUser = await isUserAdmin(user);
      if (adminUser) {
        if (adminUser.role === 'artist') {
          // They're an artist admin, get their artist profile
          const artist = await getArtistByFirebaseUid(user.uid);
          if (artist) {
            setExistingArtist(artist);
            setUserStatus('approved');
            return;
          }
        } else {
          // They're a regular admin (super_admin, admin, moderator)
          setUserStatus('admin');
          return;
        }
      }

      // Check if user is already an approved artist (backup check)
      const artist = await getArtistByFirebaseUid(user.uid);
      if (artist) {
        setExistingArtist(artist);
        setUserStatus('approved');
        return;
      }

      // Check if user has pending/rejected application
      const applicationsRef = collection(db, 'artist_applications');
      const q = query(applicationsRef, where('firebaseUid', '==', user.uid));
      const querySnapshot = await getDocs(q);
      
      if (!querySnapshot.empty) {
        const applicationDoc = querySnapshot.docs[0];
        const application = { id: applicationDoc.id, ...applicationDoc.data() } as ArtistApplication;
        setExistingApplication(application);
        setUserStatus(application.status);
      } else {
        setUserStatus('new');
      }
    } catch (error) {
      console.error('Error checking user status:', error);
      setUserStatus('new');
    } finally {
      setCheckingStatus(false);
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setUser(user);
      
      if (user) {
        await checkUserStatus(user);
      }
      
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const handleGoogleSignIn = async () => {
    try {
      setLoading(true);
      const provider = new GoogleAuthProvider();
      // Force account selection every time
      provider.setCustomParameters({
        prompt: 'select_account'
      });
      await signInWithPopup(auth, provider);
    } catch (error) {
      console.error('Error signing in with Google:', error);
      alert('Failed to sign in with Google. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut(auth);
      setUser(null);
      setUserStatus('new');
      setExistingArtist(null);
      setExistingApplication(null);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handleApplicationSubmit = async (applicationData: Omit<ArtistApplication, 'id' | 'status' | 'submittedAt'>) => {
    if (!user) return;
    
    try {
      const applicationId = await submitArtistApplication({
        ...applicationData,
        firebaseUid: user.uid,
      });
      setApplicationId(applicationId);
      setApplicationSubmitted(true);
    } catch (error) {
      console.error('Error submitting application:', error);
      alert('Failed to submit application. Please try again.');
    }
  };

  if (loading || checkingStatus) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // If user is already an admin (but not an artist)
  if (userStatus === 'admin') {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-2xl mx-auto px-4">
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
              </svg>
            </div>
            <h1 className={cx(editorialNew.className, "text-3xl font-bold mb-4")}>
              You're Already an Admin!
            </h1>
            <p className="text-gray-600 mb-6">
              You're currently logged in as an administrator of Milo's Menagerie. 
              Admin accounts cannot apply to become artists through this form.
            </p>
            <p className="text-gray-600 mb-6">
              If you'd like to become an artist on the platform, please contact a super admin 
              to discuss your request.
            </p>
            <div className="space-y-4">
              <a
                href="/admin"
                className="inline-block bg-black text-white px-6 py-3 rounded-md hover:bg-gray-800 transition-colors"
              >
                Go to Admin Dashboard
              </a>
              <div>
                <a
                  href="/artist"
                  className="text-gray-500 hover:text-gray-700 underline"
                >
                  View Our Artists
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If user is already an approved artist
  if (userStatus === 'approved' && existingArtist) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-2xl mx-auto px-4">
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <h1 className={cx(editorialNew.className, "text-3xl font-bold mb-4")}>
              Welcome Back!
            </h1>
            <p className="text-gray-600 mb-6">
              You're already an approved artist with Milo's Menagerie. 
            </p>
            <div className="space-y-4">
              <a
                href="/dashboard"
                className="inline-block bg-black text-white px-6 py-3 rounded-md hover:bg-gray-800 transition-colors"
              >
                Go to Artist Dashboard
              </a>
              <div>
                <a
                  href="/artist"
                  className="text-gray-500 hover:text-gray-700 underline"
                >
                  View All Artists
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If user has a pending application
  if (userStatus === 'pending' && existingApplication) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-2xl mx-auto px-4">
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <h1 className={cx(editorialNew.className, "text-3xl font-bold mb-4")}>
              Application Under Review
            </h1>
            <p className="text-gray-600 mb-6">
              You've already submitted an application to join Milo's Menagerie. 
              We're currently reviewing your submission and will email you with updates.
            </p>
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <p className="text-sm text-gray-600">
                <strong>Application ID:</strong> {existingApplication.id}
              </p>
              <p className="text-sm text-gray-600">
                <strong>Submitted:</strong> {existingApplication.submittedAt?.toDate?.()?.toLocaleDateString() || 'Recently'}
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Applications are typically reviewed within 3-5 business days.
              </p>
            </div>
            <div className="space-y-4">
              <a
                href="/artist"
                className="inline-block bg-black text-white px-6 py-3 rounded-md hover:bg-gray-800 transition-colors"
              >
                View Our Artists
              </a>
              <div>
                <a
                  href="/"
                  className="text-gray-500 hover:text-gray-700 underline"
                >
                  Return to Homepage
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If user has a rejected application
  if (userStatus === 'rejected' && existingApplication) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-2xl mx-auto px-4">
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </div>
            <h1 className={cx(editorialNew.className, "text-3xl font-bold mb-4")}>
              Application Not Approved
            </h1>
            <p className="text-gray-600 mb-4">
              Thank you for your interest in joining Milo's Menagerie. Unfortunately, 
              your application was not approved at this time.
            </p>
            {existingApplication.rejectionReason && (
              <div className="bg-red-50 rounded-lg p-4 mb-6 text-left">
                <p className="text-sm text-gray-700">
                  <strong>Feedback:</strong> {existingApplication.rejectionReason}
                </p>
              </div>
            )}
            <p className="text-gray-600 mb-6">
              You're welcome to reapply in the future as your work and experience develop.
            </p>
            <div className="space-y-4">
              <button
                onClick={async () => {
                  // Allow them to submit a new application
                  setUserStatus('new');
                  setExistingApplication(null);
                }}
                className="inline-block bg-black text-white px-6 py-3 rounded-md hover:bg-gray-800 transition-colors"
              >
                Submit New Application
              </button>
              <div>
                <a
                  href="/artist"
                  className="text-gray-500 hover:text-gray-700 underline"
                >
                  View Our Artists
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If application was submitted
  if (applicationSubmitted) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-2xl mx-auto px-4">
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
              </svg>
            </div>
            <h1 className={cx(editorialNew.className, "text-3xl font-bold mb-4")}>
              Application Submitted!
            </h1>
            <p className="text-gray-600 mb-6">
              Thank you for your interest in joining Milo's Menagerie! We've received your application 
              and will review it within 3-5 business days.
            </p>
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <p className="text-sm text-gray-600">
                <strong>Application ID:</strong> {applicationId}
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Please save this ID for your records. We'll email you at {user?.email} with updates on your application status.
              </p>
            </div>
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">What happens next?</h3>
                <ul className="text-sm text-gray-600 text-left space-y-1">
                  <li>• Our team will review your application and portfolio</li>
                  <li>• We'll check that your art aligns with our brand values</li>
                  <li>• You'll receive an email with our decision within 3-5 business days</li>
                  <li>• If approved, we'll guide you through the onboarding process</li>
                </ul>
              </div>
              <a
                href="/"
                className="inline-block bg-black text-white px-6 py-3 rounded-md hover:bg-gray-800 transition-colors"
              >
                Return to Homepage
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If user is not signed in
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4">
          {/* Hero Section */}
          <div className="text-center mb-12">
            <h1 className={cx(editorialNew.className, "text-4xl md:text-5xl font-bold mb-6")}>
              Join Milo's Menagerie
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Become part of our curated marketplace and share your art with a community 
              that values creativity, quality, and authentic artistic expression.
            </p>
          </div>

          {/* Benefits Grid */}
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              </div>
              <h3 className="font-semibold text-lg mb-2">Easy Setup</h3>
              <p className="text-gray-600">
                Simple application process and intuitive dashboard to manage your art business.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
              </div>
              <h3 className="font-semibold text-lg mb-2">Fair Compensation</h3>
              <p className="text-gray-600">
                Competitive commission rates with direct payments and transparent fee structure.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
              </div>
              <h3 className="font-semibold text-lg mb-2">Supportive Community</h3>
              <p className="text-gray-600">
                Join a network of artists who support each other's growth and success.
              </p>
            </div>
          </div>

          {/* Sign In Section */}
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <h2 className={cx(editorialNew.className, "text-2xl font-bold mb-4")}>
              Ready to Get Started?
            </h2>
            <p className="text-gray-600 mb-6">
              Sign in with Google to begin your application. This helps us verify your identity 
              and streamline the process.
            </p>
            <button
              onClick={handleGoogleSignIn}
              disabled={loading}
              className="inline-flex items-center px-6 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50"
            >
              <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
                <path fill="#4285f4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34a853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#fbbc05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#ea4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              {loading ? 'Signing in...' : 'Continue with Google'}
            </button>
            <p className="text-xs text-gray-500 mt-4">
              By continuing, you agree to our Terms of Service and Privacy Policy
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Main application form
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className={cx(editorialNew.className, "text-3xl font-bold mb-4")}>
            Artist Application
          </h1>
          <div className="flex items-center justify-center space-x-4 mb-4">
            <p className="text-gray-600">
              Hello, {user.displayName || user.email}!
            </p>
            <button
              onClick={handleSignOut}
              className="text-sm text-blue-600 hover:text-blue-800 underline"
            >
              Use different account
            </button>
          </div>
          <p className="text-gray-600">
            Please fill out this application to join our marketplace.
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-8">
          <ArtistApplicationForm user={user} onSubmit={handleApplicationSubmit} />
        </div>
      </div>
    </div>
  );
}
