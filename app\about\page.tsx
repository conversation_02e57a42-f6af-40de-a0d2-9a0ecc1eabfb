'use client';

import cx from 'classnames';
import { helvetica, editorialNew } from '../fonts';
import React, { useState, useRef } from 'react';
import useIntersectionObserver from '@/lib/hooks/use-intersection-observer';
import Image from 'next/image';
import Link from 'next/link';

export default function About() {
  // Refs for sections to observe
  const heroRef = useRef<HTMLDivElement>(null);
  const teamRef = useRef<HTMLDivElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);

  // Intersection observers
  const heroVisible = useIntersectionObserver(heroRef, { threshold: 0.2, freezeOnceVisible: true });
  const teamVisible = useIntersectionObserver(teamRef, { threshold: 0.2, freezeOnceVisible: true });
  const ctaVisible = useIntersectionObserver(ctaRef, { threshold: 0.2, freezeOnceVisible: true });

  return (
    <div className={cx(helvetica.variable, editorialNew.variable, 'min-h-screen flex flex-col bg-white text-black font-helvetica')}>
      {/* Hero Section */}
      <section ref={heroRef} className="w-full py-16 md:py-20">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            {/* Left side - Text */}
            <div>
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-editorial font-normal leading-[0.9] mb-6 text-black">
                Meet the Expedition Team
              </h1>
              <p className="text-lg md:text-xl text-gray-600 font-helvetica font-light">
                The creative minds behind Milo's Menagerie, bringing nature's wonders to life through 3D printing and boundless imagination.
              </p>
            </div>
            
            {/* Right side - Family Photo */}
            <div className="flex justify-center lg:justify-end">
              <Image
                src="/family.jpeg"
                alt="The Milo's Menagerie Family"
                width={600}
                height={400}
                className="rounded-lg shadow-lg object-cover w-full max-w-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section ref={teamRef} className="py-16 md:py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="space-y-16">
            
            {/* Enya */}
            <div className="text-center max-w-3xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-editorial font-normal mb-2 text-black">
                <span className="text-5xl">E</span>nya
              </h2>
              <h3 className="text-xl text-gray-600 font-helvetica font-light mb-4">
                Lead Explorer & Designer
              </h3>
              <p className="text-base md:text-lg font-helvetica font-light leading-relaxed text-gray-700">
                The creative force behind Milo's Menagerie, Enya combines her passion for design with a love of nature to bring each creature to life. When she's not designing the next amazing print, you'll find her exploring the great outdoors with her family, always on the lookout for new inspiration.
              </p>
            </div>

            {/* Milo */}
            <div className="text-center max-w-3xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-editorial font-normal mb-2 text-black">
                <span className="text-5xl">M</span>ilo
              </h2>
              <h3 className="text-xl text-gray-600 font-helvetica font-light mb-4">
                Chief Adventure Officer
              </h3>
              <p className="text-base md:text-lg font-helvetica font-light leading-relaxed text-gray-700">
                Our youngest team member and the inspiration behind it all! Milo's endless curiosity about animals and boundless imagination help guide our designs. If it passes the Milo test for fun and wonder, you know it's Menagerie-approved.
              </p>
            </div>

            {/* Jordan */}
            <div className="text-center max-w-3xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-editorial font-normal mb-2 text-black">
                <span className="text-5xl">J</span>ordan
              </h2>
              <h3 className="text-xl text-gray-600 font-helvetica font-light mb-4">
                Master Builder & Workshop Wizard
              </h3>
              <p className="text-base md:text-lg font-helvetica font-light leading-relaxed text-gray-700">
                With his carpenter's eye for detail and craftsmanship, Jordan ensures every print meets our high standards for quality and durability. He's also our resident problem-solver, always finding creative ways to bring even the wildest design ideas to reality.
              </p>
            </div>

          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section ref={ctaRef} className="py-16 md:py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 md:px-6 text-center">
          <h2 className="text-3xl md:text-4xl font-editorial font-normal mb-6 text-black">
            Ready to Start Your Adventure?
          </h2>
          <p className="text-base md:text-xl mb-8 font-helvetica font-light max-w-3xl mx-auto text-gray-700">
            Join us in exploring the wonders of nature through beautifully crafted 3D printed creations. Every piece tells a story and sparks imagination.
          </p>
          <Link 
            href="/contact" 
            className="inline-block bg-black text-white font-helvetica font-medium py-3 px-8 text-lg rounded-lg hover:bg-gray-800 transition-colors"
          >
            Get in Touch
          </Link>
        </div>
      </section>
    </div>
  );
}
