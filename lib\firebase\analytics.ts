import { db } from './config';
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  increment, 
  serverTimestamp, 
  query, 
  where, 
  orderBy, 
  getDocs,
  getDoc,
  setDoc
} from 'firebase/firestore';

// Analytics event types
export type AnalyticsEventType = 
  | 'product_view' 
  | 'product_click' 
  | 'add_to_cart' 
  | 'share_click' 
  | 'image_view' 
  | 'page_exit'
  | 'purchase';

// Individual analytics event
export interface AnalyticsEvent {
  id?: string;
  productId: string;
  eventType: AnalyticsEventType;
  timestamp: any; // Firestore Timestamp
  sessionId: string;
  userId?: string; // If user is logged in
  duration?: number; // For view events, time spent in milliseconds
  metadata?: {
    imageIndex?: number; // Which image was viewed
    price?: number; // Product price at time of event
    artistId?: string; // Product's artist
    referrer?: string; // Where user came from
    userAgent?: string; // Browser info
    deviceType?: 'mobile' | 'tablet' | 'desktop';
  };
}

// Aggregated analytics for a product
export interface ProductAnalytics {
  productId: string;
  artistId?: string;
  totalViews: number;
  totalClicks: number;
  totalAddToCarts: number;
  totalShares: number;
  totalPurchases: number;
  averageViewDuration: number; // in milliseconds
  uniqueVisitors: number;
  conversionRate: number; // (purchases / views) * 100
  lastUpdated: any; // Firestore Timestamp
  dailyStats: {
    [date: string]: {
      views: number;
      clicks: number;
      addToCarts: number;
      shares: number;
      purchases: number;
      uniqueVisitors: number;
    };
  };
}

/**
 * Track an analytics event
 */
export const trackAnalyticsEvent = async (event: Omit<AnalyticsEvent, 'id' | 'timestamp'>): Promise<void> => {
  try {
    console.log('🔍 Tracking analytics event:', event.eventType, 'for product:', event.productId);
    
    // Filter out undefined values to avoid Firebase errors
    const cleanEvent: any = {
      productId: event.productId,
      eventType: event.eventType,
      sessionId: event.sessionId,
      timestamp: serverTimestamp()
    };

    // Only add userId if it's defined
    if (event.userId) {
      cleanEvent.userId = event.userId;
    }

    // Only add duration if it's defined
    if (event.duration !== undefined) {
      cleanEvent.duration = event.duration;
    }

    // Only add metadata if it exists and has defined values
    if (event.metadata) {
      const cleanMetadata: any = {};
      if (event.metadata.imageIndex !== undefined) cleanMetadata.imageIndex = event.metadata.imageIndex;
      if (event.metadata.price !== undefined) cleanMetadata.price = event.metadata.price;
      if (event.metadata.artistId) cleanMetadata.artistId = event.metadata.artistId;
      if (event.metadata.referrer) cleanMetadata.referrer = event.metadata.referrer;
      if (event.metadata.userAgent) cleanMetadata.userAgent = event.metadata.userAgent;
      if (event.metadata.deviceType) cleanMetadata.deviceType = event.metadata.deviceType;
      
      // Only add metadata if it has any properties
      if (Object.keys(cleanMetadata).length > 0) {
        cleanEvent.metadata = cleanMetadata;
      }
    }

    // Add event to events collection
    await addDoc(collection(db, 'analytics_events'), cleanEvent);
    console.log('✅ Analytics event saved successfully:', event.eventType);

    // Update aggregated statistics
    await updateProductAnalytics(event);
    console.log('✅ Product analytics updated successfully');
  } catch (error) {
    console.error('❌ Error tracking analytics event:', error);
    // Don't throw error to avoid breaking user experience
  }
};

/**
 * Update aggregated product analytics
 */
const updateProductAnalytics = async (event: Omit<AnalyticsEvent, 'id' | 'timestamp'>): Promise<void> => {
  try {
    console.log('🔄 Updating product analytics for:', event.productId);
    console.log('🔄 Event metadata:', event.metadata);
    
    const analyticsRef = doc(db, 'product_analytics', event.productId);
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

    // Prepare update data
    const updateData: any = {
      productId: event.productId,
      lastUpdated: serverTimestamp()
    };

    // Add artist ID if available
    if (event.metadata?.artistId) {
      updateData.artistId = event.metadata.artistId;
      console.log('✅ Adding artistId to analytics:', event.metadata.artistId);
    } else {
      console.log('❌ No artistId in event metadata');
    }

    // Update counters based on event type
    switch (event.eventType) {
      case 'product_view':
        updateData.totalViews = increment(1);
        updateData[`dailyStats.${today}.views`] = increment(1);
        break;
      case 'product_click':
        updateData.totalClicks = increment(1);
        updateData[`dailyStats.${today}.clicks`] = increment(1);
        break;
      case 'add_to_cart':
        updateData.totalAddToCarts = increment(1);
        updateData[`dailyStats.${today}.addToCarts`] = increment(1);
        break;
      case 'share_click':
        updateData.totalShares = increment(1);
        updateData[`dailyStats.${today}.shares`] = increment(1);
        break;
      case 'purchase':
        updateData.totalPurchases = increment(1);
        updateData[`dailyStats.${today}.purchases`] = increment(1);
        break;
    }

    // Handle view duration for product_view events
    if (event.eventType === 'product_view' && event.duration) {
      // We'll calculate average duration using a more complex approach
      // For now, just store the duration and calculate average when querying
      updateData.lastViewDuration = event.duration;
    }

    await setDoc(analyticsRef, updateData, { merge: true });
  } catch (error) {
    console.error('Error updating product analytics:', error);
  }
};

/**
 * Get analytics for a specific product
 */
export const getProductAnalytics = async (productId: string): Promise<ProductAnalytics | null> => {
  try {
    const analyticsDoc = await getDoc(doc(db, 'product_analytics', productId));
    
    if (!analyticsDoc.exists()) {
      return null;
    }

    const data = analyticsDoc.data();
    
    // Calculate conversion rate
    const conversionRate = data.totalViews > 0 ? (data.totalPurchases / data.totalViews) * 100 : 0;
    
    // Calculate average view duration from recent events
    const averageViewDuration = await calculateAverageViewDuration(productId);

    return {
      productId,
      artistId: data.artistId,
      totalViews: data.totalViews || 0,
      totalClicks: data.totalClicks || 0,
      totalAddToCarts: data.totalAddToCarts || 0,
      totalShares: data.totalShares || 0,
      totalPurchases: data.totalPurchases || 0,
      averageViewDuration,
      uniqueVisitors: data.uniqueVisitors || 0,
      conversionRate,
      lastUpdated: data.lastUpdated,
      dailyStats: data.dailyStats || {}
    };
  } catch (error) {
    console.error('Error getting product analytics:', error);
    return null;
  }
};

/**
 * Get analytics for all products by an artist
 */
export const getArtistProductAnalytics = async (artistId: string): Promise<ProductAnalytics[]> => {
  try {
    console.log('🔍 Getting analytics for artist:', artistId);
    
    // First, let's check if there are any documents in product_analytics at all
    const allAnalyticsQuery = query(collection(db, 'product_analytics'));
    const allSnapshot = await getDocs(allAnalyticsQuery);
    console.log('🔍 Total documents in product_analytics collection:', allSnapshot.docs.length);
    
    // Log all artistIds in the collection for debugging
    allSnapshot.docs.forEach(doc => {
      console.log('🔍 Found analytics doc:', doc.id, 'artistId:', doc.data().artistId);
    });
    
    const analyticsQuery = query(
      collection(db, 'product_analytics'),
      where('artistId', '==', artistId),
      orderBy('totalViews', 'desc')
    );

    console.log('🔍 Executing analytics query for artistId:', artistId);
    const querySnapshot = await getDocs(analyticsQuery);
    console.log('🔍 Query returned', querySnapshot.docs.length, 'documents');
    
    const analytics: ProductAnalytics[] = [];

    for (const doc of querySnapshot.docs) {
      console.log('🔍 Processing analytics doc:', doc.id, doc.data());
      const data = doc.data();
      const productAnalytics = await getProductAnalytics(doc.id);
      if (productAnalytics) {
        analytics.push(productAnalytics);
        console.log('✅ Added analytics for product:', doc.id);
      } else {
        console.log('❌ No analytics returned for product:', doc.id);
      }
    }

    console.log('🔍 Final analytics array:', analytics);
    return analytics;
  } catch (error) {
    console.error('❌ Error getting artist product analytics:', error);
    return [];
  }
};

/**
 * Get analytics for all products (admin view)
 */
export const getAllProductAnalytics = async (): Promise<ProductAnalytics[]> => {
  try {
    const analyticsQuery = query(
      collection(db, 'product_analytics'),
      orderBy('totalViews', 'desc')
    );

    const querySnapshot = await getDocs(analyticsQuery);
    const analytics: ProductAnalytics[] = [];

    for (const doc of querySnapshot.docs) {
      const productAnalytics = await getProductAnalytics(doc.id);
      if (productAnalytics) {
        analytics.push(productAnalytics);
      }
    }

    return analytics;
  } catch (error) {
    console.error('Error getting all product analytics:', error);
    return [];
  }
};

/**
 * Calculate average view duration for a product from recent events
 */
const calculateAverageViewDuration = async (productId: string): Promise<number> => {
  try {
    const eventsQuery = query(
      collection(db, 'analytics_events'),
      where('productId', '==', productId),
      where('eventType', '==', 'product_view'),
      orderBy('timestamp', 'desc')
    );

    const querySnapshot = await getDocs(eventsQuery);
    const durations: number[] = [];

    querySnapshot.docs.forEach(doc => {
      const data = doc.data();
      if (data.duration && data.duration > 0) {
        durations.push(data.duration);
      }
    });

    if (durations.length === 0) return 0;

    const sum = durations.reduce((acc, duration) => acc + duration, 0);
    return Math.round(sum / durations.length);
  } catch (error) {
    console.error('Error calculating average view duration:', error);
    return 0;
  }
};

/**
 * Generate a session ID for tracking user sessions
 */
export const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Get device type based on user agent
 */
export const getDeviceType = (): 'mobile' | 'tablet' | 'desktop' => {
  if (typeof window === 'undefined') return 'desktop';
  
  const userAgent = window.navigator.userAgent;
  
  if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
    return 'tablet';
  }
  
  if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
    return 'mobile';
  }
  
  return 'desktop';
};
