'use client';

import { useState, useEffect } from 'react';
import ProductCard from './product-card';
import { Product, getAllProducts } from '@/lib/firebase/products';

interface ProductGridProps {
  onProductRequest?: (productId: string) => void;
}

export default function ProductGrid({ onProductRequest }: ProductGridProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      const productsData = await getAllProducts();
      setProducts(productsData);
    } catch (error) {
      console.error('Error loading products:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 auto-rows-max">
        {[...Array(8)].map((_, i) => (
          <div key={i} className="bg-gray-200 animate-pulse rounded-lg h-72"></div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 auto-rows-max">
      {products.map((product) => (
        <ProductCard
          key={product.id}
          id={product.id!}
          title={product.title}
          description={product.description}
          images={product.images}
          price={product.price}
          stock={product.stock}
          size={product.size}
          onRequest={onProductRequest}
          artistId={product.artistId}
        />
      ))}
    </div>
  );
}




