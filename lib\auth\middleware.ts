import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { AdminUser } from './admin-firestore';

export interface AdminSession {
  uid: string;
  email: string;
  role: string;
  permissions: string[];
  isAdmin: boolean;
  iat: number;
  exp: number;
}

export async function verifyAdminAuth(request: NextRequest): Promise<AdminSession | null> {
  try {
    // Get token from cookie or Authorization header
    const token = request.cookies.get('admin-token')?.value || 
                 request.headers.get('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return null;
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as AdminSession;
    
    // Check if user has admin permissions
    if (!decoded.isAdmin) {
      return null;
    }

    return decoded;
  } catch (error) {
    console.error('Admin auth verification failed:', error);
    return null;
  }
}

export function createAdminSession(adminUser: AdminUser): string {
  const payload: Omit<AdminSession, 'iat' | 'exp'> = {
    uid: adminUser.uid,
    email: adminUser.email,
    role: adminUser.role,
    permissions: adminUser.permissions,
    isAdmin: true
  };

  return jwt.sign(payload, process.env.JWT_SECRET!, {
    expiresIn: '24h' // Admin sessions expire in 24 hours
  });
}

export function requireAdminAuth() {
  return async (request: NextRequest) => {
    const adminSession = await verifyAdminAuth(request);
    
    if (!adminSession) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    return null; // Continue with the request
  };
}

/**
 * Check if admin has specific permission
 */
export function hasPermission(session: AdminSession, permission: string): boolean {
  // Super admin or wildcard permission
  if (session.permissions.includes('*')) {
    return true;
  }
  
  return session.permissions.includes(permission);
}
