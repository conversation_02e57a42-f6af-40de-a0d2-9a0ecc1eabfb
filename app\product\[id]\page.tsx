'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowLeft, ShoppingCart } from 'lucide-react';
import { helvetica, editorialNew } from '@/app/fonts';
import { Product, getProduct } from '@/lib/firebase/products';
import { ArtistProduct, getArtistProduct } from '@/lib/firebase/artist-products';
import { useCart } from '@/lib/context/cart-context';
import { Share2 } from 'lucide-react';
import cx from 'classnames';
import { useProductAnalytics } from '@/lib/hooks/use-analytics';
import { onAuthStateChanged, User as FirebaseUser } from 'firebase/auth';
import { auth } from '@/lib/firebase/config';
import { isUserAdmin, AdminUser } from '@/lib/auth/admin-firestore';
import ProductVariationSelector from '@/components/ui/ProductVariationSelector';

export default function ProductPage() {
  const params = useParams();
  const router = useRouter();
  const productId = params.id as string;

  const [product, setProduct] = useState<Product | ArtistProduct | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [activeTab, setActiveTab] = useState<'images' | 'videos'>('images');
  const [isAdding, setIsAdding] = useState(false);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [selectedVariations, setSelectedVariations] = useState<Record<string, string>>({});
  const [variationPriceModifier, setVariationPriceModifier] = useState(0);
  const [variationText, setVariationText] = useState('');

  const { dispatch } = useCart();
  
  // Analytics tracking - artistId will be undefined for regular products
  // Helper function to get the artist ID from the product
  const getArtistId = (product: Product | ArtistProduct | null): string | undefined => {
    if (!product) return undefined;
    
    // Check if it's an ArtistProduct
    if ('artistId' in product) {
      return product.artistId;
    }
    
    // Check if it's a Product with artistId (from artist_products collection)
    if ((product as Product).artistId) {
      return (product as Product).artistId;
    }
    
    return undefined;
  };

  // Helper function to get the product title
  const getProductTitle = (product: Product | ArtistProduct | null): string => {
    if (!product) return '';
    
    // Check if it's an ArtistProduct (has 'name' field)
    if ('name' in product) {
      return product.name;
    }
    
    // It's a regular Product (has 'title' field)
    return (product as Product).title;
  };

  // Helper function to check if product has videos
  const hasVideos = (product: Product | ArtistProduct | null): boolean => {
    if (!product) return false;
    return 'videos' in product && Array.isArray((product as Product).videos) && (product as Product).videos!.length > 0;
  };

  // Helper function to get videos
  const getVideos = (product: Product | ArtistProduct | null): any[] => {
    if (!product || !('videos' in product)) return [];
    return (product as Product).videos || [];
  };

  // Helper function to get sorted images
  const getSortedImages = (product: Product | ArtistProduct | null): any[] => {
    if (!product?.images) return [];
    
    // Check if it's ArtistProduct (images don't have order)
    if ('artistId' in product) {
      return product.images.map((img, index) => ({ ...img, id: `img-${index}` }));
    }
    
    // Regular Product images with order
    return (product as Product).images.sort((a, b) => a.order - b.order);
  };

  const { trackEvent } = useProductAnalytics(
    productId, 
    getArtistId(product)
  );

  // Debug the artist ID
  useEffect(() => {
    if (product) {
      console.log('🔍 Product loaded:', product);
      console.log('🔍 Product artistId:', getArtistId(product));
      console.log('🔍 Is ArtistProduct?', 'artistId' in product);
      console.log('🔍 Product keys:', Object.keys(product));
    }
  }, [product]);

  // Authentication state management
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        // Check if user is an admin/artist
        const admin = await isUserAdmin(firebaseUser);
        setAdminUser(admin);
      } else {
        setAdminUser(null);
      }
    });

    return () => unsubscribe();
  }, []);

  useEffect(() => {
    if (productId) {
      loadProduct();
    }
  }, [productId]);

  const loadProduct = async () => {
    try {
      console.log('🔍 Starting to load product:', productId);
      
      // First try to get as an artist product
      let productData: Product | ArtistProduct | null = await getArtistProduct(productId);
      console.log('🔍 ArtistProduct result:', productData);
      
      // If not found, try the regular products collection
      if (!productData) {
        productData = await getProduct(productId);
        console.log('🔍 Regular Product result:', productData);
      }
      
      if (productData) {
        setProduct(productData);
        console.log('🔍 Final product set:', productData);
        console.log('🔍 Artist ID extracted:', getArtistId(productData));
        
        // Set default tab based on available media
        if (productData.images?.length > 0) {
          setActiveTab('images');
        } else if ('videos' in productData && (productData as Product).videos?.length > 0) {
          setActiveTab('videos');
        }
      } else {
        setError('Product not found');
      }
    } catch (err) {
      console.error('Error loading product:', err);
      setError('Failed to load product');
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = () => {
    if (!product) return;

    // Check for required variations
    const variations = (product as Product).variations || [];
    const requiredVariations = variations.filter(v => v.required);
    const hasAllRequiredVariations = requiredVariations.every(v => selectedVariations[v.id]);
    
    if (requiredVariations.length > 0 && !hasAllRequiredVariations) {
      // Don't add to cart if required variations are missing
      return;
    }

    // Check stock before adding
    const availableStock = (product as any).quantity !== undefined ? (product as any).quantity : product.stock;
    if (typeof availableStock === 'number' && availableStock === 0) {
      return; // Don't add if out of stock
    }

    setIsAdding(true);
    
    // Track add to cart event
    trackEvent('add_to_cart', {
      price: product.price + variationPriceModifier,
      imageIndex: currentImageIndex,
      variations: selectedVariations
    });
    
    dispatch({
      type: 'ADD_ITEM',
      payload: {
        id: product.id!,
        title: getProductTitle(product),
        description: product.description,
        imageUrl: product.images?.[0]?.url || '',
        price: product.price,
        maxStock: (product as any).quantity !== undefined ? (product as any).quantity : product.stock, // Include stock limit
        artistId: getArtistId(product), // Include artistId for order tracking
        variations: Object.keys(selectedVariations).length > 0 ? selectedVariations : undefined,
        variationText: variationText || undefined,
        priceModifier: variationPriceModifier !== 0 ? variationPriceModifier : undefined,
      },
    });

    setTimeout(() => {
      setIsAdding(false);
    }, 600);
  };

  const handleVariationSelectionChange = (selections: Record<string, string>) => {
    setSelectedVariations(selections);
    
    // Generate human-readable variation text
    if (!product) return;
    const variations = (product as Product).variations || [];
    const text = variations
      .filter(v => selections[v.id])
      .map(v => {
        const value = v.values.find(val => val.id === selections[v.id]);
        return `${v.name}: ${value?.value || ''}`;
      })
      .join(', ');
    setVariationText(text);
  };

  const handleVariationPriceChange = (priceModifier: number) => {
    setVariationPriceModifier(priceModifier);
  };

  const nextMedia = () => {
    if (activeTab === 'images' && sortedImages.length > 0) {
      setCurrentImageIndex((prev) => (prev + 1) % sortedImages.length);
    } else if (activeTab === 'videos' && hasVideos(product)) {
      const videos = getVideos(product);
      setCurrentVideoIndex((prev) => (prev + 1) % videos.length);
    }
  };

  const prevMedia = () => {
    if (activeTab === 'images' && sortedImages.length > 0) {
      setCurrentImageIndex((prev) => (prev - 1 + sortedImages.length) % sortedImages.length);
    } else if (activeTab === 'videos' && hasVideos(product)) {
      const videos = getVideos(product);
      setCurrentVideoIndex((prev) => (prev - 1 + videos.length) % videos.length);
    }
  };

  if (loading) {
    return (
      <div className={cx(helvetica.className, "min-h-screen bg-gray-50 flex items-center justify-center")}>
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
          <p className="mt-4 text-gray-600">Loading product...</p>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className={cx(helvetica.className, "min-h-screen bg-gray-50 flex items-center justify-center")}>
        <div className="text-center max-w-md mx-auto px-4">
          <h1 className={cx(editorialNew.className, "text-2xl mb-4")}>Product Not Found</h1>
          <p className="text-gray-600 mb-6">{error || 'The requested product could not be found.'}</p>
          <Link
            href="/"
            className="bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors inline-flex items-center"
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Menagerie
          </Link>
        </div>
      </div>
    );
  }

  const sortedImages = getSortedImages(product);
  const sortedVideos = getVideos(product).sort((a: any, b: any) => a.order - b.order);
  const currentImage = sortedImages[currentImageIndex];
  const currentVideo = sortedVideos[currentVideoIndex];

  return (
    <div className={cx(helvetica.className, "min-h-screen bg-gray-50")}>
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <Link
            href="/"
            className="inline-flex items-center text-gray-600 hover:text-black transition-colors"
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Menagerie
          </Link>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Media Section */}
          <div className="space-y-4">
            {/* Media Tabs */}
            {sortedImages.length > 0 && sortedVideos.length > 0 && (
              <div className="flex space-x-4 border-b">
                <button
                  onClick={() => setActiveTab('images')}
                  className={`pb-2 px-1 border-b-2 transition-colors ${activeTab === 'images'
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                >
                  Images ({sortedImages.length})
                </button>
                <button
                  onClick={() => setActiveTab('videos')}
                  className={`pb-2 px-1 border-b-2 transition-colors ${activeTab === 'videos'
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                >
                  Videos ({sortedVideos.length})
                </button>
              </div>
            )}

            {/* Media Display */}
            <div className="relative bg-white rounded-lg overflow-hidden">
              <div className="aspect-square relative">
                {activeTab === 'images' && currentImage ? (
                  <Image
                    src={currentImage.url}
                    alt={currentImage.alt || getProductTitle(product)}
                    fill
                    className="object-contain"
                  />
                ) : activeTab === 'videos' && currentVideo ? (
                  <video
                    src={currentVideo.url}
                    className="w-full h-full object-contain"
                    controls
                    poster={sortedImages[0]?.url}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-400">
                    No media available
                  </div>
                )}

                {/* Navigation Arrows */}
                {((activeTab === 'images' && sortedImages.length > 1) ||
                  (activeTab === 'videos' && sortedVideos.length > 1)) && (
                    <>
                      <button
                        onClick={prevMedia}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                      >
                        ←
                      </button>
                      <button
                        onClick={nextMedia}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                      >
                        →
                      </button>
                    </>
                  )}
              </div>

              {/* Media Indicators */}
              {activeTab === 'images' && sortedImages.length > 1 && (
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {sortedImages.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`w-3 h-3 rounded-full transition-colors ${index === currentImageIndex ? 'bg-white' : 'bg-white bg-opacity-50'
                        }`}
                    />
                  ))}
                </div>
              )}

              {activeTab === 'videos' && sortedVideos.length > 1 && (
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {sortedVideos.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentVideoIndex(index)}
                      className={`w-3 h-3 rounded-full transition-colors ${index === currentVideoIndex ? 'bg-white' : 'bg-white bg-opacity-50'
                        }`}
                    />
                  ))}
                </div>
              )}
            </div>

            {/* Thumbnail Grid */}
            {activeTab === 'images' && sortedImages.length > 1 && (
              <div className="grid grid-cols-4 gap-2">
                {sortedImages.map((image, index) => (
                  <button
                    key={image.id}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`aspect-square relative rounded overflow-hidden border-2 transition-colors ${index === currentImageIndex ? 'border-black' : 'border-gray-200 hover:border-gray-400'
                      }`}
                  >
                    <Image
                      src={image.url}
                      alt={image.alt || getProductTitle(product)}
                      fill
                      className="object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            <div>
              <h1 className={cx(editorialNew.className, "text-3xl md:text-4xl font-normal mb-4")}>
                {getProductTitle(product)}
              </h1>
              <p className="text-2xl font-medium mb-2">
                ${(product.price + variationPriceModifier).toFixed(2)}
                {variationPriceModifier !== 0 && (
                  <span className="text-sm text-gray-500 ml-2">
                    (Base: ${product.price.toFixed(2)} + ${variationPriceModifier.toFixed(2)})
                  </span>
                )}
              </p>
              {/* Stock Information */}
              {(typeof product.stock === 'number' || (product as any).quantity !== undefined) && (
                <div className={`text-sm font-medium mb-6 ${
                  ((product as any).quantity !== undefined ? (product as any).quantity : product.stock) === 0 
                    ? 'text-red-600' 
                    : ((product as any).quantity !== undefined ? (product as any).quantity : product.stock) <= 5 
                      ? 'text-orange-600' 
                      : 'text-green-600'
                }`}>
                  {((product as any).quantity !== undefined ? (product as any).quantity : product.stock) === 0 
                    ? '❌ Out of Stock' 
                    : ((product as any).quantity !== undefined ? (product as any).quantity : product.stock) <= 5 
                      ? `⚠️ Only ${(product as any).quantity !== undefined ? (product as any).quantity : product.stock} left in stock` 
                      : `✅ ${(product as any).quantity !== undefined ? (product as any).quantity : product.stock} in stock`
                  }
                </div>
              )}

              {/* Product Variations */}
              {(product as Product).variations && (product as Product).variations!.length > 0 && (
                <div className="mb-6">
                  <ProductVariationSelector
                    variations={(product as Product).variations!}
                    onSelectionChange={handleVariationSelectionChange}
                    onPriceChange={handleVariationPriceChange}
                  />
                </div>
              )}

              <p className="text-gray-600 leading-relaxed mb-8">
                {product.description}
              </p>
            </div>

            <div className="space-y-4">
              {/* Add to Cart button - Only show for non-authenticated users or regular customers */}
              {!adminUser && (
                <button 
                  onClick={handleAddToCart}
                  disabled={isAdding || ((product as any).quantity !== undefined ? (product as any).quantity : product.stock) === 0}
                  className={`w-full py-3 px-6 rounded-lg font-medium transition-colors ${
                    ((product as any).quantity !== undefined ? (product as any).quantity : product.stock) === 0
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : isAdding 
                        ? 'bg-green-600 text-white' 
                        : 'bg-black text-white hover:bg-gray-800'
                  }`}
                >
                  {((product as any).quantity !== undefined ? (product as any).quantity : product.stock) === 0 
                    ? 'Out of Stock' 
                    : isAdding 
                      ? 'Added to Cart!' 
                      : 'Add to Cart'
                  }
                </button>
              )}

              {/* Share Button */}
              <button
                onClick={() => {
                  // Track share click event
                  trackEvent('share_click', {
                    shareMethod: (navigator.share && typeof navigator.share === 'function') ? 'native' : 'clipboard'
                  });

                  const url = window.location.href;
                  const shareText = "This has your energy"; // Text without URL

                  if (navigator.share) {
                    // Native share API - let it handle the URL separately
                    navigator.share({
                      title: getProductTitle(product),
                      url: "This has your energy " + url, // URL as separate parameter
                    });
                  } else {
                    // Fallback - combine text and URL for clipboard
                    const fullShareText = `${shareText} ${url}`;
                    navigator.clipboard.writeText(fullShareText);
                    alert('Product link copied to clipboard!');
                  }
                }}
                className="w-full flex items-center justify-center space-x-2 py-3 px-6 border border-gray-300 rounded-lg font-medium hover:bg-gray-50 transition-colors"
              >
                <Share2 size={20} />
                <span>Share Product</span>
              </button>

            </div>

          </div>
        </div>
      </div>
    </div>
  );
}
