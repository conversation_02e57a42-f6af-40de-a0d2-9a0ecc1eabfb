import { 
  collection, 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  deleteDoc,
  query, 
  where, 
  orderBy, 
  getDocs,
  serverTimestamp,
  Timestamp 
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { db, storage } from './config';

// Artist Product Interface
export interface ArtistProduct {
  id: string;
  
  // Basic Product Info
  name: string;
  description: string;
  price: number;
  category: string; // Category ID instead of hardcoded options
  customCategory?: string; // Custom category name if not using predefined
  
  // Art Details
  medium: string;
  dimensions?: {
    width: number;
    height: number;
    depth?: number;
    unit: 'in' | 'cm';
  };
  weight?: {
    value: number;
    unit: 'lb' | 'kg' | 'oz' | 'g';
  };
  
  // Images
  images: {
    url: string;
    isPrimary: boolean;
    alt?: string;
  }[];
  
  // Inventory
  quantity: number;
  stock?: number; // Available inventory quantity (for compatibility with Product interface)
  
  // Product Variations
  variations?: {
    id: string;
    name: string; // e.g., "Metal Color", "Size", "Finish"
    values: {
      id: string;
      value: string; // e.g., "Gold", "Silver", "Bronze"
      priceModifier?: number; // Additional cost for this variation
      stockModifier?: number; // Stock specific to this variation
    }[];
    required: boolean;
  }[];
  isUnique: boolean; // True for one-of-a-kind pieces
  sku?: string;
  
  // Shipping
  shippingWeight?: number; // in pounds
  shippingDimensions?: {
    length: number;
    width: number;
    height: number;
  };
  
  // Metadata
  tags: string[];
  isActive: boolean;
  isFeatured: boolean;
  
  // Artist Info
  artistId: string;
  artistName: string;
  
  // Sales Data
  totalSold: number;
  totalRevenue: number;
  
  // Timestamps
  createdAt: Timestamp;
  updatedAt: Timestamp;
  
  // SEO
  slug?: string;
  metaDescription?: string;
}

/**
 * Create a new artist product
 */
export const createArtistProduct = async (
  artistId: string,
  artistName: string,
  productData: Omit<ArtistProduct, 'id' | 'artistId' | 'artistName' | 'totalSold' | 'totalRevenue' | 'createdAt' | 'updatedAt'>
): Promise<string> => {
  try {
    const productId = `prod_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const product: ArtistProduct = {
      ...productData,
      id: productId,
      artistId,
      artistName,
      totalSold: 0,
      totalRevenue: 0,
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp,
    };
    
    const productRef = doc(db, 'artist_products', productId);
    await setDoc(productRef, product);
    
    return productId;
  } catch (error) {
    console.error('Error creating artist product:', error);
    throw error;
  }
};

/**
 * Get products by artist ID
 */
export const getProductsByArtistId = async (artistId: string): Promise<ArtistProduct[]> => {
  try {
    const productsRef = collection(db, 'artist_products');
    const q = query(
      productsRef, 
      where('artistId', '==', artistId), 
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as ArtistProduct));
  } catch (error) {
    console.error('Error fetching artist products:', error);
    throw error;
  }
};

/**
 * Get single product by ID
 */
export const getArtistProduct = async (productId: string): Promise<ArtistProduct | null> => {
  try {
    const productRef = doc(db, 'artist_products', productId);
    const snapshot = await getDoc(productRef);
    
    if (snapshot.exists()) {
      return {
        id: snapshot.id,
        ...snapshot.data()
      } as ArtistProduct;
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching artist product:', error);
    throw error;
  }
};

/**
 * Update artist product
 */
export const updateArtistProduct = async (
  productId: string,
  updates: Partial<Omit<ArtistProduct, 'id' | 'artistId' | 'createdAt'>>
): Promise<void> => {
  try {
    const productRef = doc(db, 'artist_products', productId);
    await updateDoc(productRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error updating artist product:', error);
    throw error;
  }
};

/**
 * Delete artist product
 */
export const deleteArtistProduct = async (productId: string): Promise<void> => {
  try {
    const productRef = doc(db, 'artist_products', productId);
    await deleteDoc(productRef);
  } catch (error) {
    console.error('Error deleting artist product:', error);
    throw error;
  }
};

/**
 * Toggle product active status
 */
export const toggleProductStatus = async (productId: string, isActive: boolean): Promise<void> => {
  try {
    const productRef = doc(db, 'artist_products', productId);
    await updateDoc(productRef, {
      isActive,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error toggling product status:', error);
    throw error;
  }
};

/**
 * Get all active products (for public display)
 */
export const getAllActiveProducts = async (): Promise<ArtistProduct[]> => {
  try {
    const productsRef = collection(db, 'artist_products');
    const q = query(
      productsRef, 
      where('isActive', '==', true), 
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as ArtistProduct));
  } catch (error) {
    console.error('Error fetching active products:', error);
    throw error;
  }
};

/**
 * Upload images for artist products
 */
export const uploadArtistProductImages = async (
  files: File[], 
  artistId: string, 
  productId?: string
): Promise<{ url: string; isPrimary: boolean; alt?: string }[]> => {
  try {
    const tempProductId = productId || `temp_${Date.now()}`;
    
    const uploadPromises = files.map(async (file, index) => {
      // Create unique filename
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 8);
      const fileName = `${timestamp}_${randomString}_${file.name}`;
      
      // Create storage reference
      const imageRef = ref(storage, `artist-products/${artistId}/${tempProductId}/${fileName}`);
      
      // Upload file
      await uploadBytes(imageRef, file);
      
      // Get download URL
      const url = await getDownloadURL(imageRef);
      
      return {
        url,
        isPrimary: index === 0, // First image is primary by default
        alt: file.name.split('.')[0] // Use filename without extension as alt text
      };
    });

    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error('Error uploading artist product images:', error);
    throw error;
  }
};

/**
 * Delete an image from Firebase Storage
 */
export const deleteArtistProductImage = async (imageUrl: string): Promise<void> => {
  try {
    // Extract the storage path from the URL
    const baseUrl = 'https://firebasestorage.googleapis.com/v0/b/';
    const altTokenIndex = imageUrl.indexOf('&token=');
    const pathStart = imageUrl.indexOf('/o/') + 3;
    const pathEnd = altTokenIndex > -1 ? imageUrl.indexOf('?alt=') : imageUrl.indexOf('?');
    
    if (pathStart > 2 && pathEnd > pathStart) {
      const encodedPath = imageUrl.substring(pathStart, pathEnd);
      const decodedPath = decodeURIComponent(encodedPath);
      
      const imageRef = ref(storage, decodedPath);
      await deleteObject(imageRef);
    }
  } catch (error) {
    console.error('Error deleting image:', error);
    // Don't throw error - image might already be deleted or path might be invalid
  }
};
