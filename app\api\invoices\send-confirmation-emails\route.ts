import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase/config';
import { doc, getDoc } from 'firebase/firestore';
import nodemailer from 'nodemailer';

interface ContactInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

interface Address {
  line1: string;
  line2: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

interface ShippingOption {
  id: string;
  name: string;
  description: string;
  price: number;
  estimatedDays: string;
}

export async function POST(req: NextRequest) {
  try {
    const { 
      invoiceId, 
      customOrderId, 
      contactInfo, 
      shippingAddress,
      selectedShipping 
    }: {
      invoiceId: string;
      customOrderId: string;
      contactInfo: ContactInfo;
      shippingAddress: Address;
      selectedShipping?: ShippingOption;
    } = await req.json();

    // Get invoice details
    const invoiceRef = doc(db, 'invoices', invoiceId);
    const invoiceSnap = await getDoc(invoiceRef);

    if (!invoiceSnap.exists()) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      );
    }

    const invoice = invoiceSnap.data();

    // Generate email content
    const customerEmailContent = generateCustomerConfirmationEmail({
      invoice,
      contactInfo,
      shippingAddress,
      customOrderId,
      selectedShipping
    });

    const adminEmailContent = generateAdminNotificationEmail({
      invoice,
      contactInfo,
      shippingAddress,
      customOrderId,
      selectedShipping
    });

    // Create email transporter
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: Number(process.env.SMTP_PORT),
      secure: Number(process.env.SMTP_PORT) === 465,
      auth: {
        user: process.env.SMTP_USER!,
        pass: process.env.SMTP_PASS!,
      },
    });

    // Send customer confirmation email
    await transporter.sendMail({
      from: process.env.SMTP_USER,
      to: contactInfo.email,
      subject: customerEmailContent.subject,
      html: customerEmailContent.html,
    });

    // Send admin notification email
    await transporter.sendMail({
      from: process.env.SMTP_USER,
      to: '<EMAIL>', // Admin email
      subject: adminEmailContent.subject,
      html: adminEmailContent.html,
    });

    console.log('📧 Confirmation emails sent to:', contactInfo.email, 'and admin');

    return NextResponse.json({
      success: true,
      message: 'Confirmation emails sent successfully',
    });

  } catch (error) {
    console.error('Error sending confirmation emails:', error);
    return NextResponse.json(
      { error: 'Failed to send confirmation emails' },
      { status: 500 }
    );
  }
}

function generateCustomerConfirmationEmail({
  invoice,
  contactInfo,
  shippingAddress,
  customOrderId,
  selectedShipping
}: any) {
  const shippingCost = selectedShipping?.price || 0;
  const totalAmount = invoice.amount + shippingCost;
  return {
    subject: `Payment Confirmation - ${invoice.title}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
          <h1 style="color: #333; margin: 0;">Payment Confirmed!</h1>
        </div>
        
        <div style="padding: 20px;">
          <p>Hi ${contactInfo.firstName},</p>
          
          <p>Thank you for your payment! Your order has been confirmed and we'll begin processing it shortly.</p>
          
          <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Order Details</h3>
            <p><strong>Invoice:</strong> ${invoice.title}</p>
            <p><strong>Description:</strong> ${invoice.description}</p>
            <table style="width: 100%; border-collapse: collapse; margin-top: 15px;">
              <tr>
                <td style="padding: 5px 0; border-bottom: 1px solid #ddd;">Invoice Amount:</td>
                <td style="padding: 5px 0; border-bottom: 1px solid #ddd; text-align: right;">$${invoice.amount.toFixed(2)}</td>
              </tr>
              ${selectedShipping ? `
              <tr>
                <td style="padding: 5px 0; border-bottom: 1px solid #ddd;">
                  Shipping (${selectedShipping.name}):
                  <br><small style="color: #666;">${selectedShipping.description}</small>
                </td>
                <td style="padding: 5px 0; border-bottom: 1px solid #ddd; text-align: right;">
                  ${selectedShipping.price === 0 ? 'FREE' : `$${selectedShipping.price.toFixed(2)}`}
                </td>
              </tr>
              ` : ''}
              <tr style="font-weight: bold;">
                <td style="padding: 10px 0;">Total:</td>
                <td style="padding: 10px 0; text-align: right;">$${totalAmount.toFixed(2)}</td>
              </tr>
            </table>
            <p><strong>Order ID:</strong> ${customOrderId}</p>
          </div>
          
          <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Shipping Information</h3>
            <p><strong>Address:</strong><br>
              ${contactInfo.firstName} ${contactInfo.lastName}<br>
              ${shippingAddress.line1}<br>
              ${shippingAddress.line2 ? shippingAddress.line2 + '<br>' : ''}
              ${shippingAddress.city}, ${shippingAddress.state} ${shippingAddress.postalCode}<br>
              ${shippingAddress.country}
            </p>
            ${selectedShipping ? `
            <p><strong>Shipping Method:</strong> ${selectedShipping.name}</p>
            <p><strong>Estimated Delivery:</strong> ${selectedShipping.estimatedDays}</p>
            ` : ''}
          </div>
          
          <h3>What happens next?</h3>
          <ul>
            <li>We'll begin processing your order immediately</li>
            <li>You'll receive shipping updates via email</li>
            <li>If you have questions, contact us with your order ID: ${customOrderId}</li>
          </ul>
          
          <p>Thank you for your business!</p>
          
          <div style="border-top: 1px solid #ddd; padding-top: 20px; margin-top: 30px; text-align: center; color: #666; font-size: 12px;">
            <p>Milo's Menagerie<br>
            This is an automated confirmation email.</p>
          </div>
        </div>
      </div>
    `
  };
}

function generateAdminNotificationEmail({
  invoice,
  contactInfo,
  shippingAddress,
  customOrderId,
  selectedShipping
}: any) {
  const shippingCost = selectedShipping?.price || 0;
  const totalAmount = invoice.amount + shippingCost;
  return {
    subject: `New Custom Order Payment - ${invoice.title}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #28a745; padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">New Payment Received!</h1>
        </div>
        
        <div style="padding: 20px;">
          <p>A new payment has been received for a custom invoice.</p>
          
          <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Invoice Details</h3>
            <p><strong>Title:</strong> ${invoice.title}</p>
            <p><strong>Description:</strong> ${invoice.description}</p>
            <table style="width: 100%; border-collapse: collapse; margin-top: 15px;">
              <tr>
                <td style="padding: 5px 0; border-bottom: 1px solid #ddd;">Invoice Amount:</td>
                <td style="padding: 5px 0; border-bottom: 1px solid #ddd; text-align: right;">$${invoice.amount.toFixed(2)}</td>
              </tr>
              ${selectedShipping ? `
              <tr>
                <td style="padding: 5px 0; border-bottom: 1px solid #ddd;">
                  Shipping (${selectedShipping.name}):
                  <br><small style="color: #666;">${selectedShipping.description}</small>
                </td>
                <td style="padding: 5px 0; border-bottom: 1px solid #ddd; text-align: right;">
                  ${selectedShipping.price === 0 ? 'FREE' : `$${selectedShipping.price.toFixed(2)}`}
                </td>
              </tr>
              ` : ''}
              <tr style="font-weight: bold;">
                <td style="padding: 10px 0;">Total:</td>
                <td style="padding: 10px 0; text-align: right;">$${totalAmount.toFixed(2)}</td>
              </tr>
            </table>
            <p><strong>Invoice ID:</strong> ${invoice.id}</p>
            <p><strong>Order ID:</strong> ${customOrderId}</p>
          </div>
          
          <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Customer Information</h3>
            <p><strong>Name:</strong> ${contactInfo.firstName} ${contactInfo.lastName}</p>
            <p><strong>Email:</strong> ${contactInfo.email}</p>
            <p><strong>Phone:</strong> ${contactInfo.phone}</p>
          </div>
          
          <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Shipping Information</h3>
            <p><strong>Address:</strong><br>
              ${contactInfo.firstName} ${contactInfo.lastName}<br>
              ${shippingAddress.line1}<br>
              ${shippingAddress.line2 ? shippingAddress.line2 + '<br>' : ''}
              ${shippingAddress.city}, ${shippingAddress.state} ${shippingAddress.postalCode}<br>
              ${shippingAddress.country}
            </p>
            ${selectedShipping ? `
            <p><strong>Shipping Method:</strong> ${selectedShipping.name}</p>
            <p><strong>Estimated Delivery:</strong> ${selectedShipping.estimatedDays}</p>
            ` : ''}
          </div>
          
          <p><strong>Action Required:</strong> Please begin processing this custom order.</p>
          
          <div style="border-top: 1px solid #ddd; padding-top: 20px; margin-top: 30px; text-align: center; color: #666; font-size: 12px;">
            <p>Milo's Menagerie Admin Notification</p>
          </div>
        </div>
      </div>
    `
  };
}
