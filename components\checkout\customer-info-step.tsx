'use client';

import { useState, useEffect } from 'react';
import ContactInfoForm from './contact-info-form';
import GooglePlacesInput from './google-places-input';
import AddressForm from './address-form';

interface Address {
  firstName: string;
  lastName: string;
  line1: string;
  line2: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

interface CustomerInfo {
  email: string;
  phone: string;
  shippingAddress: Address;
  billingAddress: Address;
  sameAsBilling: boolean;
}

interface CustomerInfoStepProps {
  customerInfo: CustomerInfo;
  setCustomerInfo: (info: CustomerInfo) => void;
  onComplete: () => void;
  isLoading: boolean;
}

const countries = [
  { code: 'US', name: 'United States' },
  { code: 'CA', name: 'Canada' },
  { code: 'GB', name: 'United Kingdom' },
  { code: 'AU', name: 'Australia' },
  { code: 'DE', name: 'Germany' },
  { code: 'FR', name: 'France' },
  { code: 'JP', name: 'Japan' },
  { code: 'OTHER', name: 'Other' }
];

export default function CustomerInfoStep({
  customerInfo,
  setCustomerInfo,
  onComplete,
  isLoading
}: CustomerInfoStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});

  // COMPLETELY SEPARATE STATE - No connection to customerInfo
  const [contactInfo, setContactInfo] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: ''
  });

  // SEPARATE ADDRESS STATE - Only for address fields
  const [shippingAddress, setShippingAddress] = useState({
    line1: '',
    line2: '',
    city: '',
    state: '',
    postalCode: '',
    country: 'US'
  });

  // Handle contact info changes (completely separate)
  const handleContactInfoChange = (newContactInfo: typeof contactInfo) => {
    console.log('Contact info changed:', newContactInfo);
    setContactInfo(newContactInfo);
  };

  // Handle address changes (completely separate)
  const handleAddressChange = (updates: Partial<typeof shippingAddress>) => {
    console.log('Address changed:', updates);
    setShippingAddress(prev => ({ ...prev, ...updates }));
  };

  // Handle Google Places selection (only fills address, never touches contact info)
  const handleGooglePlacesSelect = (addressData: any) => {
    console.log('Google Places selected:', addressData);
    setShippingAddress(prev => ({
      ...prev,
      line1: addressData.line1 || prev.line1,
      city: addressData.city || prev.city,
      state: addressData.state || prev.state,
      postalCode: addressData.postalCode || prev.postalCode,
      country: addressData.country || prev.country
    }));
  };

  const updateBillingAddress = (updates: Partial<Address>) => {
    const updatedInfo = {
      ...customerInfo,
      billingAddress: { ...customerInfo.billingAddress, ...updates }
    };
    setCustomerInfo(updatedInfo);
    localStorage.setItem('milos-menagerie-customer-info', JSON.stringify(updatedInfo));
  };

  const handleSameAsBillingChange = (checked: boolean) => {
    const updatedInfo = { ...customerInfo, sameAsBilling: checked };
    if (checked) {
      updatedInfo.billingAddress = { ...customerInfo.shippingAddress };
    }
    setCustomerInfo(updatedInfo);
    localStorage.setItem('milos-menagerie-customer-info', JSON.stringify(updatedInfo));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Contact info validation
    if (!contactInfo.firstName.trim()) newErrors.firstName = 'First name is required';
    if (!contactInfo.lastName.trim()) newErrors.lastName = 'Last name is required';
    if (!contactInfo.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(contactInfo.email)) {
      newErrors.email = 'Please enter a valid email';
    }
    if (!contactInfo.phone.trim()) newErrors.phone = 'Phone number is required';

    // Address validation
    if (!shippingAddress.line1.trim()) newErrors.line1 = 'Address is required';
    if (!shippingAddress.city.trim()) newErrors.city = 'City is required';
    if (!shippingAddress.state.trim() && shippingAddress.country === 'US') newErrors.state = 'State is required';
    if (!shippingAddress.postalCode.trim()) newErrors.postalCode = 'Postal code is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      // Combine the separate states into the expected customerInfo format
      const combinedCustomerInfo: CustomerInfo = {
        email: contactInfo.email,
        phone: contactInfo.phone,
        shippingAddress: {
          firstName: contactInfo.firstName,
          lastName: contactInfo.lastName,
          line1: shippingAddress.line1,
          line2: shippingAddress.line2,
          city: shippingAddress.city,
          state: shippingAddress.state,
          postalCode: shippingAddress.postalCode,
          country: shippingAddress.country
        },
        billingAddress: {
          firstName: contactInfo.firstName,
          lastName: contactInfo.lastName,
          line1: shippingAddress.line1,
          line2: shippingAddress.line2,
          city: shippingAddress.city,
          state: shippingAddress.state,
          postalCode: shippingAddress.postalCode,
          country: shippingAddress.country
        },
        sameAsBilling: true
      };

      console.log('Submitting combined customer info:', combinedCustomerInfo);
      setCustomerInfo(combinedCustomerInfo);
      onComplete();
    }
  };



  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-medium">Customer Information</h2>

      {/* Contact Information - Completely Separate */}
      <ContactInfoForm
        contactInfo={contactInfo}
        onContactInfoChange={handleContactInfoChange}
        errors={errors}
      />

      {/* Google Places Address Lookup - Separate Input */}
      <div className="bg-white p-6 rounded-lg border">
        <GooglePlacesInput
          onAddressSelect={handleGooglePlacesSelect}
          placeholder="Start typing your address for suggestions..."
        />
      </div>

      {/* Address Form - Gets filled by Google Places */}
      <AddressForm
        address={shippingAddress}
        onAddressChange={handleAddressChange}
        errors={errors}
        title="Shipping Address"
      />

      {/* Continue Button */}
      <form onSubmit={handleSubmit}>
        <button
          type="submit"
          disabled={isLoading}
          className="w-full bg-black text-white font-medium py-3 px-6 rounded-lg hover:bg-gray-800 disabled:opacity-50 transition-colors"
        >
          {isLoading ? 'Saving...' : 'Continue to Shipping'}
        </button>
      </form>
    </div>
  );
}
