'use client';

import { useState, useRef, useEffect } from 'react';
import { Product, ProductImage, ProductVideo, ProductCategory, ProductVariation, getCategories, addProductVariation, updateProductVariation, deleteProductVariation } from '@/lib/firebase/products';
import ProductVariationsManager from './ProductVariationsManager';

interface ProductFormProps {
  product?: Product | null;
  onSubmit: (productData: Omit<Product, 'id'>, imageFiles?: File[], videoFiles?: File[]) => Promise<void>;
  onCancel: () => void;
}

export default function ProductForm({ product, onSubmit, onCancel }: ProductFormProps) {
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [formData, setFormData] = useState({
    title: product?.title || '',
    description: product?.description || '',
    price: product?.price || 0,
    stock: product?.stock || 0,
    size: (product?.size || 'medium') as 'small' | 'medium' | 'large' | 'wide',
    category: product?.category || '',
    variations: product?.variations || [],
    images: product?.images || [],
    videos: product?.videos || []
  });
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [existingImages, setExistingImages] = useState<ProductImage[]>(product?.images || []);
  const [videoFiles, setVideoFiles] = useState<File[]>([]);
  const [videoPreviews, setVideoPreviews] = useState<string[]>([]);
  const [existingVideos, setExistingVideos] = useState<ProductVideo[]>(product?.videos || []);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  // Load categories on component mount
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const fetchedCategories = await getCategories();
        setCategories(fetchedCategories);
      } catch (error) {
        console.error('Error loading categories:', error);
      }
    };
    loadCategories();
  }, []);

  // Variation management handlers
  const handleAddVariation = async (variation: Omit<ProductVariation, 'id'>) => {
    if (product?.id) {
      // For existing product, add to Firebase
      try {
        const variationId = await addProductVariation(product.id, variation);
        const newVariation = { ...variation, id: variationId };
        setFormData(prev => ({
          ...prev,
          variations: [...prev.variations, newVariation]
        }));
      } catch (error) {
        console.error('Error adding variation:', error);
      }
    } else {
      // For new product, add to local state
      const newVariation = {
        ...variation,
        id: `temp_var_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };
      setFormData(prev => ({
        ...prev,
        variations: [...prev.variations, newVariation]
      }));
    }
  };

  const handleUpdateVariation = async (variationId: string, updates: Partial<ProductVariation>) => {
    if (product?.id) {
      // For existing product, update in Firebase
      try {
        await updateProductVariation(product.id, variationId, updates);
        setFormData(prev => ({
          ...prev,
          variations: prev.variations.map(variation =>
            variation.id === variationId ? { ...variation, ...updates } : variation
          )
        }));
      } catch (error) {
        console.error('Error updating variation:', error);
      }
    } else {
      // For new product, update in local state
      setFormData(prev => ({
        ...prev,
        variations: prev.variations.map(variation =>
          variation.id === variationId ? { ...variation, ...updates } : variation
        )
      }));
    }
  };

  const handleDeleteVariation = async (variationId: string) => {
    if (product?.id) {
      // For existing product, delete from Firebase
      try {
        await deleteProductVariation(product.id, variationId);
        setFormData(prev => ({
          ...prev,
          variations: prev.variations.filter(variation => variation.id !== variationId)
        }));
      } catch (error) {
        console.error('Error deleting variation:', error);
      }
    } else {
      // For new product, remove from local state
      setFormData(prev => ({
        ...prev,
        variations: prev.variations.filter(variation => variation.id !== variationId)
      }));
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      setImageFiles(prev => [...prev, ...files]);
      
      // Create preview URLs
      const newPreviews = files.map(file => URL.createObjectURL(file));
      setImagePreviews(prev => [...prev, ...newPreviews]);
    }
  };

  const removeExistingImage = (index: number) => {
    const updatedImages = existingImages.filter((_, i) => i !== index);
    setExistingImages(updatedImages);
    setFormData(prev => ({ ...prev, images: updatedImages }));
  };

  const removeNewImage = (index: number) => {
    setImageFiles(prev => prev.filter((_, i) => i !== index));
    setImagePreviews(prev => {
      URL.revokeObjectURL(prev[index]);
      return prev.filter((_, i) => i !== index);
    });
  };

  const handleVideoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      setVideoFiles(prev => [...prev, ...files]);

      // Create preview URLs
      const newPreviews = files.map(file => URL.createObjectURL(file));
      setVideoPreviews(prev => [...prev, ...newPreviews]);
    }
  };

  const removeExistingVideo = (index: number) => {
    const updatedVideos = existingVideos.filter((_, i) => i !== index);
    setExistingVideos(updatedVideos);
    setFormData(prev => ({ ...prev, videos: updatedVideos }));
  };

  const removeNewVideo = (index: number) => {
    setVideoFiles(prev => prev.filter((_, i) => i !== index));
    setVideoPreviews(prev => {
      URL.revokeObjectURL(prev[index]);
      return prev.filter((_, i) => i !== index);
    });
  };

  const handleDragStart = (index: number) => {
    setDraggedIndex(index);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    if (draggedIndex === null) return;

    const newFiles = [...imageFiles];
    const newPreviews = [...imagePreviews];
    
    const draggedFile = newFiles[draggedIndex];
    const draggedPreview = newPreviews[draggedIndex];
    
    newFiles.splice(draggedIndex, 1);
    newPreviews.splice(draggedIndex, 1);
    
    newFiles.splice(dropIndex, 0, draggedFile);
    newPreviews.splice(dropIndex, 0, draggedPreview);
    
    setImageFiles(newFiles);
    setImagePreviews(newPreviews);
    setDraggedIndex(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    try {
      const updatedFormData = {
        ...formData,
        images: existingImages,
        videos: existingVideos
      };
      await onSubmit(updatedFormData, imageFiles, videoFiles);
    } catch (err: any) {
      setError(err.message || 'An error occurred while saving the product');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg mb-6">
      <h3 className="text-lg font-semibold mb-4">
        {product ? 'Edit Product' : 'Add New Product'}
      </h3>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Title
          </label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
            rows={3}
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Price ($)
            </label>
            <input
              type="number"
              step="0.01"
              value={formData.price}
              onChange={(e) => setFormData(prev => ({ ...prev, price: parseFloat(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Stock Quantity
            </label>
            <input
              type="number"
              min="0"
              value={formData.stock}
              onChange={(e) => setFormData(prev => ({ ...prev, stock: parseInt(e.target.value) || 0 }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
              placeholder="0 = unlimited"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Size
          </label>
          <select
            value={formData.size}
            onChange={(e) => setFormData(prev => ({ ...prev, size: e.target.value as 'small' | 'medium' | 'large' | 'wide' }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
          >
            <option value="small">Small</option>
            <option value="medium">Medium</option>
            <option value="large">Large</option>
            <option value="wide">Wide</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Category
          </label>
          <select
            value={formData.category}
            onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
          >
            <option value="">Select a category (optional)</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Product Images
          </label>
          
          {/* Existing Images */}
          {existingImages.length > 0 && (
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">Current images:</p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {existingImages.sort((a, b) => a.order - b.order).map((image, index) => (
                  <div
                    key={image.id}
                    className="relative group border-2 border-gray-300 rounded-lg p-2"
                  >
                    <img
                      src={image.url}
                      alt={image.alt || `Image ${index + 1}`}
                      className="w-full h-24 object-cover rounded"
                    />
                    <div className="absolute top-1 left-1 bg-black text-white text-xs px-1 rounded">
                      {index + 1}
                    </div>
                    <button
                      type="button"
                      onClick={() => removeExistingImage(index)}
                      className="absolute top-1 right-1 bg-red-500 text-white text-xs w-5 h-5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* New Image Upload */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            multiple
            onChange={handleImageChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
          
          {/* New Image Previews */}
          {imagePreviews.length > 0 && (
            <div className="mt-4">
              <p className="text-sm text-gray-600 mb-2">New images to add (drag to reorder):</p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {imagePreviews.map((preview, index) => (
                  <div
                    key={index}
                    draggable
                    onDragStart={() => handleDragStart(index)}
                    onDragOver={handleDragOver}
                    onDrop={(e) => handleDrop(e, index)}
                    className="relative group cursor-move border-2 border-dashed border-gray-300 rounded-lg p-2 hover:border-gray-400"
                  >
                    <img
                      src={preview}
                      alt={`Preview ${index + 1}`}
                      className="w-full h-24 object-cover rounded"
                    />
                    <div className="absolute top-1 left-1 bg-green-600 text-white text-xs px-1 rounded">
                      New {index + 1}
                    </div>
                    <button
                      type="button"
                      onClick={() => removeNewImage(index)}
                      className="absolute top-1 right-1 bg-red-500 text-white text-xs w-5 h-5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Video Upload Section */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Product Videos (Optional)
          </label>

          {/* Existing Videos */}
          {existingVideos.length > 0 && (
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">Current videos:</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {existingVideos.sort((a, b) => a.order - b.order).map((video, index) => (
                  <div
                    key={video.id}
                    className="relative group border-2 border-gray-300 rounded-lg p-2"
                  >
                    <video
                      src={video.url}
                      className="w-full h-32 object-cover rounded"
                      controls
                    />
                    <div className="absolute top-1 left-1 bg-black text-white text-xs px-1 rounded">
                      {index + 1}
                    </div>
                    <button
                      type="button"
                      onClick={() => removeExistingVideo(index)}
                      className="absolute top-1 right-1 bg-red-500 text-white text-xs w-5 h-5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* New Video Upload */}
          <input
            ref={videoInputRef}
            type="file"
            accept="video/*"
            multiple
            onChange={handleVideoChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
          <p className="text-xs text-gray-500 mt-1">
            Supported formats: MP4, MOV, AVI. Max size: 50MB per video.
          </p>

          {/* New Video Previews */}
          {videoPreviews.length > 0 && (
            <div className="mt-4">
              <p className="text-sm text-gray-600 mb-2">New videos to add:</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {videoPreviews.map((preview, index) => (
                  <div
                    key={index}
                    className="relative group border-2 border-dashed border-gray-300 rounded-lg p-2"
                  >
                    <video
                      src={preview}
                      className="w-full h-32 object-cover rounded"
                      controls
                    />
                    <div className="absolute top-1 left-1 bg-green-600 text-white text-xs px-1 rounded">
                      New {index + 1}
                    </div>
                    <button
                      type="button"
                      onClick={() => removeNewVideo(index)}
                      className="absolute top-1 right-1 bg-red-500 text-white text-xs w-5 h-5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Product Variations Section */}
        <div className="border-t pt-6">
          <ProductVariationsManager
            variations={formData.variations}
            onAddVariation={handleAddVariation}
            onUpdateVariation={handleUpdateVariation}
            onDeleteVariation={handleDeleteVariation}
          />
        </div>

        {error && (
          <div className="bg-red-50 border border-red-300 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {loading && (
          <div className="bg-blue-50 border border-blue-300 text-blue-700 px-4 py-3 rounded">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-700"></div>
              <span>Adding product... {uploadProgress}%</span>
            </div>
          </div>
        )}

        <div className="flex space-x-4">
          <button
            type="submit"
            disabled={loading}
            className="flex-1 bg-black text-white py-2 px-4 rounded-md hover:bg-gray-800 disabled:opacity-50"
          >
            {loading ? (product ? 'Updating...' : 'Adding...') : (product ? 'Update Product' : 'Add Product')}
          </button>
          <button
            type="button"
            onClick={onCancel}
            className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}








