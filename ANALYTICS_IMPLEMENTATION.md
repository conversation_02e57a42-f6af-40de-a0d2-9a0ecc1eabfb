# Product Analytics System Implementation

## Overview
This implementation provides comprehensive product analytics tracking for Milo's Jungle marketplace, allowing artists and admins to track detailed user interactions with products.

## What's Tracked

### 1. Product View Events
- **When**: User visits a product page or views a product card
- **Data**: View duration, device type, referrer, user agent
- **Purpose**: Understanding product visibility and engagement

### 2. Product Click Events  
- **When**: User clicks on product card or product image
- **Data**: Click target (card/image), image index if clicked on image
- **Purpose**: Measuring click-through rates and user interaction patterns

### 3. Add to Cart Events
- **When**: User adds product to cart (from card or product page)
- **Data**: Product price, current image being viewed
- **Purpose**: Tracking conversion funnel and purchase intent

### 4. Share Events
- **When**: User shares a product (from product card)
- **Data**: Share method (native share API or clipboard)
- **Purpose**: Measuring virality and social engagement

### 5. Image View Events
- **When**: User views different product images (carousel navigation)
- **Data**: Image index, view duration
- **Purpose**: Understanding which images are most engaging

### 6. Page Exit Events
- **When**: User leaves product page
- **Data**: Total time spent on page
- **Purpose**: Measuring engagement depth

## Files Created/Modified

### Core Analytics System
- `lib/firebase/analytics.ts` - Analytics data models and Firebase functions
- `lib/hooks/use-analytics.ts` - React hook for tracking analytics

### UI Components Updated
- `components/ui/product-card.tsx` - Added tracking to all interactions
- `app/product/[id]/page.tsx` - Added tracking to product page

### Dashboard Components
- `components/artist/AnalyticsDashboard.tsx` - Artist analytics dashboard
- `components/admin/AdminAnalyticsDashboard.tsx` - Admin analytics dashboard
- `components/artist/ArtistDashboard.tsx` - Added analytics tab

## Analytics Dashboards

### Artist Dashboard (`/dashboard` - Analytics tab)
Artists can view:
- Total views, clicks, add-to-carts, shares for their products
- Average view duration per product
- Conversion rates (purchases/views)
- Product performance comparison table
- Time period filtering (7d, 30d, 90d, all time)

### Admin Dashboard 
Super admins can view:
- All products analytics across the platform
- Filter by artist or view all products
- Sort by views, clicks, or conversion rate
- Compare performance between site products and artist products
- Identify top-performing products and artists

## Database Collections

### `analytics_events`
Individual event records:
```typescript
{
  productId: string,
  eventType: 'product_view' | 'product_click' | 'add_to_cart' | 'share_click' | 'image_view' | 'page_exit',
  timestamp: Firestore.Timestamp,
  sessionId: string,
  userId?: string,
  duration?: number,
  metadata: {
    imageIndex?: number,
    price?: number,
    artistId?: string,
    referrer?: string,
    userAgent?: string,
    deviceType: 'mobile' | 'tablet' | 'desktop'
  }
}
```

### `product_analytics`
Aggregated statistics per product:
```typescript
{
  productId: string,
  artistId?: string,
  totalViews: number,
  totalClicks: number,
  totalAddToCarts: number,
  totalShares: number,
  totalPurchases: number,
  averageViewDuration: number,
  uniqueVisitors: number,
  conversionRate: number,
  lastUpdated: Firestore.Timestamp,
  dailyStats: {
    [date]: {
      views: number,
      clicks: number,
      addToCarts: number,
      shares: number,
      purchases: number
    }
  }
}
```

## Key Features

### 1. Real-time Tracking
- Events are tracked immediately when they occur
- Non-blocking implementation (errors don't affect user experience)
- Session-based tracking for user journey analysis

### 2. Privacy-Conscious
- Only tracks behavioral data, not personal information
- Uses session IDs instead of personal identifiers
- Respects user privacy while providing valuable insights

### 3. Performance Optimized
- Asynchronous event tracking
- Efficient data aggregation
- Minimal impact on page load times

### 4. Comprehensive Metrics
- View duration tracking with visibility API
- Device type detection
- Referrer tracking for traffic analysis
- Conversion funnel analysis

## Usage Examples

### Track Custom Event
```typescript
import { useProductAnalytics } from '@/lib/hooks/use-analytics';

const { trackEvent } = useProductAnalytics(productId, artistId);

// Track custom interaction
trackEvent('share_click', { 
  shareMethod: 'twitter',
  customData: 'value'
});
```

### Get Analytics Data
```typescript
import { getProductAnalytics } from '@/lib/firebase/analytics';

const analytics = await getProductAnalytics(productId);
console.log(`Views: ${analytics.totalViews}`);
console.log(`Conversion Rate: ${analytics.conversionRate}%`);
```

## Next Steps

1. **Purchase Tracking**: Add purchase event tracking when checkout is completed
2. **A/B Testing**: Use analytics data to test different product layouts
3. **Recommendations**: Build recommendation engine based on view patterns  
4. **Email Insights**: Send weekly/monthly analytics reports to artists
5. **Export Features**: Allow CSV export of analytics data
6. **Advanced Filtering**: Add date range pickers and advanced filters

## Firestore Indexes Required

Create these composite indexes in Firebase Console:

1. **analytics_events collection**:
   - `productId` (Ascending) + `timestamp` (Descending)
   - `eventType` (Ascending) + `timestamp` (Descending)

2. **product_analytics collection**:
   - `artistId` (Ascending) + `totalViews` (Descending)
   - `totalViews` (Descending) + `__name__` (Ascending)

The system is now ready to provide detailed insights into product performance for both artists and administrators!
