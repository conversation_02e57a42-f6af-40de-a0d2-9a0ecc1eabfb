'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import ArtistLogin from '@/components/artist/ArtistLogin';
import { isUserAdmin, AdminUser } from '@/lib/auth/admin-firestore';
import { onAuthStateChanged, User } from 'firebase/auth';
import { auth } from '@/lib/firebase/config';

export default function ArtistLoginPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        // User is authenticated, check their role and redirect accordingly
        try {
          const adminUser: AdminUser | null = await isUserAdmin(firebaseUser);
          console.log('Admin user data:', adminUser); // Debug log
          
          if (adminUser) {
            if (adminUser.artistId) {
              // Approved artist - redirect to artist dashboard
              console.log('Redirecting to dashboard - artistId:', adminUser.artistId);
              router.push('/dashboard');
              return; // Keep loading state, don't show login form
            } else if (adminUser.role === 'admin' || adminUser.role === 'super_admin') {
              // Admin - redirect to admin dashboard
              console.log('Redirecting to admin - role:', adminUser.role);
              router.push('/admin');
              return; // Keep loading state, don't show login form
            }
          }
          // User is authenticated but not an admin/artist - redirect to artist application
          console.log('Redirecting to artist application');
          router.push('/artist');
          return; // Keep loading state, don't show login form
        } catch (error) {
          console.error('Error checking user status:', error);
          // On error, redirect to artist application page
          router.push('/artist');
          return;
        }
      } else {
        // User is not authenticated - show login form
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, [router]);

  const handleSignIn = async (email: string, password: string) => {
    // The authentication happens in the ArtistLogin component
    // After successful auth, the useEffect will handle the redirection
    // So we don't need to do anything here except wait for the auth state change
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto"></div>
          <p className="mt-2 text-gray-600 font-helvetica">Redirecting to your dashboard...</p>
        </div>
      </div>
    );
  }

  return <ArtistLogin onSignIn={handleSignIn} />;
}
