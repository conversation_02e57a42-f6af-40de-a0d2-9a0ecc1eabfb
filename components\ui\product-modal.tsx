'use client';

import { useState } from 'react';
import Image from 'next/image';
import { X } from 'lucide-react';
import Modal from '@/components/shared/modal';
import { ProductImage } from '@/lib/firebase/products';
import { useCart } from '@/lib/context/cart-context';

interface ProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: {
    id: string;
    title: string;
    description: string;
    images: ProductImage[];
    price: number;
    stock?: number;
  };
}

export default function ProductModal({ isOpen, onClose, product }: ProductModalProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [imageError, setImageError] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const { dispatch } = useCart();

  const sortedImages = product.images.sort((a, b) => a.order - b.order);
  const currentImage = sortedImages[currentImageIndex];

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % sortedImages.length);
    setImageError(false);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + sortedImages.length) % sortedImages.length);
    setImageError(false);
  };

  const handleAddToCart = () => {
    setIsAdding(true);
    dispatch({
      type: 'ADD_ITEM',
      payload: {
        id: product.id,
        title: product.title,
        description: product.description,
        imageUrl: currentImage?.url || '',
        price: product.price,
        maxStock: product.stock,
      },
    });
    
    setTimeout(() => {
      setIsAdding(false);
    }, 600);
  };

  const formatTitle = (title: string) => {
    if (!title) return '';
    const firstLetter = title.charAt(0);
    const restOfTitle = title.slice(1);
    
    return (
      <span>
        <span className="text-3xl">{firstLetter}</span>
        {restOfTitle}
      </span>
    );
  };

  return (
    <Modal showModal={isOpen} setShowModal={onClose}>
      <div className="w-full max-w-4xl mx-auto bg-white rounded-lg overflow-hidden relative">
        {/* Close button - Desktop only */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 p-2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full transition-all hidden md:block"
        >
          <X size={20} className="text-gray-600" />
        </button>

        <div className="flex flex-col md:flex-row">
          <div className="md:w-2/3 relative">
            <div className="aspect-square md:aspect-square relative bg-gray-100 min-h-[300px] md:min-h-0">
              {currentImage && !imageError ? (
                <Image
                  src={currentImage.url}
                  alt={currentImage.alt || product.title}
                  fill
                  className="object-cover"
                  onError={() => setImageError(true)}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400">
                  No image available
                </div>
              )}
              
              {sortedImages.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                  >
                    ←
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                  >
                    →
                  </button>
                </>
              )}
            </div>
            
            {sortedImages.length > 1 && (
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                {sortedImages.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      index === currentImageIndex ? 'bg-white' : 'bg-white bg-opacity-50'
                    }`}
                  />
                ))}
              </div>
            )}
          </div>

          <div className="md:w-1/3 p-6 flex flex-col justify-between">
            <div>
              <h2 className="font-editorial font-normal text-2xl text-black mb-4 leading-tight">
                {formatTitle(product.title)}
              </h2>
              <p className="text-gray-600 font-helvetica mb-6 leading-relaxed">
                {product.description}
              </p>
              <p className="text-black font-helvetica font-medium text-xl mb-2">
                ${product.price.toFixed(2)}
              </p>
              {product.stock !== undefined && (
                <p className="text-sm text-gray-500 font-helvetica mb-6">
                  {product.stock > 0 ? (
                    product.stock <= 5 ? (
                      <span className="text-orange-600">Only {product.stock} left in stock</span>
                    ) : (
                      `${product.stock} in stock`
                    )
                  ) : (
                    <span className="text-red-600 font-medium">Out of Stock</span>
                  )}
                </p>
              )}
            </div>
            
            <button
              onClick={handleAddToCart}
              disabled={isAdding || (product.stock !== undefined && product.stock <= 0)}
              className={`w-full font-helvetica font-medium py-3 px-4 rounded transition-all duration-300 ${
                product.stock !== undefined && product.stock <= 0
                  ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                  : isAdding 
                    ? 'scale-95 bg-green-600 transform text-white' 
                    : 'bg-black text-white hover:bg-gray-800 active:scale-95'
              }`}
            >
              {product.stock !== undefined && product.stock <= 0 
                ? 'Out of Stock' 
                : isAdding 
                  ? 'Added!' 
                  : 'Add to Cart'
              }
            </button>
          </div>
        </div>
      </div>
    </Modal>
  );
}



