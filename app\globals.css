@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  @property --pos-x {
    syntax: '<percentage>';
    initial-value: 11.14%;
    inherits: false;
  }

  @property --pos-y {
    syntax: '<percentage>';
    initial-value: 140%;
    inherits: false;
  }

  @property --spread-x {
    syntax: '<percentage>';
    initial-value: 150%;
    inherits: false;
  }

  @property --spread-y {
    syntax: '<percentage>';
    initial-value: 180.06%;
    inherits: false;
  }

  @property --color-1 {
    syntax: '<color>';
    initial-value: #1e3a8a;
    inherits: false;
  }

  @property --color-2 {
    syntax: '<color>';
    initial-value: #3b82f6;
    inherits: false;
  }

  @property --color-3 {
    syntax: '<color>';
    initial-value: #1d4ed8;
    inherits: false;
  }

  @property --color-4 {
    syntax: '<color>';
    initial-value: #2563eb;
    inherits: false;
  }

  @property --color-5 {
    syntax: '<color>';
    initial-value: #1e40af;
    inherits: false;
  }

  @property --border-angle {
    syntax: '<angle>';
    initial-value: 20deg;
    inherits: true;
  }

  @property --border-color-1 {
    syntax: '<color>';
    initial-value: hsla(220, 75%, 40%, 0.4);
    inherits: true;
  }

  @property --border-color-2 {
    syntax: '<color>';
    initial-value: hsla(220, 85%, 25%, 0.8);
    inherits: true;
  }

  @property --stop-1 {
    syntax: '<percentage>';
    initial-value: 37.35%;
    inherits: false;
  }

  @property --stop-2 {
    syntax: '<percentage>';
    initial-value: 61.36%;
    inherits: false;
  }

  @property --stop-3 {
    syntax: '<percentage>';
    initial-value: 78.42%;
    inherits: false;
  }

  @property --stop-4 {
    syntax: '<percentage>';
    initial-value: 89.52%;
    inherits: false;
  }

  @property --stop-5 {
    syntax: '<percentage>';
    initial-value: 100%;
    inherits: false;
  }
}

/* Keyframes for continuous animation */
@keyframes gradientFlow {
  0% {
    --pos-x: 11.14%;
    --pos-y: 140%;
    --spread-x: 150%;
    --spread-y: 180.06%;
  }

  25% {
    --pos-x: 80%;
    --pos-y: 60%;
    --spread-x: 170%;
    --spread-y: 160%;
  }

  50% {
    --pos-x: 90%;
    --pos-y: 20%;
    --spread-x: 190%;
    --spread-y: 200%;
  }

  75% {
    --pos-x: 20%;
    --pos-y: 80%;
    --spread-x: 160%;
    --spread-y: 170%;
  }

  100% {
    --pos-x: 11.14%;
    --pos-y: 140%;
    --spread-x: 150%;
    --spread-y: 180.06%;
  }
}

@keyframes colorShift {
  0% {
    --color-1: #1e3a8a;
    --color-2: #3b82f6;
    --color-3: #1d4ed8;
    --color-4: #2563eb;
    --color-5: #1e40af;
  }

  25% {
    --color-1: #3b82f6;
    --color-2: #60a5fa;
    --color-3: #2563eb;
    --color-4: #1d4ed8;
    --color-5: #1e3a8a;
  }

  50% {
    --color-1: #1d4ed8;
    --color-2: #2563eb;
    --color-3: #60a5fa;
    --color-4: #3b82f6;
    --color-5: #1e40af;
  }

  75% {
    --color-1: #2563eb;
    --color-2: #1e40af;
    --color-3: #3b82f6;
    --color-4: #60a5fa;
    --color-5: #1d4ed8;
  }

  100% {
    --color-1: #1e3a8a;
    --color-2: #3b82f6;
    --color-3: #1d4ed8;
    --color-4: #2563eb;
    --color-5: #1e40af;
  }
}

@keyframes borderRotate {
  0% {
    --border-angle: 20deg;
  }

  50% {
    --border-angle: 200deg;
  }

  100% {
    --border-angle: 20deg;
  }
}

@layer components {
  .gradient-button {
    @apply relative appearance-none cursor-pointer;
    background: radial-gradient(var(--spread-x) var(--spread-y) at var(--pos-x) var(--pos-y),
        var(--color-1) var(--stop-1),
        var(--color-2) var(--stop-2),
        var(--color-3) var(--stop-3),
        var(--color-4) var(--stop-4),
        var(--color-5) var(--stop-5));
    transition:
      --pos-x 0.5s,
      --pos-y 0.5s,
      --spread-x 0.5s,
      --spread-y 0.5s,
      --color-1 0.5s,
      --color-2 0.5s,
      --color-3 0.5s,
      --color-4 0.5s,
      --color-5 0.5s,
      --border-angle 0.5s,
      --border-color-1 0.5s,
      --border-color-2 0.5s,
      --stop-1 0.5s,
      --stop-2 0.5s,
      --stop-3 0.5s,
      --stop-4 0.5s,
      --stop-5 0.5s;

    /* Add continuous animations */
    animation:
      gradientFlow 8s ease-in-out infinite,
      colorShift 12s ease-in-out infinite,
      borderRotate 10s linear infinite;
  }

  .gradient-button::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(var(--border-angle),
        var(--border-color-1),
        var(--border-color-2));
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    -webkit-mask-composite: xor;
    pointer-events: none;
  }

  .gradient-button:hover {
    --pos-x: 0%;
    --pos-y: 91.51%;
    --spread-x: 120.24%;
    --spread-y: 103.18%;
    --color-1: #60a5fa;
    --color-2: #3b82f6;
    --color-3: #2563eb;
    --color-4: #1d4ed8;
    --color-5: #1e3a8a;
    --border-angle: 190deg;
    --border-color-1: hsla(220, 80%, 60%, 0.2);
    --border-color-2: hsla(220, 90%, 70%, 0.7);
    --stop-1: 0%;
    --stop-2: 8.8%;
    --stop-3: 21.44%;
    --stop-4: 71.34%;
    --stop-5: 85.76%;

    /* Speed up animations on hover */
    animation:
      gradientFlow 3s ease-in-out infinite,
      colorShift 4s ease-in-out infinite,
      borderRotate 2s linear infinite;

    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  }

  .gradient-button:active {
    transform: translateY(0px) scale(0.98);
    animation:
      gradientFlow 1s ease-in-out infinite,
      colorShift 1.5s ease-in-out infinite,
      borderRotate 0.8s linear infinite;
  }

  .gradient-button-variant {
    --color-1: #1e40af;
    --color-2: #3b82f6;
    --color-3: #60a5fa;
    --color-4: #93c5fd;
    --border-angle: 200deg;
    --border-color-1: hsla(220, 75%, 50%, 0.6);
    --border-color-2: hsla(220, 85%, 30%, 0.8);

    /* Different animation timing for variant */
    animation:
      gradientFlow 10s ease-in-out infinite reverse,
      colorShift 15s ease-in-out infinite,
      borderRotate 12s linear infinite reverse;
  }

  .gradient-button-variant:hover {
    --pos-x: 0%;
    --pos-y: 95.51%;
    --spread-x: 110.24%;
    --spread-y: 110.2%;
    --color-1: #93c5fd;
    --color-2: #60a5fa;
    --color-3: #3b82f6;
    --color-4: #2563eb;
    --color-5: #1e40af;
    --stop-1: 0%;
    --stop-2: 10%;
    --stop-3: 35.44%;
    --stop-4: 71.34%;
    --stop-5: 90.76%;
    --border-angle: 210deg;
    --border-color-1: hsla(220, 80%, 65%, 0.3);
    --border-color-2: hsla(220, 90%, 45%, 0.9);

    /* Faster variant animations on hover */
    animation:
      gradientFlow 4s ease-in-out infinite reverse,
      colorShift 5s ease-in-out infinite,
      borderRotate 3s linear infinite reverse;
  }
}