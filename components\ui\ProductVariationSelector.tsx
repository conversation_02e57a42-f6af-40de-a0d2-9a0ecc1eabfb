'use client';

import { useState, useEffect } from 'react';
import { ProductVariation } from '@/lib/firebase/products';

interface ProductVariationSelectorProps {
  variations: ProductVariation[];
  onSelectionChange: (selections: Record<string, string>) => void;
  onPriceChange: (totalModifier: number) => void;
}

export default function ProductVariationSelector({ 
  variations, 
  onSelectionChange, 
  onPriceChange 
}: ProductVariationSelectorProps) {
  const [selections, setSelections] = useState<Record<string, string>>({});

  useEffect(() => {
    // Calculate total price modifier
    let totalModifier = 0;
    Object.entries(selections).forEach(([variationId, valueId]) => {
      const variation = variations.find(v => v.id === variationId);
      if (variation) {
        const value = variation.values.find(val => val.id === valueId);
        if (value && value.priceModifier) {
          totalModifier += value.priceModifier;
        }
      }
    });
    
    onPriceChange(totalModifier);
    onSelectionChange(selections);
  }, [selections, variations, onSelectionChange, onPriceChange]);

  const handleSelectionChange = (variationId: string, valueId: string) => {
    setSelections(prev => ({
      ...prev,
      [variationId]: valueId
    }));
  };

  if (!variations || variations.length === 0) {
    return null;
  }

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-helvetica font-medium text-black">
        Product Options
      </h3>
      
      {variations.map((variation) => (
        <div key={variation.id} className="space-y-3">
          <div className="flex items-center gap-2">
            <h4 className="font-helvetica font-medium text-black">
              {variation.name}
            </h4>
            {variation.required && (
              <span className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded">
                Required
              </span>
            )}
          </div>
          
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
            {variation.values.map((value) => {
              const isSelected = selections[variation.id] === value.id;
              return (
                <button
                  key={value.id}
                  onClick={() => handleSelectionChange(variation.id, value.id)}
                  className={`p-3 border-2 rounded-lg text-sm font-helvetica transition-all ${
                    isSelected
                      ? 'border-black bg-black text-white'
                      : 'border-gray-300 bg-white text-gray-700 hover:border-gray-400'
                  }`}
                >
                  <div className="text-center">
                    <div className="font-medium">{value.value}</div>
                    {(value.priceModifier || 0) !== 0 && (
                      <div className={`text-xs mt-1 ${isSelected ? 'text-gray-200' : 'text-gray-500'}`}>
                        {(value.priceModifier || 0) > 0 ? '+' : ''}${(value.priceModifier || 0).toFixed(2)}
                      </div>
                    )}
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      ))}
      
      {/* Validation message for required variations */}
      {variations.some(v => v.required && !selections[v.id]) && (
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
          <p className="text-sm text-orange-700 font-helvetica">
            Please select all required options before adding to cart.
          </p>
        </div>
      )}
    </div>
  );
}
