import { Metadata } from 'next';
import { getProduct } from '@/lib/firebase/products';

interface Props {
  params: Promise<{ id: string }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { id: productId } = await params;
  
  try {
    const product = await getProduct(productId);
    
    if (product) {
      const productImage = product.images && product.images.length > 0 
        ? product.images.sort((a, b) => a.order - b.order)[0].url
        : '/milosmenagerie square.png';

      return {
        title: product.title,
        description: product.description,
        openGraph: {
          title: product.title,
          description: product.description,
          images: [
            {
              url: productImage,
              width: 800,
              height: 600,
              alt: product.title,
            },
            {
              url: '/milosmenagerie square.png',
              width: 800,
              height: 800,
              alt: "<PERSON>'s Menagerie",
            }
          ],
          type: 'website',
          siteName: "<PERSON>'s Menagerie",
        },
        twitter: {
          card: 'summary_large_image',
          title: product.title,
          description: product.description,
          images: [productImage],
        },
      };
    }
  } catch (error) {
    console.error('Error generating metadata:', error);
  }

  // Fallback metadata
  return {
    title: 'Product - Milo\'s Menagerie',
    description: '3D printed animals and plants that inspire exploration, learning, and endless adventure',
    openGraph: {
      title: 'Product - Milo\'s Menagerie',
      description: '3D printed animals and plants that inspire exploration, learning, and endless adventure',
      images: [
        {
          url: '/milosmenagerie square.png',
          width: 800,
          height: 800,
          alt: "Milo's Menagerie",
        }
      ],
      type: 'website',
      siteName: "Milo's Menagerie",
    },
    twitter: {
      card: 'summary_large_image',
      title: 'Product - Milo\'s Menagerie',
      description: '3D printed animals and plants that inspire exploration, learning, and endless adventure',
      images: ['/milosmenagerie square.png'],
    },
  };
}

export default function ProductLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
