'use client';

import { useState, useRef } from 'react';
import { signInAdmin, verifyMfaCode, initiateMfa, signInAdminWithGoogle, signOutAdmin } from '@/lib/auth/admin';

interface AdminLoginProps {
  onSignIn: (email: string, password: string) => Promise<any>;
}

export default function AdminLogin({ onSignIn }: AdminLoginProps) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [mfaCode, setMfaCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showMfa, setShowMfa] = useState(false);
  const [mfaResolver, setMfaResolver] = useState<any>(null);
  const [verificationId, setVerificationId] = useState('');
  const recaptchaRef = useRef<HTMLDivElement>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      await signInAdmin(email, password);
      // If we get here, no MFA required
      await onSignIn(email, password);
    } catch (error: any) {
      if (error.code === 'auth/multi-factor-auth-required') {
        // MFA is required
        setMfaResolver(error.resolver);
        setShowMfa(true);
        
        try {
          // Initiate MFA flow
          const verificationId = await initiateMfa(error.resolver, 'recaptcha-container');
          setVerificationId(verificationId);
        } catch (mfaError) {
          console.error('MFA initiation error:', mfaError);
          setError('Failed to initiate multi-factor authentication');
        }
      } else {
        setError(error.message || 'Failed to sign in');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setLoading(true);
    setError('');

    try {
      // Clear any existing authentication state first
      await signOutAdmin();
      
      const user = await signInAdminWithGoogle();
      // Simulate onSignIn call for Google login
      await onSignIn(user.email!, ''); // No password for Google login
    } catch (error: any) {
      if (error.code === 'auth/multi-factor-auth-required') {
        // MFA is required for Google sign-in
        setMfaResolver(error.resolver);
        setShowMfa(true);
        
        try {
          const verificationId = await initiateMfa(error.resolver, 'recaptcha-container');
          setVerificationId(verificationId);
        } catch (mfaError) {
          console.error('MFA initiation error:', mfaError);
          setError('Failed to initiate multi-factor authentication');
        }
      } else if (error.code === 'auth/popup-closed-by-user') {
        // User closed the popup - not an error, just clear loading
        setError('');
      } else if (error.code === 'auth/cancelled-popup-request') {
        // Popup was cancelled - not an error
        setError('');
      } else {
        setError(error.message || 'Failed to sign in with Google');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleMfaSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      await verifyMfaCode(mfaResolver, mfaCode, verificationId);
      // MFA verification successful
      await onSignIn(email, password);
    } catch (error: any) {
      setError(error.message || 'Invalid verification code');
    } finally {
      setLoading(false);
    }
  };

  if (showMfa) {
    return (
      <div className="min-h-screen flex items-start justify-center bg-gray-50 pt-20 px-4">
        <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow">
          <h2 className="text-2xl font-bold text-center">Two-Factor Authentication</h2>
          <p className="text-center text-gray-600">
            Enter the verification code sent to your phone
          </p>
          
          <form onSubmit={handleMfaSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Verification Code</label>
              <input
                type="text"
                value={mfaCode}
                onChange={(e) => setMfaCode(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="Enter 6-digit code"
                maxLength={6}
                required
              />
            </div>

            {error && <div className="text-red-600 text-sm">{error}</div>}

            <button
              type="submit"
              disabled={loading}
              className="w-full py-2 px-4 bg-black text-white rounded-md hover:bg-gray-800 disabled:opacity-50"
            >
              {loading ? 'Verifying...' : 'Verify Code'}
            </button>
            
            <button
              type="button"
              onClick={() => {
                setShowMfa(false);
                setMfaCode('');
                setError('');
              }}
              className="w-full py-2 px-4 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
            >
              Back to Login
            </button>
          </form>
          
          {/* Hidden recaptcha container */}
          <div id="recaptcha-container" ref={recaptchaRef} style={{ display: 'none' }}></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-start justify-center bg-gray-50 pt-20 px-4">
      <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow">
        <h2 className="text-2xl font-bold text-center">Admin Login</h2>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700">Email</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700">Password</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
              required
            />
          </div>

          {error && <div className="text-red-600 text-sm">{error}</div>}

          <button
            type="submit"
            disabled={loading}
            className="w-full py-2 px-4 bg-black text-white rounded-md hover:bg-gray-800 disabled:opacity-50"
          >
            {loading ? 'Signing in...' : 'Sign In'}
          </button>
        </form>

        {/* Divider */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Or continue with</span>
          </div>
        </div>

        {/* Google Sign-In Button */}
        <button
          onClick={handleGoogleSignIn}
          disabled={loading}
          className="w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
        >
          <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          {loading ? 'Signing in...' : 'Continue with Google'}
        </button>
        
        {/* Hidden recaptcha container */}
        <div id="recaptcha-container" ref={recaptchaRef} style={{ display: 'none' }}></div>
      </div>
    </div>
  );
}
