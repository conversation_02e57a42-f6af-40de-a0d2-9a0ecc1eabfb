'use client';

import { useState, useEffect } from 'react';
import { MapPin, Truck, Clock } from 'lucide-react';

interface ShippingAddress {
  name?: string;
  line1?: string;
  line2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country: string;
}

interface ShippingRate {
  name: string;
  description: string;
  price: number;
  estimatedDays: string;
}

interface CartItem {
  id: string;
  title: string;
  price: number;
  quantity: number;
}

interface ShippingCalculatorProps {
  items: CartItem[];
  subtotal: number;
  onShippingChange: (rate: ShippingRate | null, address?: ShippingAddress) => void;
}

export default function ShippingCalculator({ items, subtotal, onShippingChange }: ShippingCalculatorProps) {
  const [address, setAddress] = useState<ShippingAddress>({
    name: '',
    line1: '',
    line2: '',
    city: '',
    state: '',
    postalCode: '',
    country: 'US'
  });

  // Load saved address from localStorage on component mount
  useEffect(() => {
    const savedAddress = localStorage.getItem('milos-menagerie-shipping-address');
    if (savedAddress) {
      try {
        const parsedAddress = JSON.parse(savedAddress);
        setAddress(parsedAddress);
        setAddressLoaded(true);
      } catch (error) {
        console.error('Error loading saved address:', error);
      }
    }
  }, []);

  const clearSavedAddress = () => {
    localStorage.removeItem('milos-menagerie-shipping-address');
    setAddress({
      name: '',
      line1: '',
      line2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'US'
    });
    setAddressLoaded(false);
    setShowRates(false);
    setSelectedRate(null);
    onShippingChange(null, undefined);
  };
  const [shippingRates, setShippingRates] = useState<ShippingRate[]>([]);
  const [selectedRate, setSelectedRate] = useState<ShippingRate | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [showRates, setShowRates] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [addressLoaded, setAddressLoaded] = useState(false);

  const countries = [
    { code: 'US', name: 'United States' },
    { code: 'CA', name: 'Canada' },
    { code: 'GB', name: 'United Kingdom' },
    { code: 'AU', name: 'Australia' },
    { code: 'DE', name: 'Germany' },
    { code: 'FR', name: 'France' },
    { code: 'JP', name: 'Japan' },
    { code: 'KR', name: 'South Korea' },
    { code: 'SG', name: 'Singapore' },
    { code: 'NZ', name: 'New Zealand' },
    { code: 'IT', name: 'Italy' },
    { code: 'ES', name: 'Spain' },
    { code: 'NL', name: 'Netherlands' },
    { code: 'SE', name: 'Sweden' },
    { code: 'NO', name: 'Norway' },
    { code: 'OTHER', name: 'Other' }
  ];

  const calculateShipping = async () => {
    if (!address.country) {
      setError('Please select a country');
      return;
    }

    if (!address.name?.trim() || !address.line1?.trim() || !address.city?.trim()) {
      setError('Please fill in all required address fields');
      return;
    }

    if (!address.postalCode?.trim() && address.country !== 'OTHER') {
      setError('Please enter postal code');
      return;
    }

    setIsCalculating(true);
    setError(null);

    try {
      const response = await fetch('/api/calculate-shipping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          address,
          items,
          subtotal
        }),
      });

      const data = await response.json();

      if (data.success) {
        setShippingRates(data.rates);
        setShowRates(true);
        // Auto-select the first (usually cheapest) option
        if (data.rates.length > 0) {
          setSelectedRate(data.rates[0]);
          onShippingChange(data.rates[0], address);
        }

        // Auto-scroll to shipping options after they appear
        setTimeout(() => {
          const shippingSection = document.querySelector('[data-shipping-options]');
          if (shippingSection) {
            shippingSection.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        }, 100); // Small delay to ensure DOM is updated
      } else {
        setError(data.error || 'Failed to calculate shipping');
      }
    } catch (err) {
      setError('Error calculating shipping rates');
      console.error('Shipping calculation error:', err);
    } finally {
      setIsCalculating(false);
    }
  };

  const handleRateSelection = (rate: ShippingRate) => {
    setSelectedRate(rate);
    onShippingChange(rate, address);
  };

  const handleAddressChange = (field: keyof ShippingAddress, value: string) => {
    const updatedAddress = { ...address, [field]: value };
    setAddress(updatedAddress);

    // Save to localStorage whenever address changes
    localStorage.setItem('milos-menagerie-shipping-address', JSON.stringify(updatedAddress));

    setShowRates(false);
    setSelectedRate(null);
    onShippingChange(null, undefined);
  };

  const handleInputFocus = (event: React.FocusEvent<HTMLInputElement | HTMLSelectElement>) => {
    // Small delay to ensure keyboard is shown first
    setTimeout(() => {
      const element = event.target;
      const rect = element.getBoundingClientRect();
      const viewportHeight = window.innerHeight;

      // Calculate desired position (element should be in upper third of viewport)
      const desiredTop = viewportHeight * 0.25;
      const currentTop = rect.top;

      // Only scroll if the element is not in the desired position
      if (currentTop > desiredTop || currentTop < 50) {
        const scrollAmount = currentTop - desiredTop;
        window.scrollBy({
          top: scrollAmount,
          behavior: 'smooth'
        });
      }
    }, 300); // Wait for mobile keyboard animation
  };

  return (
    <div className="border-t pt-3 space-y-3">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <MapPin size={16} className="text-gray-600" />
          <h3 className="font-helvetica font-medium text-sm">Calculate Shipping</h3>
        </div>
        {addressLoaded && (
          <button
            onClick={clearSavedAddress}
            className="text-xs text-gray-500 hover:text-gray-700 font-helvetica underline"
          >
            Clear Saved
          </button>
        )}
      </div>

      {addressLoaded && (
        <div className="bg-blue-50 border border-blue-200 rounded p-2 mb-3">
          <p className="text-xs text-blue-700 font-helvetica">
            ✓ Using your saved shipping address
          </p>
        </div>
      )}

      {/* Address Form */}
      <div className="space-y-2">
        {/* Full Name */}
        <input
          type="text"
          placeholder="Full Name *"
          value={address.name}
          onChange={(e) => handleAddressChange('name', e.target.value)}
          onFocus={handleInputFocus}
          className="w-full p-4 border border-gray-300 rounded text-lg font-helvetica focus:outline-none focus:ring-1 focus:ring-black focus:border-transparent min-h-[48px]"
          style={{ fontSize: '16px' }}
        />

        {/* Address Line 1 */}
        <input
          type="text"
          placeholder="Address Line 1 *"
          value={address.line1}
          onChange={(e) => handleAddressChange('line1', e.target.value)}
          onFocus={handleInputFocus}
          className="w-full p-4 border border-gray-300 rounded text-lg font-helvetica focus:outline-none focus:ring-1 focus:ring-black focus:border-transparent min-h-[48px]"
          style={{ fontSize: '16px' }}
        />

        {/* Address Line 2 */}
        <input
          type="text"
          placeholder="Address Line 2 (Optional)"
          value={address.line2}
          onChange={(e) => handleAddressChange('line2', e.target.value)}
          onFocus={handleInputFocus}
          className="w-full p-4 border border-gray-300 rounded text-lg font-helvetica focus:outline-none focus:ring-1 focus:ring-black focus:border-transparent min-h-[48px]"
          style={{ fontSize: '16px' }}
        />

        {/* City and State/Postal Code */}
        <div className="grid grid-cols-2 gap-2">
          <input
            type="text"
            placeholder="City *"
            value={address.city}
            onChange={(e) => handleAddressChange('city', e.target.value)}
            onFocus={handleInputFocus}
            className="p-4 border border-gray-300 rounded text-lg font-helvetica focus:outline-none focus:ring-1 focus:ring-black focus:border-transparent min-h-[48px]"
            style={{ fontSize: '16px' }}
          />
          {address.country === 'US' ? (
            <input
              type="text"
              placeholder="State *"
              value={address.state}
              onChange={(e) => handleAddressChange('state', e.target.value)}
              onFocus={handleInputFocus}
              className="p-4 border border-gray-300 rounded text-lg font-helvetica focus:outline-none focus:ring-1 focus:ring-black focus:border-transparent min-h-[48px]"
              style={{ fontSize: '16px' }}
            />
          ) : (
            <input
              type="text"
              placeholder="State/Province"
              value={address.state}
              onChange={(e) => handleAddressChange('state', e.target.value)}
              onFocus={handleInputFocus}
              className="p-4 border border-gray-300 rounded text-lg font-helvetica focus:outline-none focus:ring-1 focus:ring-black focus:border-transparent min-h-[48px]"
              style={{ fontSize: '16px' }}
            />
          )}
        </div>

        {/* Postal Code and Country */}
        <div className="grid grid-cols-2 gap-2">
          <input
            type="text"
            placeholder={address.country === 'US' ? 'ZIP Code *' : 'Postal Code *'}
            value={address.postalCode}
            onChange={(e) => handleAddressChange('postalCode', e.target.value)}
            onFocus={handleInputFocus}
            className="p-4 border border-gray-300 rounded text-lg font-helvetica focus:outline-none focus:ring-1 focus:ring-black focus:border-transparent min-h-[48px]"
            style={{ fontSize: '16px' }}
          />
          <select
            value={address.country}
            onChange={(e) => handleAddressChange('country', e.target.value)}
            onFocus={handleInputFocus}
            className="p-4 border border-gray-300 rounded text-lg font-helvetica focus:outline-none focus:ring-1 focus:ring-black focus:border-transparent min-h-[48px]"
            style={{ fontSize: '16px' }}
          >
            {countries.map(country => (
              <option key={country.code} value={country.code}>
                {country.name}
              </option>
            ))}
          </select>
        </div>

        <button
          onClick={calculateShipping}
          disabled={
            isCalculating ||
            !address.country ||
            !address.name?.trim() ||
            !address.line1?.trim() ||
            !address.city?.trim() ||
            (!address.postalCode?.trim() && address.country !== 'OTHER')
          }
          className="w-full bg-gray-100 hover:bg-gray-200 disabled:bg-gray-50 disabled:text-gray-400 text-black font-helvetica font-medium py-3 px-4 rounded text-base transition-colors"
        >
          {isCalculating ? 'Calculating...' : 'Calculate Shipping'}
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="text-red-600 text-sm font-helvetica bg-red-50 p-2 rounded">
          {error}
        </div>
      )}

      {/* Address Confirmation */}
      {showRates && address.name && address.line1 && (
        <div className="bg-green-50 border border-green-200 rounded p-2 mb-2">
          <div className="flex items-center space-x-1 text-green-700 mb-1">
            <MapPin size={12} />
            <span className="font-helvetica text-xs font-medium">Address Saved</span>
          </div>
          <p className="text-xs text-green-600 font-helvetica">
            {address.name}, {address.line1}, {address.city}, {address.state} {address.postalCode}, {address.country}
          </p>
        </div>
      )}

      {/* Shipping Rates */}
      {showRates && shippingRates.length > 0 && (
        <div className="space-y-2" data-shipping-options>
          <h4 className="font-helvetica font-medium text-xs flex items-center space-x-1">
            <Truck size={14} className="text-gray-600" />
            <span>Shipping Options</span>
          </h4>

          {shippingRates.map((rate, index) => (
            <label
              key={index}
              className={`block p-2 border rounded cursor-pointer transition-colors ${
                selectedRate?.name === rate.name
                  ? 'border-black bg-gray-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <input
                type="radio"
                name="shipping"
                checked={selectedRate?.name === rate.name}
                onChange={() => handleRateSelection(rate)}
                className="sr-only"
              />
              <div className="flex justify-between items-start">
                <div className="flex-1 min-w-0">
                  <div className="font-helvetica font-medium text-xs">
                    {rate.name}
                  </div>
                  <div className="text-gray-600 text-xs font-helvetica truncate">
                    {rate.description}
                  </div>
                  <div className="flex items-center space-x-1 text-gray-500 text-xs mt-1">
                    <Clock size={10} />
                    <span>{rate.estimatedDays}</span>
                  </div>
                </div>
                <div className="font-helvetica font-medium text-xs ml-2 flex-shrink-0">
                  {rate.price === 0 ? 'FREE' : `$${rate.price.toFixed(2)}`}
                </div>
              </div>
            </label>
          ))}
        </div>
      )}
    </div>
  );
}
