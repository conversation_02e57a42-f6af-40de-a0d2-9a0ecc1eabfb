import { collection, addDoc, getDocs, getDoc, doc, updateDoc, deleteDoc, query, orderBy, where } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { db, storage } from './config';

export interface ProductVariation {
  id: string;
  name: string; // e.g., "Metal Color", "Size", "Finish"
  values: ProductVariationValue[];
  required: boolean;
}

export interface ProductVariationValue {
  id: string;
  value: string; // e.g., "Gold", "Silver", "Bronze"
  priceModifier?: number; // Additional cost for this variation
  stockModifier?: number; // Stock specific to this variation
}

export interface ProductCategory {
  id: string;
  name: string;
  description?: string;
  parentId?: string; // For nested categories
}

export interface Product {
  id?: string;
  title: string;
  description: string;
  images: ProductImage[];
  videos: ProductVideo[];
  price: number;
  size: 'small' | 'medium' | 'large' | 'wide';
  stock?: number; // Available inventory quantity
  category?: string; // Category ID
  variations?: ProductVariation[]; // Custom product variations
  createdAt?: Date;
  updatedAt?: Date;
  artistId?: string; // For products from artist_products collection
}

export interface ProductImage {
  id: string;
  url: string;
  order: number;
  alt?: string;
}

export interface ProductVideo {
  id: string;
  url: string;
  order: number;
  title?: string;
}

export const addProduct = async (product: Omit<Product, 'id'>) => {
  try {
    const docRef = await addDoc(collection(db, 'products'), {
      ...product,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error adding product:', error);
    throw error;
  }
};

export const getProducts = async (): Promise<Product[]> => {
  try {
    const q = query(collection(db, 'products'), orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Product));
  } catch (error) {
    console.error('Error getting products:', error);
    throw error;
  }
};

export const getProduct = async (id: string): Promise<Product | null> => {
  try {
    // First try to get from regular products
    const productRef = doc(db, 'products', id);
    const snapshot = await getDoc(productRef);

    if (snapshot.exists()) {
      return {
        id: snapshot.id,
        ...snapshot.data()
      } as Product;
    }

    // If not found, try artist products
    const artistProductRef = doc(db, 'artist_products', id);
    const artistSnapshot = await getDoc(artistProductRef);

    if (artistSnapshot.exists()) {
      const data = artistSnapshot.data();
      return {
        id: artistSnapshot.id,
        title: data.name,
        description: data.description,
        price: data.price,
        stock: data.quantity, // Map quantity to stock for consistency
        images: data.images?.map((img: any, index: number) => ({
          id: `artist-${artistSnapshot.id}-img-${index}`,
          url: img.url,
          order: img.isPrimary ? 0 : index + 1,
          alt: img.alt || data.name
        })) || [],
        videos: [], // Artist products don't have videos yet
        size: 'medium' as const, // Default size for artist products
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        artistId: data.artistId // Preserve the artistId for analytics
      } as Product & { artistId: string };
    }

    return null;
  } catch (error) {
    console.error('Error fetching product:', error);
    throw error;
  }
};

export const uploadProductImages = async (files: File[], productId: string): Promise<ProductImage[]> => {
  try {
    const uploadPromises = files.map(async (file, index) => {
      const imageId = `${Date.now()}-${index}`;
      const imageRef = ref(storage, `products/${productId}/${imageId}-${file.name}`);
      await uploadBytes(imageRef, file);
      const url = await getDownloadURL(imageRef);

      return {
        id: imageId,
        url,
        order: index,
        alt: file.name
      };
    });

    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error('Error uploading images:', error);
    throw error;
  }
};

export const uploadProductVideos = async (files: File[], productId: string): Promise<ProductVideo[]> => {
  try {
    const uploadPromises = files.map(async (file, index) => {
      const videoId = `${Date.now()}-${index}`;
      const videoRef = ref(storage, `products/${productId}/videos/${videoId}-${file.name}`);
      await uploadBytes(videoRef, file);
      const url = await getDownloadURL(videoRef);

      return {
        id: videoId,
        url,
        order: index,
        title: file.name
      };
    });

    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error('Error uploading videos:', error);
    throw error;
  }
};

export const updateProduct = async (id: string, updates: Partial<Omit<Product, 'id'>>) => {
  try {
    const productRef = doc(db, 'products', id);
    await updateDoc(productRef, {
      ...updates,
      updatedAt: new Date()
    });
  } catch (error) {
    console.error('Error updating product:', error);
    throw error;
  }
};

export const deleteProduct = async (id: string) => {
  try {
    await deleteDoc(doc(db, 'products', id));
  } catch (error) {
    console.error('Error deleting product:', error);
    throw error;
  }
};

// New function to get all products from both collections
export const getAllProducts = async (): Promise<Product[]> => {
  try {
    // Get regular products
    const regularProductsQuery = query(collection(db, 'products'), orderBy('createdAt', 'desc'));
    const regularProductsSnapshot = await getDocs(regularProductsQuery);
    const regularProducts = regularProductsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Product));

    // Get artist products
    const artistProductsQuery = query(
      collection(db, 'artist_products'), 
      where('isActive', '==', true),
      orderBy('createdAt', 'desc')
    );
    const artistProductsSnapshot = await getDocs(artistProductsQuery);
    
    // Transform artist products to match Product interface
    const artistProducts = artistProductsSnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        title: data.name,
        description: data.description,
        price: data.price,
        stock: data.quantity, // Map quantity to stock for consistency
        images: data.images?.map((img: any, index: number) => ({
          id: `artist-${doc.id}-img-${index}`,
          url: img.url,
          order: img.isPrimary ? 0 : index + 1,
          alt: img.alt || data.name
        })) || [],
        videos: [], // Artist products don't have videos yet
        size: 'medium' as const, // Default size for artist products
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        artistId: data.artistId // Preserve the artistId for analytics
      } as Product;
    });

    // Combine and sort by creation date
    const allProducts = [...regularProducts, ...artistProducts];
    return allProducts.sort((a, b) => {
      // Safely convert dates, handling various date formats
      const getDateValue = (date: any): number => {
        if (!date) return 0;
        if (date instanceof Date) return date.getTime();
        if (typeof date === 'object' && date.toDate) return date.toDate().getTime(); // Firestore Timestamp
        if (typeof date === 'string') return new Date(date).getTime();
        if (typeof date === 'number') return date;
        return 0;
      };
      
      const dateA = getDateValue(a.createdAt);
      const dateB = getDateValue(b.createdAt);
      return dateB - dateA; // Descending order (newest first)
    });

  } catch (error) {
    console.error('Error getting all products:', error);
    throw error;
  }
};

// Category Management Functions
export const addCategory = async (category: Omit<ProductCategory, 'id'>) => {
  try {
    const docRef = await addDoc(collection(db, 'categories'), category);
    return docRef.id;
  } catch (error) {
    console.error('Error adding category:', error);
    throw error;
  }
};

export const getCategories = async (): Promise<ProductCategory[]> => {
  try {
    const querySnapshot = await getDocs(collection(db, 'categories'));
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as ProductCategory));
  } catch (error) {
    console.error('Error getting categories:', error);
    throw error;
  }
};

export const updateCategory = async (categoryId: string, updates: Partial<ProductCategory>) => {
  try {
    const categoryRef = doc(db, 'categories', categoryId);
    await updateDoc(categoryRef, updates);
  } catch (error) {
    console.error('Error updating category:', error);
    throw error;
  }
};

export const deleteCategory = async (categoryId: string) => {
  try {
    await deleteDoc(doc(db, 'categories', categoryId));
  } catch (error) {
    console.error('Error deleting category:', error);
    throw error;
  }
};

// Product Variation Management Functions
export const addProductVariation = async (productId: string, variation: Omit<ProductVariation, 'id'>) => {
  try {
    const productRef = doc(db, 'products', productId);
    const productDoc = await getDoc(productRef);
    
    if (!productDoc.exists()) {
      throw new Error('Product not found');
    }
    
    const productData = productDoc.data();
    const variations = productData.variations || [];
    
    const newVariation = {
      ...variation,
      id: `var_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
    
    await updateDoc(productRef, {
      variations: [...variations, newVariation],
      updatedAt: new Date()
    });
    
    return newVariation.id;
  } catch (error) {
    console.error('Error adding product variation:', error);
    throw error;
  }
};

export const updateProductVariation = async (productId: string, variationId: string, updates: Partial<ProductVariation>) => {
  try {
    const productRef = doc(db, 'products', productId);
    const productDoc = await getDoc(productRef);
    
    if (!productDoc.exists()) {
      throw new Error('Product not found');
    }
    
    const productData = productDoc.data();
    const variations = productData.variations || [];
    
    const updatedVariations = variations.map((variation: ProductVariation) =>
      variation.id === variationId ? { ...variation, ...updates } : variation
    );
    
    await updateDoc(productRef, {
      variations: updatedVariations,
      updatedAt: new Date()
    });
  } catch (error) {
    console.error('Error updating product variation:', error);
    throw error;
  }
};

export const deleteProductVariation = async (productId: string, variationId: string) => {
  try {
    const productRef = doc(db, 'products', productId);
    const productDoc = await getDoc(productRef);
    
    if (!productDoc.exists()) {
      throw new Error('Product not found');
    }
    
    const productData = productDoc.data();
    const variations = productData.variations || [];
    
    const filteredVariations = variations.filter((variation: ProductVariation) => 
      variation.id !== variationId
    );
    
    await updateDoc(productRef, {
      variations: filteredVariations,
      updatedAt: new Date()
    });
  } catch (error) {
    console.error('Error deleting product variation:', error);
    throw error;
  }
};

