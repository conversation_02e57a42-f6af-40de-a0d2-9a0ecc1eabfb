import { NextRequest, NextResponse } from 'next/server';

interface ShippingAddress {
  country: string;
  state?: string;
  postalCode?: string;
  city?: string;
}

interface CartItem {
  id: string;
  title: string;
  price: number;
  quantity: number;
}

interface ShippingCalculationRequest {
  address: ShippingAddress;
  items: CartItem[];
  subtotal: number;
}

interface ShippingRate {
  name: string;
  description: string;
  price: number;
  estimatedDays: string;
}

// Shipping origin point
const ORIGIN_ZIP = '66048'; // Kansas

// Shipping rate calculation logic
function calculateShippingRates(address: ShippingAddress, subtotal: number, totalWeight: number): ShippingRate[] {
  const { country, state, postalCode } = address;
  const rates: ShippingRate[] = [];

  // Free shipping threshold - US: $75, International: $300
  const freeShippingThreshold = country === 'US' ? 75 : 300;

  // Calculate distance-based adjustments for US shipping
  const isLocalDelivery = country === 'US' && isNearOrigin(postalCode, state);
  const isZone1 = country === 'US' && isZone1Delivery(state); // Nearby states
  const isZone2 = country === 'US' && isZone2Delivery(state); // Mid-distance states
  
  if (country === 'US') {
    // US Domestic shipping with zone-based pricing
    let standardPrice = 8.99;
    let priorityPrice = 15.99;
    let expressPrice = 25.99;
    let estimatedDays = '5-7 business days';

    // Adjust pricing and delivery times based on distance from Kansas (66048)
    if (isLocalDelivery) {
      standardPrice = 6.99;
      priorityPrice = 12.99;
      expressPrice = 19.99;
      estimatedDays = '3-5 business days';
    } else if (isZone1) {
      standardPrice = 7.99;
      priorityPrice = 14.99;
      expressPrice = 22.99;
      estimatedDays = '4-6 business days';
    } else if (isZone2) {
      standardPrice = 8.99;
      priorityPrice = 15.99;
      expressPrice = 25.99;
      estimatedDays = '5-7 business days';
    } else {
      // Zone 3 - Farthest states (West Coast, Northeast)
      standardPrice = 9.99;
      priorityPrice = 17.99;
      expressPrice = 28.99;
      estimatedDays = '6-8 business days';
    }

    if (subtotal >= freeShippingThreshold) {
      rates.push({
        name: 'Free Standard Shipping',
        description: `Free shipping on orders over $${freeShippingThreshold}`,
        price: 0,
        estimatedDays: estimatedDays
      });
    } else {
      rates.push({
        name: 'Standard Shipping',
        description: `USPS Ground`,
        price: standardPrice,
        estimatedDays: estimatedDays
      });
    }

    rates.push({
      name: 'Priority Shipping',
      description: 'USPS Priority Mail',
      price: priorityPrice,
      estimatedDays: isLocalDelivery ? '1-2 business days' : '2-3 business days'
    });

    rates.push({
      name: 'Express Shipping',
      description: 'USPS Priority Express',
      price: expressPrice,
      estimatedDays: isLocalDelivery ? 'Next business day' : '1-2 business days'
    });

  } else if (country === 'CA') {
    // Canada shipping
    const baseRate = subtotal >= freeShippingThreshold ? 0 : 12.99;
    
    if (baseRate === 0) {
      rates.push({
        name: 'Free Standard Shipping',
        description: 'Free shipping on orders over $75',
        price: 0,
        estimatedDays: '7-14 business days'
      });
    } else {
      rates.push({
        name: 'Standard Shipping',
        description: 'Canada Post Regular',
        price: baseRate,
        estimatedDays: '7-14 business days'
      });
    }

    rates.push({
      name: 'Express Shipping',
      description: 'Canada Post Expedited',
      price: 24.99,
      estimatedDays: '3-5 business days'
    });

  } else {
    // International shipping
    let standardPrice = 19.99;
    let expressPrice = 39.99;

    // Adjust pricing for specific regions
    if (['GB', 'DE', 'FR'].includes(country)) {
      // Europe
      standardPrice = 16.99;
      expressPrice = 32.99;
    } else if (['AU', 'NZ'].includes(country)) {
      // Oceania
      standardPrice = 22.99;
      expressPrice = 42.99;
    } else if (['JP', 'KR', 'SG'].includes(country)) {
      // Asia
      standardPrice = 18.99;
      expressPrice = 36.99;
    }

    rates.push({
      name: 'International Standard',
      description: 'International shipping',
      price: standardPrice,
      estimatedDays: '10-21 business days'
    });

    rates.push({
      name: 'International Express',
      description: 'Express international shipping',
      price: expressPrice,
      estimatedDays: '5-10 business days'
    });
  }

  return rates;
}

// Estimate weight based on items (simplified)
function calculateTotalWeight(items: CartItem[]): number {
  // Assume each item weighs approximately 0.5 lbs on average
  const averageWeightPerItem = 0.5;
  return items.reduce((total, item) => total + (item.quantity * averageWeightPerItem), 0);
}

// Helper functions for zone-based shipping from Kansas (66048)
function isNearOrigin(postalCode?: string, state?: string): boolean {
  if (!state) return false;

  // Kansas and immediately surrounding areas
  const localStates = ['KS'];
  const localZips = ['660', '661', '662', '663', '664', '665', '666', '667', '668', '669'];

  if (localStates.includes(state)) return true;
  if (postalCode && localZips.some(zip => postalCode.startsWith(zip))) return true;

  return false;
}

function isZone1Delivery(state?: string): boolean {
  if (!state) return false;

  // Adjacent states and nearby regions
  const zone1States = ['MO', 'OK', 'NE', 'CO', 'AR', 'IA'];
  return zone1States.includes(state);
}

function isZone2Delivery(state?: string): boolean {
  if (!state) return false;

  // Mid-distance states
  const zone2States = ['TX', 'NM', 'WY', 'SD', 'ND', 'MN', 'WI', 'IL', 'IN', 'KY', 'TN', 'MS', 'AL', 'LA'];
  return zone2States.includes(state);
}

export async function POST(req: NextRequest) {
  try {
    const { address, items, subtotal }: ShippingCalculationRequest = await req.json();

    // Validate required fields
    if (!address || !address.country) {
      return NextResponse.json(
        { error: 'Address with country is required' },
        { status: 400 }
      );
    }

    if (!items || items.length === 0) {
      return NextResponse.json(
        { error: 'Cart items are required' },
        { status: 400 }
      );
    }

    // Calculate total weight
    const totalWeight = calculateTotalWeight(items);

    // Calculate shipping rates
    const shippingRates = calculateShippingRates(address, subtotal, totalWeight);

    return NextResponse.json({
      success: true,
      rates: shippingRates,
      address,
      subtotal,
      totalWeight: Math.round(totalWeight * 100) / 100, // Round to 2 decimal places
      originZip: ORIGIN_ZIP // Include origin for reference
    });

  } catch (error) {
    console.error('Error calculating shipping:', error);
    return NextResponse.json(
      { error: 'Error calculating shipping rates' },
      { status: 500 }
    );
  }
}
