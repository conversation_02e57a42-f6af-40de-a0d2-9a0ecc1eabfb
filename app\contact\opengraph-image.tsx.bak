import { ImageResponse } from "next/og";

export const alt = "Contact Us | <PERSON>'s Menagerie";
export const contentType = "image/png";
export const size = { width: 1200, height: 630 };

export default function OG() {
  return new ImageResponse(
    (
      <div
        style={{
          height: "100%",
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: "#f5f5f5",
          backgroundImage: "linear-gradient(to bottom right, #ffffff 25%, #f5f5f5 50%, #e0e0e0 75%)",
        }}
      >
        <div
          style={{
            fontSize: "60px",
            fontWeight: "bold",
            color: "#000000",
            marginBottom: "20px",
          }}
        >
          Contact Us
        </div>
        <div
          style={{
            fontSize: "32px",
            color: "#333333",
            textAlign: "center",
            maxWidth: "80%",
          }}
        >
          Connect with Milo's Menagerie
        </div>
      </div>
    ),
    size
  );
}