'use client';

import { helvetica, editorialNew } from '@/app/fonts';
import cx from 'classnames';
import ProductGrid from './product-grid';

interface ProductShowcaseProps {
  title?: string;
  subtitle?: string;
  onProductRequest?: (productId: string) => void;
}

export default function ProductShowcase({ 
  title = "Our Collection",
  onProductRequest 
}: ProductShowcaseProps) {
  
  return (
    <section className={cx(helvetica.variable, editorialNew.variable, 'py-8 md:py-12 bg-white')}>
      <div className="max-w-7xl mx-auto px-4 md:px-6">
        {/* Header */}
        <div className="text-left mb-8">
          <h2 className="text-3xl md:text-3xl text-gray-600 font-helvetica font-light max-w-2xl">
            {title}
          </h2>
        </div>

        {/* Product Grid */}
        <ProductGrid onProductRequest={onProductRequest} />
      </div>
    </section>
  );
}

