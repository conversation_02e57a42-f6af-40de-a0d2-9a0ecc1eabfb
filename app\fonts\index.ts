import localFont from "next/font/local";

export const editorialNew = localFont({
  src: [
    {
      path: "./PPEditorialNew-Ultralight-BF644b21500d0c0.otf",
      weight: "200",
      style: "normal",
    },
    {
      path: "./PPEditorialNew-Regular-BF644b214ff145f.otf",
      weight: "400",
      style: "normal",
    },
    {
      path: "./PPEditorialNew-Ultrabold-BF644b21500840c.otf",
      weight: "800",
      style: "normal",
    },
  ],
  variable: "--font-editorial-new",
  display: "swap",
});

export const helvetica = localFont({
  src: [
    {
      path: "./HelvThin.otf",
      weight: "100",
      style: "normal",
    },
    {
      path: "./HelvUltraLight.otf",
      weight: "200",
      style: "normal",
    },
    {
      path: "./HelvLight.otf",
      weight: "300",
      style: "normal",
    },
    {
      path: "./HelvRoman.otf",
      weight: "400",
      style: "normal",
    },
    {
      path: "./Helv.ttf",
      weight: "400",
      style: "normal",
    },
    {
      path: "./HelvMedium.otf",
      weight: "500",
      style: "normal",
    },
    {
      path: "./HelvBold.otf",
      weight: "700",
      style: "normal",
    },
    {
      path: "./HelvHeavy.otf",
      weight: "800",
      style: "normal",
    },
    {
      path: "./HelvBlack.otf",
      weight: "900",
      style: "normal",
    },
  ],
  variable: "--font-helvetica",
  display: "swap",
});
