'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { onAuthStateChanged, User } from 'firebase/auth';
import { auth } from '@/lib/firebase/config';
import { isUserAdmin, AdminUser } from '@/lib/auth/admin-firestore';
import ArtistApplications from '@/components/admin/ArtistApplications';

export default function ArtistApplicationsPage() {
  const [user, setUser] = useState<User | null>(null);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        const admin = await isUserAdmin(user);
        if (admin && (admin.role === 'super_admin' || admin.role === 'admin')) {
          setUser(user);
          setAdminUser(admin);
        } else {
          // Redirect artists and non-admins to their appropriate dashboard
          router.push('/admin');
        }
      } else {
        router.push('/admin');
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, [router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user || !adminUser) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-xl font-semibold">Artist Applications</h1>
            <a
              href="/admin"
              className="text-gray-600 hover:text-gray-800 underline"
            >
              ← Back to Admin Dashboard
            </a>
          </div>
        </div>
      </header>

      <ArtistApplications user={user} adminUser={adminUser} />
    </div>
  );
}
