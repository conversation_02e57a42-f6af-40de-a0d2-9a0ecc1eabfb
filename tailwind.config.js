/** @type {import('tailwindcss').Config} */
const plugin = require("tailwindcss/plugin");

module.exports = {
  content: ["./app/**/*.{js,ts,jsx,tsx}", "./components/**/*.{js,ts,jsx,tsx}"],
  future: {
    hoverOnlyWhenSupported: true,
  },
  theme: {
    extend: {
      colors: {
        brand: {
          100: '#C0E6E0', // Columbia Blue (lightest)
          300: '#73C1B9', // Moonstone Blue
          500: '#5DB8C6', // Sea Serpent
          600: '#43ADAD', // Verdigris
          700: '#1A80A9', // Celadon Blue
          900: '#0C5985', // Blue Sapphire (darkest)
        },
        custom: {
          'deep-blue': '#05014a',
          'royal-blue': '#020079',
          'bright-blue': '#0006b1',
          'vivid-blue': '#0013de',
          'electric-blue': '#0021f3',
        },
      },
      fontFamily: {
        sans: ["var(--font-helvetica)", "Helvetica", "Arial", "sans-serif"],
        helvetica: ["var(--font-helvetica)", "Helvetica", "Arial", "sans-serif"],
        editorial: ["var(--font-editorial-new)", "serif"],
        display: ["var(--font-editorial-new)", "serif"],
      },
      animation: {
        // Fade up and down
        "fade-up": "fade-up 0.5s ease-out forwards",
        "fade-down": "fade-down 0.5s ease-out forwards",
        // New animations
        "fade-left": "fade-left 0.5s ease-out forwards",
        "fade-right": "fade-right 0.5s ease-out forwards",
        // Tooltip
        "slide-up-fade": "slide-up-fade 0.3s cubic-bezier(0.16, 1, 0.3, 1)",
        "slide-down-fade": "slide-down-fade 0.3s cubic-bezier(0.16, 1, 0.3, 1)",
        // Fade in up
        "fade-in-up": "fade-in-up 0.7s ease-out forwards",
      },
      keyframes: {
        // Fade up and down
        "fade-up": {
          "0%": {
            opacity: 0,
            transform: "translateY(20px)",
          },
          "100%": {
            opacity: 1,
            transform: "translateY(0px)",
          },
        },
        "fade-down": {
          "0%": {
            opacity: 0,
            transform: "translateY(-20px)",
          },
          "100%": {
            opacity: 1,
            transform: "translateY(0px)",
          },
        },
        // New keyframes
        "fade-left": {
          "0%": {
            opacity: 0,
            transform: "translateX(-20px)",
          },
          "100%": {
            opacity: 1,
            transform: "translateX(0px)",
          },
        },
        "fade-right": {
          "0%": {
            opacity: 0,
            transform: "translateX(20px)",
          },
          "100%": {
            opacity: 1,
            transform: "translateX(0px)",
          },
        },
        // Tooltip
        "slide-up-fade": {
          "0%": { opacity: 0, transform: "translateY(6px)" },
          "100%": { opacity: 1, transform: "translateY(0)" },
        },
        "slide-down-fade": {
          "0%": { opacity: 0, transform: "translateY(-6px)" },
          "100%": { opacity: 1, transform: "translateY(0)" },
        },
        // Fade in up
        "fade-in-up": {
          "0%": {
            opacity: 0,
            transform: "translateY(20px)",
          },
          "100%": {
            opacity: 1,
            transform: "translateY(0px)",
          },
        },
      },
    },
  },
  plugins: [
    require("@tailwindcss/forms"),
    require("@tailwindcss/typography"),
    plugin(({ addVariant }) => {
      addVariant("radix-side-top", '&[data-side="top"]');
      addVariant("radix-side-bottom", '&[data-side="bottom"]');
    }),
  ],
};
