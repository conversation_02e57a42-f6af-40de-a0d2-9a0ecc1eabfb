'use client';

import { useState, useCallback, useRef } from 'react';
import { Upload, X, Image as ImageIcon } from 'lucide-react';
import { helvetica } from '@/app/fonts';
import cx from 'classnames';

interface ImageUploadProps {
  images: { url: string; isPrimary: boolean; alt?: string; file?: File }[];
  onImagesChange: (images: { url: string; isPrimary: boolean; alt?: string; file?: File }[]) => void;
  maxImages?: number;
  className?: string;
}

export default function ImageUpload({ 
  images, 
  onImagesChange, 
  maxImages = 5,
  className = ''
}: ImageUploadProps) {
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    
    if (imageFiles.length + images.length > maxImages) {
      alert(`You can only upload up to ${maxImages} images`);
      return;
    }
    
    addFiles(imageFiles);
  }, [images.length, maxImages]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    
    if (imageFiles.length + images.length > maxImages) {
      alert(`You can only upload up to ${maxImages} images`);
      return;
    }
    
    addFiles(imageFiles);
    
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [images.length, maxImages]);

  const addFiles = (files: File[]) => {
    const newImages = files.map((file, index) => {
      const url = URL.createObjectURL(file);
      return {
        url,
        isPrimary: images.length === 0 && index === 0, // First image is primary if no existing images
        alt: file.name.split('.')[0],
        file
      };
    });
    
    onImagesChange([...images, ...newImages]);
  };

  const removeImage = (index: number) => {
    const newImages = [...images];
    const removedImage = newImages[index];
    
    // Revoke object URL to prevent memory leaks
    if (removedImage.file) {
      URL.revokeObjectURL(removedImage.url);
    }
    
    newImages.splice(index, 1);
    
    // If we removed the primary image, make the first remaining image primary
    if (removedImage.isPrimary && newImages.length > 0) {
      newImages[0].isPrimary = true;
    }
    
    onImagesChange(newImages);
  };

  const setPrimary = (index: number) => {
    const newImages = images.map((img, i) => ({
      ...img,
      isPrimary: i === index
    }));
    onImagesChange(newImages);
  };

  const updateAlt = (index: number, alt: string) => {
    const newImages = [...images];
    newImages[index] = { ...newImages[index], alt };
    onImagesChange(newImages);
  };

  return (
    <div className={cx('space-y-4', className)}>
      {/* Upload Area */}
      <div
        onDrop={handleDrop}
        onDragOver={(e) => { e.preventDefault(); setDragOver(true); }}
        onDragLeave={() => setDragOver(false)}
        className={cx(
          'border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer',
          dragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
        )}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />
        
        <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <p className={cx(helvetica.className, 'text-lg font-medium text-gray-900 mb-2')}>
          Drop images here or click to browse
        </p>
        <p className={cx(helvetica.className, 'text-sm text-gray-500')}>
          PNG, JPG, GIF up to 10MB each. Max {maxImages} images.
        </p>
      </div>

      {/* Image Preview Grid */}
      {images.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {images.map((image, index) => (
            <div key={index} className="relative group">
              <div className="aspect-square relative rounded-lg overflow-hidden border border-gray-200">
                <img
                  src={image.url}
                  alt={image.alt || 'Product image'}
                  className="w-full h-full object-cover"
                />
                
                {/* Primary Badge */}
                {image.isPrimary && (
                  <div className="absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                    Primary
                  </div>
                )}
                
                {/* Remove Button */}
                <button
                  onClick={() => removeImage(index)}
                  className="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <X size={16} />
                </button>
                
                {/* Primary Button */}
                {!image.isPrimary && (
                  <button
                    onClick={() => setPrimary(index)}
                    className="absolute bottom-2 right-2 bg-black bg-opacity-70 hover:bg-opacity-90 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    Set Primary
                  </button>
                )}
              </div>
              
              {/* Alt Text Input */}
              <input
                type="text"
                placeholder="Image description"
                value={image.alt || ''}
                onChange={(e) => updateAlt(index, e.target.value)}
                className={cx(
                  helvetica.className,
                  'mt-2 w-full text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-black'
                )}
              />
            </div>
          ))}
        </div>
      )}
      
      {/* Upload Status */}
      {images.length > 0 && (
        <p className={cx(helvetica.className, 'text-sm text-gray-500')}>
          {images.length} of {maxImages} images uploaded
          {images.find(img => img.isPrimary) && (
            <span className="ml-2 text-blue-600">
              • Primary image selected
            </span>
          )}
        </p>
      )}
    </div>
  );
}
