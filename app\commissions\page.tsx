'use client';

import { useState } from 'react';
import { helvetica, editorialNew } from '@/app/fonts';
import cx from 'classnames';

interface FormData {
  name: string;
  email: string;
  phone: string;
  description: string;
  inspiration: string;
  timeline: string;
  budget: string;
}

export default function CommissionsPage() {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    description: '',
    inspiration: '',
    timeline: '',
    budget: ''
  });
  const [status, setStatus] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    setStatus('Submitting...');

    try {
      await fetch('/api/send-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          to: "<EMAIL>",
          ...formData,
          subject: `Studio Commission Request from ${formData.name}`
        }),
      });

      setStatus('Your commission request has been sent! We\'ll be in touch soon.');
      setFormData({
        name: '',
        email: '',
        phone: '',
        description: '',
        inspiration: '',
        timeline: '',
        budget: ''
      });
    } catch (err) {
      console.error(err);
      setStatus('Error sending request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={cx(helvetica.variable, editorialNew.variable, 'min-h-screen bg-white')}>
      <div className="max-w-4xl mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-5xl md:text-6xl lg:text-7xl font-editorial font-normal leading-[0.9] mb-6 text-black">
            Studio Commissions
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 font-helvetica font-light max-w-3xl mx-auto mb-8">
            Have something particular in mind? Let's bring your vision to life with a custom piece.
          </p>
          <div className="w-24 h-px bg-black mx-auto"></div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium mb-2 text-black font-helvetica">
                Name*
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="w-full p-4 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 font-helvetica"
                placeholder="Your full name"
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-2 text-black font-helvetica">
                Email*
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="w-full p-4 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 font-helvetica"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm font-medium mb-2 text-black font-helvetica">
              Phone
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              className="w-full p-4 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 font-helvetica"
              placeholder="(*************"
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium mb-2 text-black font-helvetica">
              Project Description*
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              required
              rows={4}
              className="w-full p-4 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 font-helvetica resize-none"
              placeholder="Describe your vision in detail. What animal, plant, or nature-inspired piece would you like created?"
            />
          </div>

          <div>
            <label htmlFor="inspiration" className="block text-sm font-medium mb-2 text-black font-helvetica">
              Inspiration & References
            </label>
            <textarea
              id="inspiration"
              name="inspiration"
              value={formData.inspiration}
              onChange={handleChange}
              rows={3}
              className="w-full p-4 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 font-helvetica resize-none"
              placeholder="Share any reference images, styles, or specific details that inspire your commission"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="timeline" className="block text-sm font-medium mb-2 text-black font-helvetica">
                Timeline
              </label>
              <select
                id="timeline"
                name="timeline"
                value={formData.timeline}
                onChange={handleChange}
                className="w-full p-4 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 font-helvetica"
              >
                <option value="">Select timeline</option>
                <option value="No rush">No rush</option>
                <option value="1-2 weeks">1-2 weeks</option>
                <option value="3-4 weeks">3-4 weeks</option>
                <option value="1-2 months">1-2 months</option>
                <option value="Flexible">Flexible</option>
              </select>
            </div>

            <div>
              <label htmlFor="budget" className="block text-sm font-medium mb-2 text-black font-helvetica">
                Budget Range
              </label>
              <select
                id="budget"
                name="budget"
                value={formData.budget}
                onChange={handleChange}
                className="w-full p-4 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 font-helvetica"
              >
                <option value="">Select budget</option>
                <option value="Under $50">Under $100</option>
                <option value="$100-$200">$100-$200</option>
                <option value="$200-$500">$200-$500</option>
                <option value="$500+">$500+</option>
                <option value="Let's discuss">Let's discuss</option>
              </select>
            </div>
          </div>

          <div className="text-center pt-6">
            <button
              type="submit"
              disabled={isSubmitting}
              className={`bg-black text-white font-helvetica font-medium py-4 px-12 rounded-xl transition-all duration-300 ${
                isSubmitting 
                  ? 'opacity-70 cursor-not-allowed' 
                  : 'hover:bg-gray-800 active:scale-95'
              }`}
            >
              {isSubmitting ? 'Sending Request...' : 'Submit Commission Request'}
            </button>
          </div>

          {status && (
            <div className="text-center mt-6">
              <p className={`font-helvetica ${
                status.includes('sent') ? 'text-green-600' : 
                status.includes('Error') ? 'text-red-600' : 
                'text-gray-600'
              }`}>
                {status}
              </p>
            </div>
          )}
        </form>

        {/* Additional Info */}
        <div className="mt-16 text-center">
          <div className="bg-gray-50 rounded-2xl p-8">
            <h3 className="text-2xl font-editorial font-normal mb-4 text-black">
              How It Works
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm font-helvetica text-gray-600">
              <div>
                <div className="w-8 h-8 bg-black text-white rounded-full flex items-center justify-center mx-auto mb-3 text-xs font-medium">1</div>
                <p>Submit your commission request with detailed description</p>
              </div>
              <div>
                <div className="w-8 h-8 bg-black text-white rounded-full flex items-center justify-center mx-auto mb-3 text-xs font-medium">2</div>
                <p>We'll discuss your vision, timeline, and provide a quote</p>
              </div>
              <div>
                <div className="w-8 h-8 bg-black text-white rounded-full flex items-center justify-center mx-auto mb-3 text-xs font-medium">3</div>
                <p>Your custom piece is designed, printed, and shipped to you</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}