// Migration script to create admin user in Firestore
// Run this script once to migrate from environment variables to Firestore
// Usage: node scripts/migrate-admin-to-firestore.js

const { initializeApp } = require('firebase/app');
const { getAuth, signInWithEmailAndPassword } = require('firebase/auth');
const { getFirestore, doc, setDoc, serverTimestamp } = require('firebase/firestore');

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
};

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

async function migrateAdminToFirestore() {
  try {
    const adminEmail = process.env.NEXT_PUBLIC_ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD;
    
    if (!adminPassword) {
      console.log('Please set ADMIN_PASSWORD environment variable');
      console.log('Example: ADMIN_PASSWORD=your_password node scripts/migrate-admin-to-firestore.js');
      process.exit(1);
    }
    
    console.log('🔄 Migrating admin to Firestore...');
    console.log('Admin email:', adminEmail);
    
    // Sign in to get the user UID
    let userCredential;
    try {
      userCredential = await signInWithEmailAndPassword(auth, adminEmail, adminPassword);
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        console.log('❌ Admin user not found. Please create the admin user first with create-admin.js');
        process.exit(1);
      }
      throw error;
    }
    
    const user = userCredential.user;
    console.log('✅ Admin user authenticated, UID:', user.uid);
    
    // Create admin document in Firestore
    const adminData = {
      email: adminEmail,
      displayName: 'Admin User',
      role: 'super_admin',
      permissions: ['*'], // All permissions
      isActive: true,
      createdAt: serverTimestamp(),
      createdBy: 'migration_script',
      metadata: {
        migratedFromEnv: true,
        migrationDate: new Date().toISOString()
      }
    };
    
    await setDoc(doc(db, 'admins', user.uid), adminData);
    
    console.log('✅ Admin user successfully migrated to Firestore!');
    console.log('📊 Admin data created:');
    console.log('  - UID:', user.uid);
    console.log('  - Email:', adminEmail);
    console.log('  - Role: super_admin');
    console.log('  - Permissions: All (*)');
    console.log('  - Status: Active');
    
    console.log('\n🎉 Migration complete! You can now:');
    console.log('1. Remove NEXT_PUBLIC_ADMIN_EMAIL from .env.local');
    console.log('2. Login to /admin using email/password or Google');
    console.log('3. Manage additional admins through the admin panel');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error migrating admin to Firestore:', error);
    process.exit(1);
  }
}

migrateAdminToFirestore();
