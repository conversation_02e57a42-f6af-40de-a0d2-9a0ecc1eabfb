import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase/config';
import { doc, getDoc } from 'firebase/firestore';
import nodemailer from 'nodemailer';

export async function POST(req: NextRequest) {
  try {
    const { invoiceId } = await req.json();

    if (!invoiceId) {
      return NextResponse.json(
        { error: 'Invoice ID is required' },
        { status: 400 }
      );
    }

    // Get invoice from Firebase
    const invoiceRef = doc(db, 'invoices', invoiceId);
    const invoiceSnap = await getDoc(invoiceRef);

    if (!invoiceSnap.exists()) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      );
    }

    const invoice = invoiceSnap.data();

    // Generate email content
    const emailContent = generateInvoiceEmail(invoice);

    // Create email transporter
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: Number(process.env.SMTP_PORT),
      secure: Number(process.env.SMTP_PORT) === 465,
      auth: {
        user: process.env.SMTP_USER!,
        pass: process.env.SMTP_PASS!,
      },
    });

    // Send the invoice email
    await transporter.sendMail({
      from: process.env.SMTP_USER,
      to: invoice.customerEmail,
      subject: emailContent.subject,
      html: emailContent.html,
    });

    console.log('📧 Invoice Email sent to:', invoice.customerEmail);

    return NextResponse.json({
      success: true,
      message: 'Invoice email sent successfully',
    });

  } catch (error) {
    console.error('Error sending invoice email:', error);
    return NextResponse.json(
      { error: 'Failed to send invoice email' },
      { status: 500 }
    );
  }
}

function generateInvoiceEmail(invoice: any) {
  const paymentLink = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/pay/${invoice.id}`;
  
  return {
    subject: `Invoice from Milo's Menagerie - ${invoice.title}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
          <h1 style="color: #333; margin: 0;">Invoice from Milo's Menagerie</h1>
        </div>
        
        <div style="padding: 20px;">
          <p>Hi ${invoice.customerName},</p>
          
          <p>You have received an invoice for <strong>$${invoice.amount.toFixed(2)}</strong></p>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #333;">${invoice.title}</h3>
            <p style="margin: 10px 0;"><strong>Amount:</strong> $${invoice.amount.toFixed(2)}</p>
            ${invoice.dueDate ? `<p style="margin: 10px 0;"><strong>Due Date:</strong> ${new Date(invoice.dueDate).toLocaleDateString()}</p>` : ''}
            
            <div style="margin: 15px 0;">
              <h4 style="margin-bottom: 5px;">Description:</h4>
              <p style="margin: 0; white-space: pre-wrap;">${invoice.description}</p>
            </div>
            
            ${invoice.notes ? `
              <div style="margin: 15px 0;">
                <h4 style="margin-bottom: 5px;">Notes:</h4>
                <p style="margin: 0; white-space: pre-wrap; font-style: italic;">${invoice.notes}</p>
              </div>
            ` : ''}
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${paymentLink}" 
               style="background-color: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
              View & Pay Invoice
            </a>
          </div>
          
          <p style="font-size: 14px; color: #666;">
            Click the button above to view your invoice details and make a secure payment. 
            You'll be able to enter your shipping information and pay with a credit card.
          </p>
          
          <div style="border-top: 1px solid #ddd; padding-top: 20px; margin-top: 30px;">
            <p style="margin: 0; font-size: 12px; color: #666;">
              <strong>Invoice ID:</strong> ${invoice.id}<br>
              <strong>Created:</strong> ${new Date().toLocaleDateString()}
            </p>
          </div>
          
          <div style="border-top: 1px solid #ddd; padding-top: 20px; margin-top: 20px; text-align: center; color: #666; font-size: 12px;">
            <p>Milo's Menagerie<br>
            If you have any questions about this invoice, please contact us.</p>
          </div>
        </div>
      </div>
    `
  };
}
