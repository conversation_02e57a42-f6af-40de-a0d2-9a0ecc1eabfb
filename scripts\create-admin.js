// Run this script once to create the admin user
// Usage: node scripts/create-admin.js

const { initializeApp } = require('firebase/app');
const { getAuth, createUserWithEmailAndPassword } = require('firebase/auth');

const firebaseConfig = {
  apiKey: "AIzaSyAQcsu53n9bjVEAyJIfXEWMy9FvTanNmNg",
  authDomain: "milos-jungle.firebaseapp.com",
  projectId: "milos-jungle",
  storageBucket: "milos-jungle.firebasestorage.app",
  messagingSenderId: "1078158771916",
  appId: "1:1078158771916:web:b912c35e6ddc5f1e70e469",
  measurementId: "G-TC63N14Q9D"
};

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

async function createAdminUser() {
  try {
    const email = '<EMAIL>';
    const password = 'AdminPassword123!'; // Change this to your desired password
    
    console.log('Creating admin user:', email);
    
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    console.log('✅ Admin user created successfully!');
    console.log('User ID:', userCredential.user.uid);
    console.log('Email:', userCredential.user.email);
    console.log('\nYou can now login to /admin with:');
    console.log('Email:', email);
    console.log('Password:', password);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
    
    if (error.code === 'auth/email-already-in-use') {
      console.log('✅ Admin user already exists! You can login with the existing password.');
    }
    
    process.exit(1);
  }
}

createAdminUser();
