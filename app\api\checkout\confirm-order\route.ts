import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { updateInventory } from '@/lib/firebase/orders';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
});

interface ShippingOption {
  id: string;
  name: string;
  description: string;
  price: number;
  estimatedDays: string;
}

interface CustomerInfo {
  email: string;
  phone: string;
  shippingAddress: {
    firstName: string;
    lastName: string;
    line1: string;
    line2: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  billingAddress: any;
  sameAsBilling: boolean;
}

export async function POST(req: NextRequest) {
  try {
    const {
      orderId,
      paymentIntentId,
      customerInfo,
      selectedShipping,
      items,
      subtotal
    }: {
      orderId: string;
      paymentIntentId: string;
      customerInfo: CustomerInfo;
      selectedShipping: ShippingOption;
      items: any[];
      subtotal: number;
    } = await req.json();

    // Verify payment intent was successful
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
    
    if (paymentIntent.status !== 'succeeded') {
      return NextResponse.json(
        { error: 'Payment not completed' },
        { status: 400 }
      );
    }

    // In production, you would:
    // 1. Save order to Firebase/database ✅
    // 2. Update inventory quantities
    // 3. Create shipping labels
    // 4. Send confirmation emails ✅

    // Save order to Firestore
    try {
      const orderRef = doc(db, 'orders', orderId);
      
      // Group items by artistId for multi-vendor support
      const artistOrders: { [artistId: string]: any[] } = {};
      const regularItems: any[] = [];
      
      items.forEach(item => {
        if (item.artistId) {
          if (!artistOrders[item.artistId]) {
            artistOrders[item.artistId] = [];
          }
          artistOrders[item.artistId].push(item);
        } else {
          regularItems.push(item);
        }
      });
      
      await setDoc(orderRef, {
        id: orderId,
        orderNumber: orderId,
        customerEmail: customerInfo.email,
        customerPhone: customerInfo.phone,
        shippingAddress: customerInfo.shippingAddress,
        billingAddress: customerInfo.billingAddress,
        sameAsBilling: customerInfo.sameAsBilling,
        items: items.map(item => ({
          id: item.id,
          title: item.title,
          description: item.description,
          imageUrl: item.imageUrl,
          price: item.price,
          quantity: item.quantity,
          total: item.price * item.quantity,
        })),
        subtotal,
        shippingCost: selectedShipping.price,
        shippingMethod: selectedShipping,
        total: paymentIntent.amount / 100,
        paymentIntentId,
        paymentStatus: 'paid',
        status: 'confirmed',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        artistOrders, // Track which items belong to which artists
        regularItems, // Items not associated with specific artists
      });
      
      // Create individual artist orders for better tracking
      for (const [artistId, artistItems] of Object.entries(artistOrders)) {
        const artistOrderId = `${orderId}_artist_${artistId}`;
        const artistOrderRef = doc(db, 'custom_orders', artistOrderId);
        
        const artistTotal = artistItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        
        await setDoc(artistOrderRef, {
          id: artistOrderId,
          type: 'regular', // vs 'custom' for invoices
          orderNumber: orderId,
          artistId,
          customerEmail: customerInfo.email,
          customerName: `${customerInfo.shippingAddress.firstName} ${customerInfo.shippingAddress.lastName}`,
          customerPhone: customerInfo.phone,
          items: artistItems.map(item => ({
            id: item.id,
            name: item.title,
            description: item.description,
            quantity: item.quantity,
            amount: item.price,
            imageUrl: item.imageUrl,
          })),
          amount: artistTotal,
          shippingAddress: customerInfo.shippingAddress,
          shippingMethod: selectedShipping,
          paymentStatus: 'paid',
          paymentIntentId,
          status: 'confirmed',
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        });
      }
      
      // Update inventory quantities
      try {
        await updateInventory(items.map(item => ({
          id: item.id,
          title: item.title,
          description: item.description,
          imageUrl: item.imageUrl,
          price: item.price,
          quantity: item.quantity,
          total: item.price * item.quantity,
        })));
        console.log('✅ Inventory updated successfully');
      } catch (inventoryError) {
        console.error('❌ Failed to update inventory:', inventoryError);
        // Don't fail the order if inventory update fails, but log it
      }
      
      console.log('✅ Order saved to Firestore:', orderId);
      
    } catch (dbError) {
      console.error('❌ Failed to save order to database:', dbError);
      // Don't fail the order if database save fails, but log it
    }

    // For now, we'll simulate these steps
    console.log('Order confirmed:', {
      orderId,
      paymentIntentId,
      customerInfo,
      selectedShipping,
      amount: paymentIntent.amount / 100,
    });

    // Send confirmation emails
    try {
      await fetch(`${req.headers.get('origin')}/api/send-receipt`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentIntentId,
          orderData: {
            orderId,
            customerInfo,
            selectedShipping,
            items: items || [],
            subtotal: subtotal || 0,
            amount: paymentIntent.amount / 100, // Convert from cents to dollars
          },
        }),
      });
    } catch (emailError) {
      console.error('Failed to send confirmation emails:', emailError);
      // Don't fail the order if emails fail
    }

    return NextResponse.json({
      success: true,
      orderId,
      message: 'Order confirmed successfully',
    });

  } catch (error) {
    console.error('Error confirming order:', error);
    return NextResponse.json(
      { error: 'Failed to confirm order' },
      { status: 500 }
    );
  }
}
