import { NextRequest, NextResponse } from 'next/server';
import { doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

export async function POST(req: NextRequest) {
  try {
    const { artistId } = await req.json();
    
    if (!artistId) {
      return NextResponse.json(
        { error: 'Artist ID is required' },
        { status: 400 }
      );
    }

    // Create a few test custom orders for the artist
    const testOrders = [
      {
        id: `custom_${Date.now()}_test1`,
        invoiceId: `inv_${Date.now()}_test1`,
        invoiceTitle: 'Custom Pet Portrait Commission',
        invoiceDescription: 'Custom 3D printed portrait of customer\'s cat with custom pose and colors',
        amount: 150.00,
        shippingCost: 15.00,
        totalAmount: 165.00,
        artistId,
        
        customerEmail: '<EMAIL>',
        customerName: '<PERSON>',
        customerPhone: '+****************',
        
        shippingAddress: {
          firstName: '<PERSON>',
          lastName: '<PERSON>',
          line1: '123 Main Street',
          line2: 'Apt 4B',
          city: 'New York',
          state: 'NY',
          postalCode: '10001',
          country: 'US'
        },
        
        shippingMethod: 'Standard Shipping',
        shippingDescription: '5-7 business days',
        estimatedDelivery: '5-7 business days',
        
        paymentIntentId: 'pi_test_123456789',
        paymentStatus: 'paid',
        status: 'confirmed',
        
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        notes: 'Customer requested orange tabby cat in sitting position'
      },
      {
        id: `custom_${Date.now()}_test2`,
        invoiceId: `inv_${Date.now()}_test2`,
        invoiceTitle: 'Custom Miniature Figurine Set',
        invoiceDescription: 'Set of 4 custom D&D character figurines based on provided character sheets',
        amount: 280.00,
        shippingCost: 20.00,
        totalAmount: 300.00,
        artistId,
        
        customerEmail: '<EMAIL>',
        customerName: 'Mike Chen',
        customerPhone: '+****************',
        
        shippingAddress: {
          firstName: 'Mike',
          lastName: 'Chen',
          line1: '456 Oak Avenue',
          city: 'San Francisco',
          state: 'CA',
          postalCode: '94102',
          country: 'US'
        },
        
        shippingMethod: 'Express Shipping',
        shippingDescription: '2-3 business days',
        estimatedDelivery: '2-3 business days',
        
        paymentIntentId: 'pi_test_987654321',
        paymentStatus: 'paid',
        status: 'processing',
        
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        notes: 'Customer provided detailed character descriptions and reference images',
        trackingNumber: 'UPS123456789'
      },
      {
        id: `custom_${Date.now()}_test3`,
        invoiceId: `inv_${Date.now()}_test3`,
        invoiceTitle: 'Wedding Cake Topper',
        invoiceDescription: 'Custom bride and groom figurines for wedding cake topper',
        amount: 120.00,
        shippingCost: 10.00,
        totalAmount: 130.00,
        artistId,
        
        customerEmail: '<EMAIL>',
        customerName: 'Jenny Martinez',
        customerPhone: '+****************',
        
        shippingAddress: {
          firstName: 'Jenny',
          lastName: 'Martinez',
          line1: '789 Pine Street',
          city: 'Austin',
          state: 'TX',
          postalCode: '73301',
          country: 'US'
        },
        
        shippingMethod: 'Standard Shipping',
        shippingDescription: '5-7 business days',
        estimatedDelivery: '5-7 business days',
        
        paymentIntentId: 'pi_test_456789123',
        paymentStatus: 'paid',
        status: 'shipped',
        
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        notes: 'Rush order for wedding on Saturday',
        trackingNumber: 'FEDEX987654321'
      }
    ];

    // Create the test orders
    for (const order of testOrders) {
      const orderRef = doc(db, 'custom_orders', order.id);
      await setDoc(orderRef, order);
    }

    return NextResponse.json({
      success: true,
      message: `Created ${testOrders.length} test orders for artist ${artistId}`,
      orders: testOrders.map(o => ({ id: o.id, title: o.invoiceTitle, status: o.status }))
    });

  } catch (error) {
    console.error('Error creating test orders:', error);
    return NextResponse.json(
      { error: 'Failed to create test orders' },
      { status: 500 }
    );
  }
}
