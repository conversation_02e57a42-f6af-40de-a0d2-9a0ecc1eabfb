import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'About Us | Inside Strategy',
  description: 'Inside Strategy is a team of expert business brokers specializing in business sales $500K-$10M. Learn about our experience and approach to maximizing value.',
  keywords: ['about business brokers', 'Inside Strategy team', 'business sale experts', 'business broker experience'],
  openGraph: {
    title: 'About Us | Inside Strategy',
    description: 'Inside Strategy is a team of expert business brokers specializing in business sales $500K-$10M.',
    url: 'https://insidestrategy.co/about',
  },
};

export default function AboutLayout({
  children,
}: {
  children: React.ReactNode;
}): React.ReactElement {
  return <>{children}</>;
}