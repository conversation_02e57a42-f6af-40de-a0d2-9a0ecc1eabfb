'use client';

import { useState, useEffect } from 'react';
import { ArtistOrder, getArtistOrders, updateOrderStatus, getArtistOrderStats } from '@/lib/firebase/artist-orders';
import { helvetica, editorialNew } from '@/app/fonts';
import cx from 'classnames';
import { Package, Truck, CheckCircle, Clock, AlertCircle, DollarSign, Eye, Edit3, MapPin, User, FileText, CreditCard } from 'lucide-react';

interface ArtistOrdersProps {
  artistId: string;
}

export default function ArtistOrders({ artistId }: ArtistOrdersProps) {
  const [orders, setOrders] = useState<ArtistOrder[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<ArtistOrder | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [updatingStatus, setUpdatingStatus] = useState(false);
  const [statusUpdate, setStatusUpdate] = useState({
    status: '',
    notes: '',
    trackingNumber: ''
  });

  useEffect(() => {
    loadOrders();
  }, [artistId]);

  const loadOrders = async () => {
    try {
      setLoading(true);
      const [ordersData, statsData] = await Promise.all([
        getArtistOrders(artistId),
        getArtistOrderStats(artistId)
      ]);
      setOrders(ordersData);
      setStats(statsData);
    } catch (error) {
      console.error('Error loading orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateStatus = async () => {
    if (!selectedOrder) return;
    
    try {
      setUpdatingStatus(true);
      await updateOrderStatus(
        selectedOrder.id,
        statusUpdate.status as ArtistOrder['status'],
        statusUpdate.notes || undefined,
        statusUpdate.trackingNumber || undefined
      );
      
      // Reload orders
      await loadOrders();
      
      // Reset form and close modal
      setShowStatusModal(false);
      setSelectedOrder(null);
      setStatusUpdate({ status: '', notes: '', trackingNumber: '' });
    } catch (error) {
      console.error('Error updating order status:', error);
      alert('Failed to update order status. Please try again.');
    } finally {
      setUpdatingStatus(false);
    }
  };

  const openStatusModal = (order: ArtistOrder) => {
    setSelectedOrder(order);
    setStatusUpdate({
      status: order.status,
      notes: order.notes || '',
      trackingNumber: order.trackingNumber || ''
    });
    setShowStatusModal(true);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed': return <CheckCircle className="text-blue-500" size={16} />;
      case 'shipped': return <Truck className="text-purple-500" size={16} />;
      case 'cancelled': return <AlertCircle className="text-red-500" size={16} />;
      default: return <Clock className="text-gray-500" size={16} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-blue-100 text-blue-800';
      case 'shipped': return 'bg-purple-100 text-purple-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredOrders = orders.filter(order => 
    statusFilter === 'all' || order.status === statusFilter
  );

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Orders</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <Package className="text-blue-500" size={24} />
            </div>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold">${stats.totalRevenue.toFixed(2)}</p>
              </div>
              <DollarSign className="text-green-500" size={24} />
            </div>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Avg Order Value</p>
                <p className="text-2xl font-bold">${stats.averageOrderValue.toFixed(2)}</p>
              </div>
              <DollarSign className="text-purple-500" size={24} />
            </div>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Processing</p>
                <p className="text-2xl font-bold">{stats.processing + stats.confirmed}</p>
              </div>
              <Clock className="text-orange-500" size={24} />
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="flex flex-wrap gap-2">
          {[
            { value: 'all', label: 'All Orders' },
            { value: 'confirmed', label: 'Confirmed' },
            { value: 'processing', label: 'Processing' },
            { value: 'shipped', label: 'Shipped' },
            { value: 'delivered', label: 'Delivered' }
          ].map((filter) => (
            <button
              key={filter.value}
              onClick={() => setStatusFilter(filter.value)}
              className={cx(
                "px-3 py-1 rounded-full text-sm font-medium transition-colors",
                statusFilter === filter.value
                  ? "bg-black text-white"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              )}
            >
              {filter.label}
            </button>
          ))}
        </div>
      </div>

      {/* Orders List */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-4 border-b">
          <h3 className={cx(editorialNew.className, "text-lg font-medium")}>
            Orders ({filteredOrders.length})
          </h3>
        </div>
        
        {filteredOrders.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            {statusFilter === 'all' ? 'No orders yet' : `No ${statusFilter} orders`}
          </div>
        ) : (
          <div className="divide-y">
            {filteredOrders.map((order) => (
              <div key={order.id} className="p-4 hover:bg-gray-50">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h4 className="font-medium">
                        {order.type === 'custom' ? order.invoiceTitle : `Order #${order.orderNumber || order.id.slice(-8)}`}
                      </h4>
                      
                      <span className={cx(
                        "px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1",
                        getStatusColor(order.status)
                      )}>
                        {getStatusIcon(order.status)}
                        <span className="capitalize">{order.status}</span>
                      </span>
                      
                      <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                        {order.type === 'custom' ? 'Custom' : 'Regular'}
                      </span>
                    </div>
                    
                    <div className="text-sm text-gray-600 space-y-1">
                      <p><strong>Customer:</strong> {order.customerName || order.customerEmail}</p>
                      <p><strong>Total:</strong> ${order.total.toFixed(2)}</p>
                      <p><strong>Date:</strong> {formatDate(order.createdAt)}</p>
                      
                      {order.items.length > 0 && (
                        <p><strong>Items:</strong> {order.items.map(item => `${item.name} (${item.quantity})`).join(', ')}</p>
                      )}
                      
                      {order.shippingAddress && (
                        <p className="flex items-start space-x-1">
                          <MapPin size={14} className="mt-0.5 flex-shrink-0" />
                          <span>
                            {order.shippingAddress.city}, {order.shippingAddress.state}, {order.shippingAddress.country}
                          </span>
                        </p>
                      )}
                      
                      {order.trackingNumber && (
                        <p><strong>Tracking:</strong> {order.trackingNumber}</p>
                      )}
                      
                      {order.notes && (
                        <p><strong>Notes:</strong> {order.notes}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setSelectedOrder(order)}
                      className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded"
                      title="View Details"
                    >
                      <Eye size={16} />
                    </button>
                    
                    <button
                      onClick={() => openStatusModal(order)}
                      className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded"
                      title="Update Status"
                    >
                      <Edit3 size={16} />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {selectedOrder && !showStatusModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-6">
                <h3 className={cx(editorialNew.className, "text-xl font-medium")}>
                  {selectedOrder.type === 'custom' ? selectedOrder.invoiceTitle : `Order #${selectedOrder.orderNumber || selectedOrder.id.slice(-8)}`}
                </h3>
                <button
                  onClick={() => setSelectedOrder(null)}
                  className="text-gray-400 hover:text-gray-600 text-xl"
                >
                  ✕
                </button>
              </div>
              
              <div className="space-y-6">
                {/* Order Summary */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">${selectedOrder.total.toFixed(2)}</p>
                    <p className="text-sm text-gray-600">Total Amount</p>
                  </div>
                  <div className="text-center">
                    <p className={cx("text-lg font-medium px-3 py-1 rounded-full inline-block", getStatusColor(selectedOrder.status))}>
                      {selectedOrder.status.toUpperCase()}
                    </p>
                    <p className="text-sm text-gray-600 mt-1">Order Status</p>
                  </div>
                  <div className="text-center">
                    <p className="text-lg font-medium">{selectedOrder.items.length} {selectedOrder.items.length === 1 ? 'Item' : 'Items'}</p>
                    <p className="text-sm text-gray-600">Order Items</p>
                  </div>
                </div>

                {/* Customer Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold mb-3 text-lg flex items-center">
                      <User size={18} className="mr-2" />
                      Customer Information
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-600">Name:</span>
                        <span>{selectedOrder.customerName || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-600">Email:</span>
                        <span className="text-blue-600">{selectedOrder.customerEmail}</span>
                      </div>
                      {selectedOrder.customerPhone && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-600">Phone:</span>
                          <span>{selectedOrder.customerPhone}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold mb-3 text-lg flex items-center">
                      <FileText size={18} className="mr-2" />
                      Order Details
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-600">Order ID:</span>
                        <span className="font-mono text-xs">{selectedOrder.id}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-600">Type:</span>
                        <span className="capitalize">{selectedOrder.type === 'custom' ? 'Custom Order' : 'Regular Order'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-600">Date:</span>
                        <span>{formatDate(selectedOrder.createdAt)}</span>
                      </div>
                      {selectedOrder.updatedAt && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-600">Last Updated:</span>
                          <span>{formatDate(selectedOrder.updatedAt)}</span>
                        </div>
                      )}
                      {selectedOrder.paidAt && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-600">Paid At:</span>
                          <span>{formatDate(selectedOrder.paidAt)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Payment Information */}
                <div className="border rounded-lg p-4">
                  <h4 className="font-semibold mb-3 text-lg flex items-center">
                    <CreditCard size={18} className="mr-2" />
                    Payment Information
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-600">Payment Status:</span>
                        <span className={cx("px-2 py-1 rounded text-xs font-medium", 
                          selectedOrder.paymentStatus === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        )}>
                          {selectedOrder.paymentStatus?.toUpperCase() || 'PAID'}
                        </span>
                      </div>
                      {selectedOrder.paymentIntentId && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-600">Payment ID:</span>
                          <span className="font-mono text-xs">{selectedOrder.paymentIntentId}</span>
                        </div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-600">Subtotal:</span>
                        <span>${selectedOrder.subtotal.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-600">Shipping:</span>
                        <span>${selectedOrder.shippingCost.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between border-t pt-2 font-semibold">
                        <span>Total:</span>
                        <span>${selectedOrder.total.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Order Items */}
                <div className="border rounded-lg p-4">
                  <h4 className="font-semibold mb-3 text-lg flex items-center">
                    <Package size={18} className="mr-2" />
                    Order Items
                  </h4>
                  <div className="space-y-3">
                    {selectedOrder.items.map((item, index) => (
                      <div key={index} className="flex justify-between items-start p-3 bg-gray-50 rounded border">
                        <div className="flex-1">
                          <div className="flex items-start space-x-3">
                            {item.imageUrl && (
                              <img 
                                src={item.imageUrl} 
                                alt={item.name}
                                className="w-16 h-16 object-cover rounded"
                              />
                            )}
                            <div>
                              <p className="font-medium">{item.name}</p>
                              {item.description && (
                                <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                              )}
                              <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
                                <span>Qty: {item.quantity}</span>
                                <span>Price: ${item.amount.toFixed(2)}</span>
                                <span className="font-medium">Total: ${(item.amount * item.quantity).toFixed(2)}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Shipping Information */}
                {selectedOrder.shippingAddress && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="border rounded-lg p-4">
                      <h4 className="font-semibold mb-3 text-lg flex items-center">
                        <MapPin size={18} className="mr-2" />
                        Shipping Address
                      </h4>
                      <div className="p-3 bg-gray-50 rounded text-sm space-y-1">
                        <p className="font-medium">
                          {selectedOrder.shippingAddress.name || 
                           `${selectedOrder.shippingAddress.firstName || ''} ${selectedOrder.shippingAddress.lastName || ''}`.trim()}
                        </p>
                        <p>{selectedOrder.shippingAddress.line1}</p>
                        {selectedOrder.shippingAddress.line2 && <p>{selectedOrder.shippingAddress.line2}</p>}
                        <p>
                          {selectedOrder.shippingAddress.city}, {selectedOrder.shippingAddress.state} {selectedOrder.shippingAddress.postalCode || selectedOrder.shippingAddress.postal_code}
                        </p>
                        <p>{selectedOrder.shippingAddress.country}</p>
                      </div>
                    </div>

                    <div className="border rounded-lg p-4">
                      <h4 className="font-semibold mb-3 text-lg flex items-center">
                        <Truck size={18} className="mr-2" />
                        Shipping Details
                      </h4>
                      <div className="space-y-2 text-sm">
                        {selectedOrder.shippingMethod && (
                          <>
                            <div className="flex justify-between">
                              <span className="font-medium text-gray-600">Method:</span>
                              <span>{selectedOrder.shippingMethod.name || 'Standard Shipping'}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="font-medium text-gray-600">Cost:</span>
                              <span>${selectedOrder.shippingCost.toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="font-medium text-gray-600">Estimated Days:</span>
                              <span>{selectedOrder.shippingMethod.estimatedDays || '3-5 business days'}</span>
                            </div>
                          </>
                        )}
                        {selectedOrder.trackingNumber && (
                          <div className="flex justify-between">
                            <span className="font-medium text-gray-600">Tracking:</span>
                            <span className="font-mono text-xs">{selectedOrder.trackingNumber}</span>
                          </div>
                        )}
                        {selectedOrder.estimatedDelivery && (
                          <div className="flex justify-between">
                            <span className="font-medium text-gray-600">Est. Delivery:</span>
                            <span>{selectedOrder.estimatedDelivery}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Additional Information */}
                {(selectedOrder.notes || selectedOrder.invoiceDescription) && (
                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold mb-3 text-lg flex items-center">
                      <FileText size={18} className="mr-2" />
                      Additional Information
                    </h4>
                    {selectedOrder.notes && (
                      <div className="mb-3">
                        <p className="font-medium text-gray-600 mb-1">Order Notes:</p>
                        <p className="text-sm bg-gray-50 p-3 rounded">{selectedOrder.notes}</p>
                      </div>
                    )}
                    {selectedOrder.invoiceDescription && (
                      <div>
                        <p className="font-medium text-gray-600 mb-1">Invoice Description:</p>
                        <p className="text-sm bg-gray-50 p-3 rounded">{selectedOrder.invoiceDescription}</p>
                      </div>
                    )}
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex space-x-3 pt-4 border-t">
                  <button
                    onClick={() => openStatusModal(selectedOrder)}
                    className="flex-1 bg-black text-white py-2 px-4 rounded hover:bg-gray-800 transition-colors"
                  >
                    Update Order Status
                  </button>
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(selectedOrder.id);
                      // You could add a toast notification here
                    }}
                    className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50 transition-colors"
                  >
                    Copy Order ID
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Status Update Modal */}
      {showStatusModal && selectedOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <h3 className={cx(editorialNew.className, "text-lg font-medium")}>
                  Update Order Status
                </h3>
                <button
                  onClick={() => setShowStatusModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Status</label>
                  <select
                    value={statusUpdate.status}
                    onChange={(e) => setStatusUpdate(prev => ({ ...prev, status: e.target.value }))}
                    className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-black"
                  >
                    <option value="confirmed">Confirmed</option>
                    <option value="shipped">Shipped</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Tracking Number (optional)</label>
                  <input
                    type="text"
                    value={statusUpdate.trackingNumber}
                    onChange={(e) => setStatusUpdate(prev => ({ ...prev, trackingNumber: e.target.value }))}
                    className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-black"
                    placeholder="Enter tracking number"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Notes (optional)</label>
                  <textarea
                    value={statusUpdate.notes}
                    onChange={(e) => setStatusUpdate(prev => ({ ...prev, notes: e.target.value }))}
                    className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-black"
                    rows={3}
                    placeholder="Add any notes about this order..."
                  />
                </div>
                
                <div className="flex space-x-3 pt-4">
                  <button
                    onClick={() => setShowStatusModal(false)}
                    className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleUpdateStatus}
                    disabled={updatingStatus || !statusUpdate.status}
                    className="flex-1 px-4 py-2 bg-black text-white rounded hover:bg-gray-800 transition-colors disabled:opacity-50"
                  >
                    {updatingStatus ? 'Updating...' : 'Update Status'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
