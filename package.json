{"name": "precedent", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "format:write": "prettier --write \"**/*.{css,js,json,jsx,ts,tsx}\"", "format": "prettier \"**/*.{css,js,json,jsx,ts,tsx}\"", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^5.6.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.0.7", "@stripe/react-stripe-js": "^3.9.0", "@stripe/stripe-js": "^7.8.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "18.11.18", "@types/react": "18.2.24", "@types/react-dom": "18.2.8", "@types/three": "^0.176.0", "@vercel/analytics": "^1.1.1", "@vercel/og": "^0.0.26", "@vercel/speed-insights": "^1.0.0", "class-variance-authority": "^0.7.1", "classnames": "^2.3.2", "clsx": "^2.0.0", "eslint": "8.31.0", "eslint-config-next": "13.1.1", "firebase": "^12.0.0", "focus-trap-react": "^10.2.2", "framer-motion": "^8.5.5", "jsonwebtoken": "^9.0.2", "lucide-react": "0.105.0-alpha.4", "ms": "^2.1.3", "next": "^15.2.0", "nodemailer": "^7.0.3", "react": "18.3.1", "react-dom": "18.3.1", "react-google-recaptcha": "^3.1.0", "react-markdown": "^8.0.7", "react-rough-notation": "^1.0.5", "stripe": "^18.3.0", "tailwind-merge": "^1.14.0", "three": "^0.176.0", "typescript": "5.2.2", "use-debounce": "^9.0.4", "vaul": "^0.6.8"}, "devDependencies": {"@tailwindcss/forms": "^0.5.6", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.10", "@types/ms": "^0.7.32", "@types/nodemailer": "^6.4.17", "@types/react-google-recaptcha": "^2.1.9", "autoprefixer": "^10.4.16", "concurrently": "^7.6.0", "postcss": "^8.4.31", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.2.8", "tailwindcss": "^3.3.3"}, "packageManager": "pnpm@9.3.0+sha512.ee7b93e0c2bd11409c6424f92b866f31d3ea1bef5fbe47d3c7500cdc3c9668833d2e55681ad66df5b640c61fa9dc25d546efa54d76d7f8bf54b13614ac293631"}