import { useRef } from 'react';
import useIntersectionObserver from '@/lib/hooks/use-intersection-observer';

export default function YourComponent() {
  const elementRef = useRef<HTMLDivElement>(null);
  const isVisible = useIntersectionObserver(elementRef, { 
    threshold: 0.2,
    freezeOnceVisible: true 
  });
  
  return (
    <div 
      ref={elementRef}
      className={`transition-all duration-700 ${
        isVisible?.isIntersecting 
          ? "opacity-100 translate-y-0" 
          : "opacity-0 translate-y-10"
      }`}
    >
      Your content here
    </div>
  );
}