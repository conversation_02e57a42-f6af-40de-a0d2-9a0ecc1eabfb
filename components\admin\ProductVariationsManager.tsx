'use client';

import { useState } from 'react';
import { Plus, Edit2, Trash2, Save, X } from 'lucide-react';
import { ProductVariation, ProductVariationValue } from '@/lib/firebase/products';

interface ProductVariationsManagerProps {
  variations: ProductVariation[];
  onAddVariation: (variation: Omit<ProductVariation, 'id'>) => void;
  onUpdateVariation: (variationId: string, updates: Partial<ProductVariation>) => void;
  onDeleteVariation: (variationId: string) => void;
}

export default function ProductVariationsManager({
  variations,
  onAddVariation,
  onUpdateVariation,
  onDeleteVariation
}: ProductVariationsManagerProps) {
  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newVariation, setNewVariation] = useState<Omit<ProductVariation, 'id'>>({
    name: '',
    values: [],
    required: false
  });

  const handleAddVariation = () => {
    if (!newVariation.name.trim() || newVariation.values.length === 0) return;

    onAddVariation(newVariation);
    setNewVariation({ name: '', values: [], required: false });
    setIsAdding(false);
  };

  const handleAddValueToNewVariation = () => {
    const newValue: ProductVariationValue = {
      id: `val_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
      value: '',
      priceModifier: 0
    };
    setNewVariation({
      ...newVariation,
      values: [...newVariation.values, newValue]
    });
  };

  const handleUpdateValueInNewVariation = (valueId: string, updates: Partial<ProductVariationValue>) => {
    setNewVariation({
      ...newVariation,
      values: newVariation.values.map(val => 
        val.id === valueId ? { ...val, ...updates } : val
      )
    });
  };

  const handleRemoveValueFromNewVariation = (valueId: string) => {
    setNewVariation({
      ...newVariation,
      values: newVariation.values.filter(val => val.id !== valueId)
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-helvetica font-medium text-black">
          Product Variations
        </h3>
        <button
          onClick={() => setIsAdding(true)}
          className="flex items-center gap-2 bg-black text-white px-3 py-2 rounded text-sm hover:bg-gray-800 transition-colors"
        >
          <Plus size={14} />
          Add Variation
        </button>
      </div>

      {/* Add New Variation Form */}
      {isAdding && (
        <div className="bg-gray-50 p-4 rounded-lg border">
          <h4 className="font-helvetica font-medium mb-4">Add New Variation</h4>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-helvetica font-medium text-gray-700 mb-1">
                  Variation Name *
                </label>
                <input
                  type="text"
                  value={newVariation.name}
                  onChange={(e) => setNewVariation({ ...newVariation, name: e.target.value })}
                  placeholder="e.g., Metal Color, Size, Finish"
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="required"
                  checked={newVariation.required}
                  onChange={(e) => setNewVariation({ ...newVariation, required: e.target.checked })}
                  className="rounded"
                />
                <label htmlFor="required" className="text-sm font-helvetica text-gray-700">
                  Required selection
                </label>
              </div>
            </div>

            {/* Variation Values */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="block text-sm font-helvetica font-medium text-gray-700">
                  Variation Options
                </label>
                <button
                  onClick={handleAddValueToNewVariation}
                  className="flex items-center gap-1 text-sm text-black hover:bg-gray-100 px-2 py-1 rounded"
                >
                  <Plus size={12} />
                  Add Option
                </button>
              </div>
              <div className="space-y-2">
                {newVariation.values.map((value) => (
                  <div key={value.id} className="grid grid-cols-3 gap-2 items-center">
                    <input
                      type="text"
                      value={value.value}
                      onChange={(e) => handleUpdateValueInNewVariation(value.id, { value: e.target.value })}
                      placeholder="e.g., Gold, Silver"
                      className="p-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-black"
                    />
                    <input
                      type="number"
                      value={value.priceModifier || 0}
                      onChange={(e) => handleUpdateValueInNewVariation(value.id, { priceModifier: parseFloat(e.target.value) || 0 })}
                      placeholder="Price modifier"
                      step="0.01"
                      className="p-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-black"
                    />
                    <button
                      onClick={() => handleRemoveValueFromNewVariation(value.id)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded"
                    >
                      <Trash2 size={14} />
                    </button>
                  </div>
                ))}
                {newVariation.values.length === 0 && (
                  <p className="text-sm text-gray-500 italic">
                    Add at least one option for this variation
                  </p>
                )}
              </div>
            </div>

            <div className="flex gap-2">
              <button
                onClick={handleAddVariation}
                disabled={!newVariation.name.trim() || newVariation.values.length === 0}
                className="flex items-center gap-2 bg-black text-white px-4 py-2 rounded hover:bg-gray-800 disabled:bg-gray-400 transition-colors"
              >
                <Save size={16} />
                Save Variation
              </button>
              <button
                onClick={() => {
                  setIsAdding(false);
                  setNewVariation({ name: '', values: [], required: false });
                }}
                className="flex items-center gap-2 bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition-colors"
              >
                <X size={16} />
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Existing Variations */}
      <div className="space-y-4">
        {variations.length === 0 ? (
          <div className="text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
            <p className="text-gray-500 font-helvetica">No variations added yet.</p>
            <p className="text-sm text-gray-400 mt-1">Add variations like color, size, or material options.</p>
          </div>
        ) : (
          variations.map((variation) => (
            <VariationCard
              key={variation.id}
              variation={variation}
              isEditing={editingId === variation.id}
              onEdit={() => setEditingId(variation.id)}
              onCancelEdit={() => setEditingId(null)}
              onUpdate={(updates) => onUpdateVariation(variation.id, updates)}
              onDelete={() => onDeleteVariation(variation.id)}
            />
          ))
        )}
      </div>
    </div>
  );
}

interface VariationCardProps {
  variation: ProductVariation;
  isEditing: boolean;
  onEdit: () => void;
  onCancelEdit: () => void;
  onUpdate: (updates: Partial<ProductVariation>) => void;
  onDelete: () => void;
}

function VariationCard({ variation, isEditing, onEdit, onCancelEdit, onUpdate, onDelete }: VariationCardProps) {
  const [editForm, setEditForm] = useState({
    name: variation.name,
    required: variation.required,
    values: [...variation.values]
  });

  const handleSave = () => {
    onUpdate(editForm);
  };

  const handleCancel = () => {
    setEditForm({
      name: variation.name,
      required: variation.required,
      values: [...variation.values]
    });
    onCancelEdit();
  };

  const handleAddValue = () => {
    const newValue: ProductVariationValue = {
      id: `val_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
      value: '',
      priceModifier: 0
    };
    setEditForm({
      ...editForm,
      values: [...editForm.values, newValue]
    });
  };

  const handleUpdateValue = (valueId: string, updates: Partial<ProductVariationValue>) => {
    setEditForm({
      ...editForm,
      values: editForm.values.map(val => 
        val.id === valueId ? { ...val, ...updates } : val
      )
    });
  };

  const handleRemoveValue = (valueId: string) => {
    setEditForm({
      ...editForm,
      values: editForm.values.filter(val => val.id !== valueId)
    });
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      {isEditing ? (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-helvetica font-medium text-gray-700 mb-1">
                Variation Name *
              </label>
              <input
                type="text"
                value={editForm.name}
                onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-black"
              />
            </div>
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id={`required-${variation.id}`}
                checked={editForm.required}
                onChange={(e) => setEditForm({ ...editForm, required: e.target.checked })}
                className="rounded"
              />
              <label htmlFor={`required-${variation.id}`} className="text-sm font-helvetica text-gray-700">
                Required selection
              </label>
            </div>
          </div>

          <div>
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm font-helvetica font-medium text-gray-700">
                Variation Options
              </label>
              <button
                onClick={handleAddValue}
                className="flex items-center gap-1 text-sm text-black hover:bg-gray-100 px-2 py-1 rounded"
              >
                <Plus size={12} />
                Add Option
              </button>
            </div>
            <div className="space-y-2">
              {editForm.values.map((value) => (
                <div key={value.id} className="grid grid-cols-3 gap-2 items-center">
                  <input
                    type="text"
                    value={value.value}
                    onChange={(e) => handleUpdateValue(value.id, { value: e.target.value })}
                    placeholder="Option name"
                    className="p-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-black"
                  />
                  <input
                    type="number"
                    value={value.priceModifier || 0}
                    onChange={(e) => handleUpdateValue(value.id, { priceModifier: parseFloat(e.target.value) || 0 })}
                    placeholder="Price modifier"
                    step="0.01"
                    className="p-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-black"
                  />
                  <button
                    onClick={() => handleRemoveValue(value.id)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded"
                  >
                    <Trash2 size={14} />
                  </button>
                </div>
              ))}
            </div>
          </div>

          <div className="flex gap-2">
            <button
              onClick={handleSave}
              disabled={!editForm.name.trim() || editForm.values.length === 0}
              className="flex items-center gap-2 bg-black text-white px-3 py-1 rounded text-sm hover:bg-gray-800 disabled:bg-gray-400 transition-colors"
            >
              <Save size={14} />
              Save
            </button>
            <button
              onClick={handleCancel}
              className="flex items-center gap-2 bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600 transition-colors"
            >
              <X size={14} />
              Cancel
            </button>
          </div>
        </div>
      ) : (
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="font-helvetica font-medium text-lg text-black">
                {variation.name}
              </h3>
              {variation.required && (
                <span className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded">
                  Required
                </span>
              )}
            </div>
            <div className="flex flex-wrap gap-2">
              {variation.values.map((value) => (
                <span
                  key={value.id}
                  className="inline-flex items-center gap-1 bg-gray-100 text-gray-700 px-2 py-1 rounded text-sm"
                >
                  {value.value}
                  {(value.priceModifier || 0) !== 0 && (
                    <span className="text-xs text-gray-500">
                      ({(value.priceModifier || 0) > 0 ? '+' : ''}${(value.priceModifier || 0).toFixed(2)})
                    </span>
                  )}
                </span>
              ))}
            </div>
          </div>
          <div className="flex gap-2 ml-4">
            <button
              onClick={onEdit}
              className="p-2 text-gray-600 hover:text-black hover:bg-gray-100 rounded transition-colors"
              title="Edit variation"
            >
              <Edit2 size={16} />
            </button>
            <button
              onClick={onDelete}
              className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
              title="Delete variation"
            >
              <Trash2 size={16} />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
