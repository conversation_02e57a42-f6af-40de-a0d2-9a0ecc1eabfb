"use client";

import { Dispatch, SetStateAction, useEffect } from "react";
import { motion } from "framer-motion";
import { X } from "lucide-react";

interface LeafletProps {
  children: React.ReactNode;
  setShow: Dispatch<SetStateAction<boolean>>;
}

export default function Leaflet({ children, setShow }: LeafletProps) {
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        setShow(false);
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [setShow]);

  return (
    <motion.div
      className="fixed inset-0 z-50 flex flex-col bg-black bg-opacity-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={() => setShow(false)}
    >
      <motion.div
        className="bg-white rounded-t-2xl h-full overflow-y-auto relative"
        initial={{ y: "100%" }}
        animate={{ y: 0 }}
        exit={{ y: "100%" }}
        transition={{ type: "spring", damping: 30, stiffness: 300 }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Mobile close button */}
        <button
          onClick={() => setShow(false)}
          className="absolute top-4 right-4 z-10 p-2 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
        >
          <X size={20} className="text-gray-600" />
        </button>
        
        {children}
      </motion.div>
    </motion.div>
  );
}




