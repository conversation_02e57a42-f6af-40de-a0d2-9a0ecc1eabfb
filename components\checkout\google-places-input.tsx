'use client';

import { useState, useEffect, useRef } from 'react';
import { MapPin } from 'lucide-react';

interface AddressData {
  line1: string;
  line2: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

interface GooglePlacesInputProps {
  onAddressSelect: (address: AddressData) => void;
  placeholder?: string;
}

declare global {
  interface Window {
    google: any;
    initGooglePlaces: () => void;
  }
}

export default function GooglePlacesInput({
  onAddressSelect,
  placeholder = "Start typing your address..."
}: GooglePlacesInputProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const autocompleteRef = useRef<HTMLInputElement>(null);
  const autocompleteInstance = useRef<any>(null);

  useEffect(() => {
    // Check if Google Places API is already loaded
    if (window.google && window.google.maps && window.google.maps.places) {
      setIsLoaded(true);
      return;
    }

    // Check if script is already being loaded
    const existingScript = document.querySelector('script[src*="maps.googleapis.com"]');
    if (existingScript) {
      // Script exists, wait for it to load
      const checkLoaded = () => {
        if (window.google && window.google.maps && window.google.maps.places) {
          setIsLoaded(true);
        } else {
          setTimeout(checkLoaded, 100);
        }
      };
      checkLoaded();
      return;
    }

    // Load Google Places API for the first time
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyD4gsHevyhIxApmvBF0PQYUM4ZytptVX-E&libraries=places&callback=initGooglePlaces`;
    script.async = true;
    script.defer = true;
    
    window.initGooglePlaces = () => {
      setIsLoaded(true);
    };
    
    document.head.appendChild(script);
  }, []);

  useEffect(() => {
    if (isLoaded && autocompleteRef.current && !autocompleteInstance.current) {
      autocompleteInstance.current = new window.google.maps.places.Autocomplete(
        autocompleteRef.current,
        {
          types: ['address'],
          componentRestrictions: { country: ['us', 'ca', 'gb', 'au', 'de', 'fr', 'jp'] }
        }
      );

      autocompleteInstance.current.addListener('place_changed', () => {
        const place = autocompleteInstance.current.getPlace();
        
        if (place.address_components) {
          const addressComponents = place.address_components;
          const addressData: AddressData = {
            line1: '',
            line2: '',
            city: '',
            state: '',
            postalCode: '',
            country: 'US'
          };

          // Parse address components
          addressComponents.forEach((component: any) => {
            const types = component.types;
            
            if (types.includes('street_number')) {
              addressData.line1 = component.long_name + ' ' + addressData.line1;
            }
            if (types.includes('route')) {
              addressData.line1 = addressData.line1 + component.long_name;
            }
            if (types.includes('locality')) {
              addressData.city = component.long_name;
            }
            if (types.includes('administrative_area_level_1')) {
              addressData.state = component.short_name;
            }
            if (types.includes('postal_code')) {
              addressData.postalCode = component.long_name;
            }
            if (types.includes('country')) {
              addressData.country = component.short_name;
            }
          });

          // Clean up line1
          addressData.line1 = addressData.line1.trim();

          console.log('Google Places selected address:', addressData);
          
          // Update the input value to show the selected address
          setInputValue(place.formatted_address || '');
          
          // Send the parsed address data to parent
          onAddressSelect(addressData);
        }
      });
    }
  }, [isLoaded, onAddressSelect]);

  return (
    <div>
      <label className="block text-sm font-medium mb-1 flex items-center">
        <MapPin size={16} className="mr-1" />
        Address Lookup {isLoaded && <span className="text-xs text-gray-500 ml-2">(Start typing for suggestions)</span>}
      </label>
      <input
        ref={autocompleteRef}
        type="text"
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        className="w-full p-3 border border-gray-300 rounded-lg text-base focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
        placeholder={placeholder}
        style={{ fontSize: '16px' }}
      />
      <p className="text-xs text-gray-500 mt-1">
        Select an address from the dropdown to auto-fill the form below
      </p>
    </div>
  );
}
