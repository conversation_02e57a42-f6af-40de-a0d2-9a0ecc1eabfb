'use client';

import { Truck, Gift } from 'lucide-react';

interface FreeShippingProgressProps {
  subtotal: number;
  threshold?: number;
}

export default function FreeShippingProgress({ subtotal, threshold = 75 }: FreeShippingProgressProps) {
  const remaining = Math.max(0, threshold - subtotal);
  const progress = Math.min(100, (subtotal / threshold) * 100);
  const isEligible = subtotal >= threshold;

  if (isEligible) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-3">
        <div className="flex items-center space-x-2 text-green-700">
          <Gift size={16} />
          <span className="font-helvetica text-sm font-medium">
            🎉 You qualify for free shipping!
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <Truck size={16} className="text-blue-600" />
          <span className="font-helvetica text-sm font-medium text-blue-700">
            Free Shipping
          </span>
        </div>
        <span className="font-helvetica text-xs text-blue-600">
          ${remaining.toFixed(2)} to go
        </span>
      </div>
      
      <div className="w-full bg-blue-100 rounded-full h-2 mb-2">
        <div 
          className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${progress}%` }}
        />
      </div>
      
      <p className="text-xs text-blue-600 font-helvetica">
        Add ${remaining.toFixed(2)} more to get <strong>free standard shipping</strong>
      </p>
    </div>
  );
}
