/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable trailing slashes for better SEO
  trailingSlash: true,
  
  // Enable image optimization
  images: {
    domains: ['insidestrategy.co'],
    formats: ['image/webp', 'image/avif'],
  },
  
  // Add security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ]
  },

  // Add redirects if needed
  async redirects() {
    return [
      // Example redirect from old URL structure
      // {
      //   source: '/old-page',
      //   destination: '/new-page',
      //   permanent: true,
      // },
    ]
  },
}

module.exports = nextConfig