'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { CheckCircle, Mail, Truck, ArrowLeft } from 'lucide-react';
import { helvetica, editorialNew } from '@/app/fonts';
import { getOrder, type Order } from '@/lib/firebase/orders';
import { useCart } from '@/lib/context/cart-context';
import cx from 'classnames';
import Link from 'next/link';

function SuccessPageContent() {
  const searchParams = useSearchParams();
  const orderId = searchParams.get('order_id');
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { dispatch } = useCart();

  useEffect(() => {
    console.log('🎉 Success page loaded with order ID:', orderId);

    // Clear cart when success page loads
    localStorage.removeItem('milos-Menagerie-cart');
    dispatch({ type: 'CLEAR_CART' });

    if (orderId) {
      console.log('📋 Loading order details for:', orderId);
      loadOrder(orderId);
    } else {
      console.error('❌ No order ID provided in URL');
      setError('No order ID provided');
      setLoading(false);
    }
  }, [orderId, dispatch]);

  const loadOrder = async (id: string) => {
    try {
      console.log('🔍 Attempting to load order from Firebase:', id);
      const orderData = await getOrder(id);

      if (orderData) {
        console.log('✅ Order loaded successfully:', orderData);
        setOrder(orderData);
      } else {
        console.error('❌ Order not found in Firebase:', id);
        setError('Order not found - it may still be processing. Please check your email for confirmation.');
      }
    } catch (err) {
      console.error('❌ Error loading order from Firebase:', err);
      setError('Failed to load order details - it may still be processing. Please check your email for confirmation.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={cx(helvetica.className, "min-h-screen bg-gray-50 flex items-center justify-center")}>
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
          <p className="mt-4 text-gray-600">Loading order details...</p>
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className={cx(helvetica.className, "min-h-screen bg-gray-50 flex items-center justify-center")}>
        <div className="text-center max-w-md mx-auto px-4">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle size={32} className="text-green-600" />
          </div>
          <h1 className={cx(editorialNew.className, "text-2xl mb-4")}>Payment Successful!</h1>
          <p className="text-gray-600 mb-6">
            Your payment has been processed successfully. {error || 'Order details are being prepared.'}
          </p>
          <p className="text-sm text-gray-500 mb-6">
            You should receive a confirmation email shortly with your order details.
            {orderId && (
              <>
                <br />
                <strong>Order ID:</strong> {orderId}
              </>
            )}
          </p>
          <div className="space-y-3">
            <button
              onClick={() => window.location.reload()}
              className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Refresh Order Details
            </button>
            <Link
              href="/"
              className="block w-full bg-gray-200 text-black px-6 py-3 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Continue Shopping
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const estimatedDelivery = new Date();
  estimatedDelivery.setDate(estimatedDelivery.getDate() + 7); // Default 7 days

  return (
    <div className={cx(helvetica.className, "min-h-screen bg-gray-50")}>
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Success Header */}
        <div className="text-center mb-8">
          <CheckCircle size={64} className="mx-auto text-green-500 mb-4" />
          <h1 className={cx(editorialNew.className, "text-3xl font-normal mb-2")}>
            Order Confirmed!
          </h1>
          <p className="text-gray-600 text-lg">
            Thank you for your purchase. Your order has been received and is being processed.
          </p>
        </div>

        {/* Order Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Order Information */}
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <h2 className="text-xl font-medium mb-4">Order Information</h2>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Order Number:</span>
                <span className="font-medium">{order.orderNumber || order.id}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Order Date:</span>
                <span>{order.createdAt?.toDate().toLocaleDateString()}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Payment Status:</span>
                <span className="capitalize text-green-600 font-medium">{order.paymentStatus || 'paid'}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Order Status:</span>
                <span className="capitalize font-medium">{order.status || 'confirmed'}</span>
              </div>
            </div>
          </div>

          {/* Shipping Information */}
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <h2 className="text-xl font-medium mb-4 flex items-center">
              <Truck size={20} className="mr-2" />
              Shipping Information
            </h2>
            
            <div className="space-y-3">
              <div>
                <p className="text-gray-600 text-sm">Shipping to:</p>
                <p className="font-medium">
                  {order.shippingAddress?.firstName} {order.shippingAddress?.lastName}
                </p>
                <p>{order.shippingAddress?.line1}</p>
                {order.shippingAddress?.line2 && <p>{order.shippingAddress.line2}</p>}
                <p>
                  {order.shippingAddress?.city}, {order.shippingAddress?.state} {order.shippingAddress?.postalCode}
                </p>
                <p>{order.shippingAddress?.country}</p>
              </div>
              
              <div>
                <p className="text-gray-600 text-sm">Shipping Method:</p>
                <p className="font-medium">{order.shippingMethod?.name || 'Standard Shipping'}</p>
                <p className="text-sm text-gray-600">{order.shippingMethod?.description || 'Standard shipping method'}</p>
              </div>
              
              <div>
                <p className="text-gray-600 text-sm">Estimated Delivery:</p>
                <p className="font-medium">{estimatedDelivery.toLocaleDateString()}</p>
                <p className="text-sm text-gray-600">({order.shippingMethod?.estimatedDays || '3-5 business days'})</p>
              </div>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-8">
          <h2 className="text-xl font-medium mb-4">What's Next?</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-start space-x-3">
              <Mail size={20} className="text-blue-600 mt-1" />
              <div>
                <h3 className="font-medium mb-1">Email Confirmation</h3>
                <p className="text-sm text-gray-600">
                  A confirmation email has been sent to <strong>{order.customerEmail || 'your email'}</strong> with your order details.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <Truck size={20} className="text-blue-600 mt-1" />
              <div>
                <h3 className="font-medium mb-1">Order Processing</h3>
                <p className="text-sm text-gray-600">
                  Your order is being prepared for shipment. You'll receive tracking information once it ships.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Continue Shopping */}
        <div className="text-center mt-8">
          <Link 
            href="/"
            className="inline-flex items-center bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors"
          >
            <ArrowLeft size={16} className="mr-2" />
            Continue Shopping
          </Link>
        </div>
      </div>
    </div>
  );
}

export default function SuccessPage() {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center">Loading...</div>}>
      <SuccessPageContent />
    </Suspense>
  );
}
