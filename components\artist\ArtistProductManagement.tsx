'use client';

import { useState, useEffect } from 'react';
import { AdminUser } from '@/lib/auth/admin-firestore';
import { getArtistByFirebaseUid } from '@/lib/firebase/artists';
import { 
  getProductsByArtistId, 
  createArtistProduct, 
  updateArtistProduct, 
  deleteArtistProduct,
  toggleProductStatus,
  uploadArtistProductImages,
  deleteArtistProductImage,
  ArtistProduct 
} from '@/lib/firebase/artist-products';
import { helvetica, editorialNew } from '@/app/fonts';
import ImageUpload from '@/components/ui/ImageUpload';
import cx from 'classnames';

interface ArtistProductManagementProps {
  user: AdminUser;
}

const CATEGORIES = [
  'painting', 'sculpture', 'digital', 'print', 'jewelry', 'textile', 'other'
];

const MEDIUMS = [
  'Oil Paint', 'Acrylic Paint', 'Watercolor', 'Charcoal', 'Pencil', 'Ink', 
  'Digital', 'Mixed Media', 'Clay', 'Bronze', 'Wood', 'Metal', 'Fabric', 'Other'
];

export default function ArtistProductManagement({ user }: ArtistProductManagementProps) {
  const [products, setProducts] = useState<ArtistProduct[]>([]);
  const [artistData, setArtistData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState<ArtistProduct | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    category: 'other' as string,
    medium: '',
    dimensions: {
      width: '',
      height: '',
      depth: '',
      unit: 'in' as 'in' | 'cm'
    },
    images: [] as { url: string; isPrimary: boolean; alt?: string; file?: File }[],
    quantity: '1',
    isUnique: false,
    tags: '',
    isActive: true,
    isFeatured: false
  });
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const artist = await getArtistByFirebaseUid(user.uid);
      if (artist) {
        setArtistData(artist);
        const artistProducts = await getProductsByArtistId(artist.id);
        setProducts(artistProducts);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    // Clean up any object URLs to prevent memory leaks
    formData.images.forEach(img => {
      if (img.file && img.url.startsWith('blob:')) {
        URL.revokeObjectURL(img.url);
      }
    });

    setFormData({
      name: '',
      description: '',
      price: '',
      category: 'other',
      medium: '',
      dimensions: {
        width: '',
        height: '',
        depth: '',
        unit: 'in'
      },
      images: [],
      quantity: '1',
      isUnique: false,
      tags: '',
      isActive: true,
      isFeatured: false
    });
    setEditingProduct(null);
  };

  const handleEdit = (product: ArtistProduct) => {
    setFormData({
      name: product.name,
      description: product.description,
      price: product.price.toString(),
      category: product.category,
      medium: product.medium,
      dimensions: {
        width: product.dimensions?.width.toString() || '',
        height: product.dimensions?.height.toString() || '',
        depth: product.dimensions?.depth?.toString() || '',
        unit: product.dimensions?.unit || 'in'
      },
      images: product.images.map(img => ({
        url: img.url,
        isPrimary: img.isPrimary,
        alt: img.alt || ''
      })),
      quantity: product.quantity.toString(),
      isUnique: product.isUnique,
      tags: product.tags.join(', '),
      isActive: product.isActive,
      isFeatured: product.isFeatured
    });
    setEditingProduct(product);
    setShowForm(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!artistData) return;

    try {
      setUploading(true);
      
      // Separate files that need uploading from existing URLs
      const filesToUpload = formData.images.filter(img => img.file);
      const existingImages = formData.images.filter(img => !img.file);
      
      let uploadedImages: { url: string; isPrimary: boolean; alt?: string }[] = [];
      
      // Upload new files if any
      if (filesToUpload.length > 0) {
        const files = filesToUpload.map(img => img.file!);
        uploadedImages = await uploadArtistProductImages(files, artistData.id, editingProduct?.id);
        
        // Preserve isPrimary and alt settings from form
        uploadedImages = uploadedImages.map((uploaded, index) => ({
          ...uploaded,
          isPrimary: filesToUpload[index].isPrimary,
          alt: filesToUpload[index].alt || uploaded.alt
        }));
      }
      
      // Combine existing images with newly uploaded ones
      const allImages = [...existingImages.map(img => ({ url: img.url, isPrimary: img.isPrimary, alt: img.alt })), ...uploadedImages];
      
      // Ensure exactly one primary image
      if (allImages.length > 0 && !allImages.some(img => img.isPrimary)) {
        allImages[0].isPrimary = true;
      }

      const productData = {
        name: formData.name,
        description: formData.description,
        price: parseFloat(formData.price),
        category: formData.category,
        medium: formData.medium,
        dimensions: formData.dimensions.width ? {
          width: parseFloat(formData.dimensions.width),
          height: parseFloat(formData.dimensions.height),
          depth: formData.dimensions.depth ? parseFloat(formData.dimensions.depth) : undefined,
          unit: formData.dimensions.unit
        } : undefined,
        images: allImages,
        quantity: parseInt(formData.quantity),
        isUnique: formData.isUnique,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        isActive: formData.isActive,
        isFeatured: formData.isFeatured
      };

      if (editingProduct) {
        await updateArtistProduct(editingProduct.id, productData);
      } else {
        await createArtistProduct(
          artistData.id,
          artistData.businessName || `${artistData.firstName} ${artistData.lastName}`,
          productData
        );
      }

      await loadData();
      setShowForm(false);
      resetForm();
    } catch (error) {
      console.error('Error saving product:', error);
      alert('Failed to save product');
    } finally {
      setUploading(false);
    }
  };

  const handleDelete = async (productId: string) => {
    if (!confirm('Are you sure you want to delete this product?')) return;

    try {
      await deleteArtistProduct(productId);
      await loadData();
    } catch (error) {
      console.error('Error deleting product:', error);
      alert('Failed to delete product');
    }
  };

  const handleToggleStatus = async (productId: string, isActive: boolean) => {
    try {
      await toggleProductStatus(productId, isActive);
      await loadData();
    } catch (error) {
      console.error('Error toggling product status:', error);
      alert('Failed to update product status');
    }
  };

  const handleImagesChange = (images: { url: string; isPrimary: boolean; alt?: string; file?: File }[]) => {
    setFormData(prev => ({
      ...prev,
      images
    }));
  };

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
        <p>Loading your products...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className={cx(editorialNew.className, "text-2xl font-bold")}>My Products</h2>
        <button
          onClick={() => {
            resetForm();
            setShowForm(true);
          }}
          className="bg-black text-white px-4 py-2 rounded hover:bg-gray-800"
        >
          Add New Product
        </button>
      </div>

      {/* Product Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b">
              <div className="flex justify-between items-center">
                <h3 className={cx(editorialNew.className, "text-xl font-bold")}>
                  {editingProduct ? 'Edit Product' : 'Add New Product'}
                </h3>
                <button
                  onClick={() => setShowForm(false)}
                  className="text-gray-400 hover:text-gray-600 text-2xl"
                >
                  ×
                </button>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                    Product Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
                    required
                  />
                </div>
                <div>
                  <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                    Price ($) *
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.price}
                    onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
                    required
                  />
                </div>
              </div>

              <div>
                <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                  Description *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  rows={4}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                    Category *
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
                    required
                  >
                    {CATEGORIES.map(cat => (
                      <option key={cat} value={cat}>{cat.charAt(0).toUpperCase() + cat.slice(1)}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                    Medium
                  </label>
                  <select
                    value={formData.medium}
                    onChange={(e) => setFormData(prev => ({ ...prev, medium: e.target.value }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
                  >
                    <option value="">Select medium</option>
                    {MEDIUMS.map(medium => (
                      <option key={medium} value={medium}>{medium}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Dimensions */}
              <div>
                <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-2")}>
                  Dimensions
                </label>
                <div className="grid grid-cols-4 gap-2">
                  <input
                    type="number"
                    step="0.1"
                    placeholder="Width"
                    value={formData.dimensions.width}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      dimensions: { ...prev.dimensions, width: e.target.value }
                    }))}
                    className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
                  />
                  <input
                    type="number"
                    step="0.1"
                    placeholder="Height"
                    value={formData.dimensions.height}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      dimensions: { ...prev.dimensions, height: e.target.value }
                    }))}
                    className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
                  />
                  <input
                    type="number"
                    step="0.1"
                    placeholder="Depth (optional)"
                    value={formData.dimensions.depth}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      dimensions: { ...prev.dimensions, depth: e.target.value }
                    }))}
                    className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
                  />
                  <select
                    value={formData.dimensions.unit}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      dimensions: { ...prev.dimensions, unit: e.target.value as 'in' | 'cm' }
                    }))}
                    className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
                  >
                    <option value="in">inches</option>
                    <option value="cm">cm</option>
                  </select>
                </div>
              </div>

              {/* Images */}
              <div>
                <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-2")}>
                  Product Images
                </label>
                <ImageUpload
                  images={formData.images}
                  onImagesChange={handleImagesChange}
                  maxImages={5}
                />
              </div>

              {/* Inventory & Settings */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                    Quantity
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={formData.quantity}
                    onChange={(e) => setFormData(prev => ({ ...prev, quantity: e.target.value }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
                    disabled={formData.isUnique}
                  />
                </div>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.isUnique}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        isUnique: e.target.checked,
                        quantity: e.target.checked ? '1' : prev.quantity
                      }))}
                      className="mr-2"
                    />
                    <span className={cx(helvetica.className, "text-sm")}>One-of-a-kind piece</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.isActive}
                      onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                      className="mr-2"
                    />
                    <span className={cx(helvetica.className, "text-sm")}>Active (visible to customers)</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.isFeatured}
                      onChange={(e) => setFormData(prev => ({ ...prev, isFeatured: e.target.checked }))}
                      className="mr-2"
                    />
                    <span className={cx(helvetica.className, "text-sm")}>Featured product</span>
                  </label>
                </div>
              </div>

              <div>
                <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                  Tags (comma-separated)
                </label>
                <input
                  type="text"
                  value={formData.tags}
                  onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="abstract, colorful, modern"
                />
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-4 pt-6 border-t">
                <button
                  type="button"
                  onClick={() => setShowForm(false)}
                  className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={uploading}
                  className="px-6 py-2 bg-black text-white rounded-md hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {uploading ? 'Uploading...' : (editingProduct ? 'Update Product' : 'Create Product')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Products List */}
      {products.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <p className={cx(helvetica.className, "text-gray-600 mb-4")}>
            You haven't created any products yet.
          </p>
          <button
            onClick={() => {
              resetForm();
              setShowForm(true);
            }}
            className="bg-black text-white px-6 py-2 rounded hover:bg-gray-800"
          >
            Create Your First Product
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {products.map((product) => (
            <div key={product.id} className="bg-white border rounded-lg overflow-hidden shadow-sm">
              {product.images[0]?.url && (
                <img
                  src={product.images[0].url}
                  alt={product.name}
                  className="w-full h-48 object-cover"
                />
              )}
              <div className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className={cx(editorialNew.className, "font-semibold text-lg")}>{product.name}</h3>
                  <span className={cx(
                    "px-2 py-1 text-xs rounded",
                    product.isActive 
                      ? "bg-green-100 text-green-800" 
                      : "bg-red-100 text-red-800"
                  )}>
                    {product.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <p className={cx(helvetica.className, "text-gray-600 text-sm mb-2 line-clamp-2")}>
                  {product.description}
                </p>
                <p className={cx(helvetica.className, "text-lg font-semibold mb-3")}>
                  ${product.price.toFixed(2)}
                </p>
                <div className="flex justify-between items-center">
                  <div className="space-x-2">
                    <button
                      onClick={() => handleEdit(product)}
                      className="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleToggleStatus(product.id, !product.isActive)}
                      className="text-yellow-600 hover:text-yellow-800 text-sm"
                    >
                      {product.isActive ? 'Deactivate' : 'Activate'}
                    </button>
                    <button
                      onClick={() => handleDelete(product.id)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Delete
                    </button>
                  </div>
                  <span className={cx(helvetica.className, "text-sm text-gray-500")}>
                    Qty: {product.quantity}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
