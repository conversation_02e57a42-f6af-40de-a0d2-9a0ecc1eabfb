import React, { useRef } from 'react';

interface ImageUploadProps {
  images: string[];
  setImages: (images: string[]) => void;
}

export default function ImageUpload({ images, setImages }: ImageUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFiles = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;
    const newImages: string[] = [];
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          setImages([...images, e.target.result as string]);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div>
      <input
        type="file"
        multiple
        accept="image/*"
        ref={fileInputRef}
        style={{ display: 'none' }}
        onChange={handleFiles}
      />
      <button
        type="button"
        className="bg-gray-200 px-4 py-2 rounded"
        onClick={() => fileInputRef.current?.click()}
      >
        Upload Photos
      </button>
      <div className="flex flex-wrap gap-2 mt-2">
        {images.map((img, idx) => (
          <img key={idx} src={img} alt="Invoice" className="h-20 w-20 object-cover rounded border" />
        ))}
      </div>
    </div>
  );
}
