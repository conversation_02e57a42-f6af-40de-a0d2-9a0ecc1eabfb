# Firestore Admin Management Setup Guide

## Overview
This system replaces environment variable-based admin authentication with a flexible Firestore-based role and permission system.

## 🔥 Firebase Firestore Setup

### 1. Create Required Collections

In your Firebase Console → Firestore Database, create these collections:

#### Collection: `admins`
```javascript
// Document structure for each admin
{
  // Document ID should be the Firebase Auth UID
  "email": "<EMAIL>",
  "displayName": "Admin Name",
  "role": "super_admin", // or "admin", "moderator" 
  "permissions": ["*"], // or specific permissions array
  "isActive": true,
  "createdAt": (timestamp),
  "lastLoginAt": (timestamp),
  "createdBy": "uid_of_creator",
  "metadata": {
    // Additional custom fields
  }
}
```

#### Collection: `admin_roles` (Optional)
```javascript
// Document ID: role name (e.g., "super_admin")
{
  "name": "Super Administrator",
  "permissions": ["*"],
  "description": "Full system access"
}
```

### 2. Available Permissions
```javascript
const PERMISSIONS = {
  CREATE_INVOICE: 'create_invoice',
  MANAGE_PRODUCTS: 'manage_products', 
  VIEW_ANALYTICS: 'view_analytics',
  MANAGE_ADMINS: 'manage_admins',
  SEND_EMAILS: 'send_emails',
  ALL: '*' // Wildcard for all permissions
}
```

### 3. Admin Roles
- **super_admin**: All permissions (`["*"]`)
- **admin**: Standard admin permissions (`["create_invoice", "manage_products", "view_analytics"]`)
- **moderator**: Read-only access (`["view_analytics"]`)

## 🚀 Migration Steps

### Step 1: Install Dependencies
Already done - jsonwebtoken is installed.

### Step 2: Update Environment Variables
Remove from `.env.local`:
```bash
# Remove this line:
# NEXT_PUBLIC_ADMIN_EMAIL=<EMAIL>
```

Keep these:
```bash
JWT_SECRET=your-super-secure-random-string-here
```

### Step 3: Run Migration Script
```bash
# Set your current admin password and run migration
ADMIN_PASSWORD=your_current_password node scripts/migrate-admin-to-firestore.js
```

This script will:
- Authenticate your existing admin user
- Create an admin document in Firestore with super_admin role
- Give you the option to remove environment variables

### Step 4: Test the New System
1. Go to `/admin`
2. Try logging in with email/password
3. Try logging in with Google (if enabled)
4. Verify you can access all admin functions

## 🔧 New Features

### 1. Role-Based Access Control
```javascript
// Check permissions in your API routes
import { verifyAdminAuth, hasPermission } from '@/lib/auth/middleware';

export async function POST(request) {
  const adminSession = await verifyAdminAuth(request);
  
  if (!adminSession) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  if (!hasPermission(adminSession, 'create_invoice')) {
    return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
  }
  
  // Continue with the request...
}
```

### 2. Admin Management Functions
```javascript
import { 
  createAdminUser, 
  getAllAdmins, 
  deactivateAdmin,
  isUserAdmin 
} from '@/lib/auth/admin-firestore';

// Create new admin
await createAdminUser('user_uid', {
  email: '<EMAIL>',
  role: 'admin',
  permissions: ['create_invoice', 'manage_products'],
  displayName: 'New Admin'
}, 'creator_uid');

// Get all admins
const admins = await getAllAdmins();

// Deactivate admin
await deactivateAdmin('user_uid');
```

### 3. Enhanced Security
- ✅ JWT-based sessions with role/permission info
- ✅ Server-side permission validation
- ✅ Firestore security rules integration
- ✅ Google Sign-In support
- ✅ 2FA support maintained

## 🛡️ Firestore Security Rules

Add these rules to protect your admin collection:

```javascript
// Firestore Security Rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Admin collection - only admins can read/write
    match /admins/{adminId} {
      allow read, write: if request.auth != null && 
        exists(/databases/$(database)/documents/admins/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/admins/$(request.auth.uid)).data.isActive == true;
    }
    
    // Admin roles - read-only for admins
    match /admin_roles/{roleId} {
      allow read: if request.auth != null && 
        exists(/databases/$(database)/documents/admins/$(request.auth.uid));
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/admins/$(request.auth.uid)).data.permissions[0] == '*';
    }
    
    // Your other collections...
  }
}
```

## 📊 Benefits of New System

### Scalability
- ✅ Multiple admin users
- ✅ Different permission levels
- ✅ Easy role management
- ✅ No code changes needed for new admins

### Security
- ✅ Granular permissions
- ✅ Audit trail with creation/login timestamps
- ✅ Easy admin deactivation
- ✅ Firestore security rules protection

### Management
- ✅ Admin user management through Firestore Console
- ✅ Real-time permission updates
- ✅ Role-based access control
- ✅ Activity tracking

## 🎯 Next Steps

1. **Run the migration script**
2. **Test admin login**
3. **Create additional admin users in Firestore**
4. **Set up Firestore security rules**
5. **Build admin management UI** (optional)

## 🔍 Troubleshooting

### Common Issues:
1. **"Admin not found"**: Make sure to run the migration script first
2. **"Permissions denied"**: Check Firestore security rules
3. **"JWT errors"**: Verify JWT_SECRET is set in environment
4. **"Google login fails"**: Ensure Google provider is enabled in Firebase Auth

### Debug Commands:
```bash
# Check current admin in Firestore
# Go to Firebase Console → Firestore → admins collection

# Test login
curl -X POST http://localhost:3000/api/auth/admin-login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"yourpassword"}'
```

You now have a production-ready, scalable admin authentication system! 🎉
