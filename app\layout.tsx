import './globals.css';
import cx from 'classnames';
import { helvetica } from './fonts';
import NavBarWrapper, { MainWrapper, FooterWrapper } from '@/components/layout/navbar-wrapper';
import { Analytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/react';
import { CartProvider } from '@/lib/context/cart-context';

export const metadata = {
  title: {
    default: "<PERSON>'s Menagerie - Adventure Awaits in Every Creation | 3D Printed Animals & Plants",
    template: "%s | <PERSON>'s Menagerie"
  },
  description: "3D printed animals and plants that inspire exploration, learning, and endless adventure",
  openGraph: {
    title: "<PERSON>'s Menagerie - Adventure Awaits in Every Creation",
    description: "3D printed animals and plants that inspire exploration, learning, and endless adventure",
    images: [
      {
        url: '/milosmenagerie square.png',
        width: 800,
        height: 800,
        alt: "<PERSON>'s Menagerie",
      }
    ],
    type: 'website',
    siteName: "<PERSON>'s Menagerie",
  },
  twitter: {
    card: 'summary_large_image',
    title: "<PERSON>'s Menagerie - Adventure Awaits in Every Creation",
    description: "3D printed animals and plants that inspire exploration, learning, and endless adventure",
    images: ['/milosmenagerie square.png'],
  },
  verification: {
    google: process.env.GOOGLE_VERIFICATION || '',
    yandex: process.env.YANDEX_VERIFICATION || '',
    bing: process.env.BING_VERIFICATION || '',
  },
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Store",
              "name": "Milo's Menagerie",
              "description": "3D printed animals and plants that inspire exploration, learning, and endless adventure",
              "url": "https://milosMenagerie.com",
              "serviceType": "3D Printing & Educational Toys",
              "areaServed": "Worldwide",
              "category": "Educational Toys & 3D Printing"
            })
          }}
        />
      </head>
      <body className={cx(
        helvetica.variable,
        'flex flex-col min-h-screen bg-white text-black font-helvetica'
      )}>
        <CartProvider>
          <NavBarWrapper />
          <MainWrapper>
            {children}
          </MainWrapper>
          <FooterWrapper />
        </CartProvider>
        <Analytics />
        <SpeedInsights />
      </body>
    </html>
  );
}
