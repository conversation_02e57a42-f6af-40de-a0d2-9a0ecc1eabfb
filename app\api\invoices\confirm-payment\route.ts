import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { db } from '@/lib/firebase/config';
import { doc, getDoc, updateDoc, setDoc, serverTimestamp } from 'firebase/firestore';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
});

interface ContactInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

interface Address {
  line1: string;
  line2: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

interface ShippingOption {
  id: string;
  name: string;
  description: string;
  price: number;
  estimatedDays: string;
}

export async function POST(req: NextRequest) {
  try {
    const { 
      invoiceId, 
      paymentIntentId, 
      contactInfo, 
      shippingAddress,
      selectedShipping 
    }: {
      invoiceId: string;
      paymentIntentId: string;
      contactInfo: ContactInfo;
      shippingAddress: Address;
      selectedShipping?: ShippingOption;
    } = await req.json();

    // Verify payment intent was successful
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
    
    if (paymentIntent.status !== 'succeeded') {
      return NextResponse.json(
        { error: 'Payment not completed' },
        { status: 400 }
      );
    }

    // Get invoice from Firebase
    const invoiceRef = doc(db, 'invoices', invoiceId);
    const invoiceSnap = await getDoc(invoiceRef);

    if (!invoiceSnap.exists()) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      );
    }

    const invoice = invoiceSnap.data();

    // Update invoice status to paid
    await updateDoc(invoiceRef, {
      status: 'paid',
      paidAt: serverTimestamp(),
      paymentIntentId,
      updatedAt: serverTimestamp(),
    });

    // Create a custom order record
    const customOrderId = `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const customOrderRef = doc(db, 'custom_orders', customOrderId);
    
    const shippingCost = selectedShipping?.price || 0;
    const totalAmount = invoice.amount + shippingCost;
    
    await setDoc(customOrderRef, {
      id: customOrderId,
      invoiceId,
      invoiceTitle: invoice.title,
      invoiceDescription: invoice.description,
      amount: invoice.amount,
      shippingCost,
      totalAmount,
      
      // Artist information
      artistId: invoice.artistId, // Track which artist this order belongs to
      
      // Shipping details
      shippingMethod: selectedShipping?.name || 'No shipping',
      shippingDescription: selectedShipping?.description || '',
      estimatedDelivery: selectedShipping?.estimatedDays || '',
      
      // Customer information
      customerEmail: contactInfo.email,
      customerPhone: contactInfo.phone,
      customerName: `${contactInfo.firstName} ${contactInfo.lastName}`,
      
      // Shipping address
      shippingAddress: {
        firstName: contactInfo.firstName,
        lastName: contactInfo.lastName,
        line1: shippingAddress.line1,
        line2: shippingAddress.line2,
        city: shippingAddress.city,
        state: shippingAddress.state,
        postalCode: shippingAddress.postalCode,
        country: shippingAddress.country,
      },
      
      // Payment details
      paymentIntentId,
      paymentStatus: 'paid',
      
      // Order status
      status: 'confirmed',
      
      // Timestamps
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      
      // Notes
      notes: invoice.notes || '',
    });

    // Send confirmation emails
    try {
      await fetch(`${req.headers.get('origin')}/api/invoices/send-confirmation-emails`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          invoiceId,
          customOrderId,
          contactInfo,
          shippingAddress,
          selectedShipping,
        }),
      });
    } catch (emailError) {
      console.error('Failed to send confirmation emails:', emailError);
      // Don't fail the order if emails fail
    }

    return NextResponse.json({
      success: true,
      customOrderId,
      message: 'Payment confirmed successfully',
    });

  } catch (error) {
    console.error('Error confirming invoice payment:', error);
    return NextResponse.json(
      { error: 'Failed to confirm payment' },
      { status: 500 }
    );
  }
}
