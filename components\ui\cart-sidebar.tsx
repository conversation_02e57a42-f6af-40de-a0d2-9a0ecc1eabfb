'use client';

import { useCart } from '@/lib/context/cart-context';
import { X, Plus, Minus, ShoppingBag } from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import FreeShippingProgress from './free-shipping-progress';

interface CartSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function CartSidebar({ isOpen, onClose }: CartSidebarProps) {
  const { state, dispatch } = useCart();
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  const router = useRouter();

  const updateQuantity = (id: string, quantity: number) => {
    dispatch({ type: 'UPDATE_QUANTITY', payload: { id, quantity } });
  };

  const removeItem = (id: string) => {
    dispatch({ type: 'REMOVE_ITEM', payload: id });
  };



  return (
    <>
      {/* Overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={onClose}
        />
      )}
      
      {/* Sidebar */}
      <div className={`fixed right-0 top-0 h-full w-96 bg-white shadow-xl transform transition-transform duration-300 z-50 ${
        isOpen ? 'translate-x-0' : 'translate-x-full'
      }`}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b">
            <h2 className="text-xl font-editorial font-normal">Your Cart</h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <X size={20} />
            </button>
          </div>

          {/* Scrollable Content Area */}
          <div className="flex-1 overflow-y-auto">
            {/* Cart Items */}
            <div className="p-4">
              {state.items.length === 0 ? (
                <div className="text-center py-8">
                  <ShoppingBag size={48} className="mx-auto text-gray-300 mb-4" />
                  <p className="text-gray-500 font-helvetica">Your cart is empty</p>
                </div>
              ) : (
                <>
                  {/* Free Shipping Progress */}
                  <FreeShippingProgress subtotal={state.subtotal} />

                  <div className="space-y-3 mb-4">
                    {state.items.map((item) => (
                    <div key={item.id} className="flex gap-3 p-3 border border-gray-200 rounded-lg">
                      <div className="relative w-14 h-14 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                        <Image
                          src={item.imageUrl}
                          alt={item.title}
                          fill
                          className="object-cover"
                        />
                      </div>

                      <div className="flex-1 min-w-0">
                        <h3 className="font-editorial text-sm font-normal truncate">{item.title}</h3>
                        <p className="text-xs text-gray-600 font-helvetica">
                          ${item.price.toFixed(2)}
                          {item.priceModifier && item.priceModifier !== 0 && (
                            <span> + ${item.priceModifier.toFixed(2)}</span>
                          )}
                        </p>
                        {/* Show variation information */}
                        {item.variationText && (
                          <p className="text-xs text-gray-500 font-helvetica mt-1">
                            {item.variationText}
                          </p>
                        )}
                        {/* Show stock information */}
                        {item.maxStock !== undefined && (
                          <p className={`text-xs font-helvetica ${
                            item.maxStock <= 5 ? 'text-orange-600' : 'text-gray-500'
                          }`}>
                            {item.maxStock} available
                          </p>
                        )}

                        <div className="flex items-center gap-2 mt-1">
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            className="p-1 hover:bg-gray-100 rounded"
                          >
                            <Minus size={12} />
                          </button>
                          <span className="text-sm font-helvetica">{item.quantity}</span>
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                            disabled={item.maxStock !== undefined && item.quantity >= item.maxStock}
                            className={`p-1 rounded ${
                              item.maxStock !== undefined && item.quantity >= item.maxStock
                                ? 'text-gray-400 cursor-not-allowed'
                                : 'hover:bg-gray-100'
                            }`}
                          >
                            <Plus size={12} />
                          </button>
                          {/* Show stock limit warning */}
                          {item.maxStock !== undefined && item.quantity >= item.maxStock && (
                            <span className="text-xs text-orange-600 font-helvetica ml-1">
                              Max available
                            </span>
                          )}
                          <button
                            onClick={() => removeItem(item.id)}
                            className="ml-auto text-red-500 text-xs font-helvetica"
                          >
                            Remove
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                  </div>
                </>
              )}
            </div>


          </div>

          {/* Fixed Footer */}
          {state.items.length > 0 && (
            <div className="border-t bg-white p-4 flex-shrink-0">
              <button
                onClick={() => {
                  console.log('Proceeding to checkout with items:', state.items.length);
                  onClose(); // Close the cart sidebar
                  router.push('/checkout');
                }}
                className="w-full bg-black text-white font-helvetica font-medium py-3 rounded-lg hover:bg-gray-800 transition-colors"
              >
                Proceed to Checkout
              </button>

              <p className="text-xs text-gray-500 font-helvetica text-center mt-2">
                Shipping calculated at checkout
              </p>
            </div>
          )}
        </div>
      </div>
    </>
  );
}