import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { db } from '@/lib/firebase/config';
import { doc, getDoc } from 'firebase/firestore';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
});

interface ContactInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

interface Address {
  line1: string;
  line2: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

interface ShippingOption {
  id: string;
  name: string;
  description: string;
  price: number;
  estimatedDays: string;
}

export async function POST(req: NextRequest) {
  try {
    const { 
      invoiceId, 
      contactInfo, 
      shippingAddress,
      selectedShipping 
    }: {
      invoiceId: string;
      contactInfo: ContactInfo;
      shippingAddress: Address;
      selectedShipping?: ShippingOption;
    } = await req.json();

    // Get invoice from Firebase
    const invoiceRef = doc(db, 'invoices', invoiceId);
    const invoiceSnap = await getDoc(invoiceRef);

    if (!invoiceSnap.exists()) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      );
    }

    const invoice = invoiceSnap.data();

    if (invoice.status === 'paid') {
      return NextResponse.json(
        { error: 'Invoice has already been paid' },
        { status: 400 }
      );
    }

    // Calculate total amount
    let totalAmount: number;
    let shippingCost: number;
    let shippingMethod: string;
    let shippingDescription: string;

    if (invoice.includeShipping) {
      // Invoice already includes shipping costs
      totalAmount = invoice.totalAmount || invoice.amount;
      shippingCost = invoice.shippingCost || 0;
      shippingMethod = 'Included in invoice';
      shippingDescription = invoice.shippingNotes || 'Shipping included';
    } else {
      // Add shipping from checkout flow
      shippingCost = selectedShipping?.price || 0;
      totalAmount = invoice.amount + shippingCost;
      shippingMethod = selectedShipping?.name || 'No shipping';
      shippingDescription = selectedShipping?.description || '';
    }

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(totalAmount * 100), // Convert to cents
      currency: 'usd',
      metadata: {
        invoiceId,
        invoiceTitle: invoice.title,
        invoiceAmount: invoice.amount.toString(),
        shippingCost: shippingCost.toString(),
        shippingMethod: shippingMethod,
        shippingDescription: shippingDescription,
        customerEmail: contactInfo.email,
        customerPhone: contactInfo.phone,
        customerName: `${contactInfo.firstName} ${contactInfo.lastName}`,
        shippingLine1: shippingAddress.line1,
        shippingLine2: shippingAddress.line2 || '',
        shippingCity: shippingAddress.city,
        shippingState: shippingAddress.state,
        shippingPostalCode: shippingAddress.postalCode,
        shippingCountry: shippingAddress.country,
        type: 'invoice_payment',
      },
      automatic_payment_methods: {
        enabled: true,
      },
    });

    return NextResponse.json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
    });

  } catch (error) {
    console.error('Error creating payment intent for invoice:', error);
    return NextResponse.json(
      { error: 'Failed to create payment intent' },
      { status: 500 }
    );
  }
}
