import { 
  collection, 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  query, 
  where, 
  orderBy, 
  getDocs,
  serverTimestamp,
  Timestamp 
} from 'firebase/firestore';
import { db } from './config';

export interface OrderItem {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  price: number;
  quantity: number;
  total: number;
}

export interface ShippingAddress {
  firstName: string;
  lastName: string;
  line1: string;
  line2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

export interface ShippingMethod {
  id: string;
  name: string;
  description: string;
  price: number;
  estimatedDays: string;
}

export interface Order {
  id: string;
  orderNumber: string;
  
  // Customer Information
  customerEmail: string;
  customerPhone: string;
  
  // Addresses
  shippingAddress: ShippingAddress;
  billingAddress: ShippingAddress;
  sameAsBilling: boolean;
  
  // Order Details
  items: OrderItem[];
  subtotal: number;
  shippingCost: number;
  total: number;
  
  // Shipping
  shippingMethod: ShippingMethod;
  
  // Payment
  paymentIntentId: string;
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  
  // Order Status
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  
  // Timestamps
  createdAt: Timestamp;
  updatedAt: Timestamp;
  
  // Tracking
  trackingNumber?: string;
  estimatedDelivery?: string;
  
  // Notes
  notes?: string;
}

export interface Customer {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  
  // Saved Addresses
  defaultShippingAddress?: ShippingAddress;
  defaultBillingAddress?: ShippingAddress;
  
  // Order History
  totalOrders: number;
  totalSpent: number;
  
  // Timestamps
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Order Management Functions
export async function createOrder(orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
  try {
    const orderId = `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const orderRef = doc(db, 'orders', orderId);
    
    const order: Order = {
      ...orderData,
      id: orderId,
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp,
    };
    
    await setDoc(orderRef, order);
    
    // Update customer data
    await updateCustomerData(orderData.customerEmail, {
      firstName: orderData.shippingAddress.firstName,
      lastName: orderData.shippingAddress.lastName,
      phone: orderData.customerPhone,
      defaultShippingAddress: orderData.shippingAddress,
      defaultBillingAddress: orderData.billingAddress,
    });
    
    return orderId;
  } catch (error) {
    console.error('Error creating order:', error);
    throw new Error('Failed to create order');
  }
}

export async function getOrder(orderId: string): Promise<Order | null> {
  try {
    const orderRef = doc(db, 'orders', orderId);
    const orderSnap = await getDoc(orderRef);
    
    if (orderSnap.exists()) {
      return orderSnap.data() as Order;
    }
    
    return null;
  } catch (error) {
    console.error('Error getting order:', error);
    throw new Error('Failed to get order');
  }
}

export async function updateOrderStatus(
  orderId: string, 
  status: Order['status'], 
  updates?: Partial<Order>
): Promise<void> {
  try {
    const orderRef = doc(db, 'orders', orderId);
    
    await updateDoc(orderRef, {
      status,
      updatedAt: serverTimestamp(),
      ...updates,
    });
  } catch (error) {
    console.error('Error updating order status:', error);
    throw new Error('Failed to update order status');
  }
}

export async function getCustomerOrders(customerEmail: string): Promise<Order[]> {
  try {
    const ordersRef = collection(db, 'orders');
    const q = query(
      ordersRef,
      where('customerEmail', '==', customerEmail),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    const orders: Order[] = [];
    
    querySnapshot.forEach((doc) => {
      orders.push(doc.data() as Order);
    });
    
    return orders;
  } catch (error) {
    console.error('Error getting customer orders:', error);
    throw new Error('Failed to get customer orders');
  }
}

// Customer Management Functions
export async function updateCustomerData(
  email: string, 
  customerData: Partial<Omit<Customer, 'id' | 'email' | 'createdAt' | 'updatedAt'>>
): Promise<void> {
  try {
    const customerId = email.replace(/[^a-zA-Z0-9]/g, '_'); // Safe ID from email
    const customerRef = doc(db, 'customers', customerId);
    
    // Check if customer exists
    const customerSnap = await getDoc(customerRef);
    
    if (customerSnap.exists()) {
      // Update existing customer
      await updateDoc(customerRef, {
        ...customerData,
        updatedAt: serverTimestamp(),
      });
    } else {
      // Create new customer
      const newCustomer: Customer = {
        id: customerId,
        email,
        firstName: customerData.firstName || '',
        lastName: customerData.lastName || '',
        phone: customerData.phone,
        defaultShippingAddress: customerData.defaultShippingAddress,
        defaultBillingAddress: customerData.defaultBillingAddress,
        totalOrders: 0,
        totalSpent: 0,
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
      };
      
      await setDoc(customerRef, newCustomer);
    }
  } catch (error) {
    console.error('Error updating customer data:', error);
    throw new Error('Failed to update customer data');
  }
}

export async function getCustomer(email: string): Promise<Customer | null> {
  try {
    const customerId = email.replace(/[^a-zA-Z0-9]/g, '_');
    const customerRef = doc(db, 'customers', customerId);
    const customerSnap = await getDoc(customerRef);
    
    if (customerSnap.exists()) {
      return customerSnap.data() as Customer;
    }
    
    return null;
  } catch (error) {
    console.error('Error getting customer:', error);
    throw new Error('Failed to get customer');
  }
}

// Inventory Management (for future use)
export async function updateInventory(items: OrderItem[]): Promise<void> {
  try {
    // This would update product inventory counts
    console.log('Updating inventory for items:', items);
    
    for (const item of items) {
      // Try to update regular product first
      const productRef = doc(db, 'products', item.id);
      const productSnap = await getDoc(productRef);
      
      if (productSnap.exists()) {
        const productData = productSnap.data();
        const currentStock = productData.stock || 0;
        const newStock = Math.max(0, currentStock - item.quantity);
        
        await updateDoc(productRef, {
          stock: newStock,
          updatedAt: serverTimestamp()
        });
        
        console.log(`Updated product ${item.id} stock: ${currentStock} -> ${newStock}`);
      } else {
        // Try artist product
        const artistProductRef = doc(db, 'artist_products', item.id);
        const artistProductSnap = await getDoc(artistProductRef);
        
        if (artistProductSnap.exists()) {
          const artistProductData = artistProductSnap.data();
          const currentQuantity = artistProductData.quantity || 0;
          const newQuantity = Math.max(0, currentQuantity - item.quantity);
          
          await updateDoc(artistProductRef, {
            quantity: newQuantity,
            updatedAt: serverTimestamp()
          });
          
          console.log(`Updated artist product ${item.id} quantity: ${currentQuantity} -> ${newQuantity}`);
        }
      }
    }
  } catch (error) {
    console.error('Error updating inventory:', error);
    throw new Error('Failed to update inventory');
  }
}
