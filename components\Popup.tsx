"use client";

import React, { useState, useEffect, ChangeEvent } from 'react';
import { X, ArrowRight, ArrowLeft } from 'lucide-react';

// Define interfaces for type safety
interface FormData {
  name: string;
  email: string;
  phone: string;
  website: string;
  preferredContact: string;
  industry: string;
  salesChannels: string[];
  websiteRevenue: string;
  productType: string;
  fulfillmentMethods: string[];
  businessAge: string;
  annualRevenue: string;
  interestedInSelling: string;
}

interface Errors {
  [key: string]: string;
}

interface Question {
  id: string;
  type: string;
  title: string;
  description?: string;
  required?: boolean;
  field?: keyof FormData;
  placeholder?: string;
  options?: string[];
  dependsOn?: {
    field: keyof FormData;
    value: string;
  };
}

interface SurveyPopupProps {
  onClose: () => void;
}

export default function SurveyPopup({ onClose }: SurveyPopupProps) {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [animateIn, setAnimateIn] = useState(true);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    website: '',
    preferredContact: '',
    industry: '',
    salesChannels: [],
    websiteRevenue: '',
    productType: '',
    fulfillmentMethods: [],
    businessAge: '',
    annualRevenue: '',
    interestedInSelling: ''
  });
  const [errors, setErrors] = useState<Errors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState('');

  const preferredContactOptions = ["Email", "Phone", "Text"];

  const questions: Question[] = [
    {
      id: 'contactInfo',
      type: 'contact',
      title: "Let's Start With Your Contact Information",
      required: true
    },
    {
      id: 'industry',
      type: 'multiple-choice',
      title: "What Industry Are You In?",
      description: "Select the closest industry to your business",
      required: true,
      field: 'industry',
      options: [
        "Consumer Products",
        "Technology",
        "Healthcare",
        "Consumer Services",
        "Business Services",
        "Industrial Services",
        "Construction",
        "Saas & Software",
        "Media",
        "Manufacturing",
        "Other"
      ]
    },
    {
      id: 'salesChannels',
      type: 'checkboxes',
      title: "Sales Channels",
      description: "How do your consumers buy your product (select all that apply)",
      required: true,
      field: 'salesChannels',
      options: [
        "Through your website",
        "Through Amazon",
        "Through Walmart.com",
        "Through Wholesale channels",
        "Through traditional retail",
        "Other"
      ],
      dependsOn: {
        field: 'industry',
        value: 'Consumer Products'
      }
    },
    {
      id: 'websiteRevenue',
      type: 'multiple-choice',
      title: "Revenue Through Website",
      description: "What percentage of revenue is directly through your website?",
      required: true,
      field: 'websiteRevenue',
      options: [
        "0-20%",
        "21-40%",
        "41-60%",
        "61-80%",
        "81-100%"
      ]
    },
    {
      id: 'productType',
      type: 'multiple-choice',
      title: "Products",
      description: "Do you sell your own brands or distribute for other companies?",
      required: true,
      field: 'productType',
      options: [
        "Sell your own products",
        "Sell other companies products",
        "Both"
      ]
    },
    {
      id: 'fulfillmentMethods',
      type: 'checkboxes',
      title: "Fulfillment",
      description: "How do you fulfill orders? Select all that apply.",
      required: true,
      field: 'fulfillmentMethods',
      options: [
        "Digital Products",
        "3PL Facility",
        "Drop shipped from supplier(s)",
        "Your own warehouse (This can be owned or leased)"
      ]
    },
    {
      id: 'businessAge',
      type: 'text',
      title: "Business Age",
      description: "How many years has your business been operating?",
      required: true,
      field: 'businessAge',
      placeholder: 'Enter number of years'
    },
    {
      id: 'annualRevenue',
      type: 'multiple-choice',
      title: "Annual Revenue",
      description: "How much is your annual revenue?",
      required: true,
      field: 'annualRevenue',
      options: [
        "Under $1M",
        "$1M-$3M",
        "$3M-$5M",
        "$5M-$10M",
        "$10M-$20M",
        "$20M-$50M",
        "$50M+"
      ]
    },
    {
      id: 'interestedInSelling',
      type: 'multiple-choice',
      title: "Are You Interested in Selling?",
      description: "Select your current interest in selling your business",
      required: true,
      field: 'interestedInSelling',
      options: [
        "Yes, I'm interested in selling now",
        "Yes, in the next 1-3 years",
        "Not yet interested"
      ]
    },
    {
      id: 'final',
      type: 'final',
      title: "Thanks! We'll be in touch soon.",
      description: "We'll analyze your responses and get back to you promptly."
    }
  ];

  const validateCurrentQuestion = (): boolean => {
    const currentQ = questions[currentQuestion];

    // Special handling for the contact info screen
    if (currentQ.id === 'contactInfo') {
      const newErrors: Errors = {};
      let hasError = false;

      if (!formData.name) {
        newErrors.name = 'Your name is required';
        hasError = true;
      }

      if (!formData.email) {
        newErrors.email = 'Your email is required';
        hasError = true;
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = 'Please enter a valid email address';
        hasError = true;
      }

      if (!formData.phone) {
        newErrors.phone = 'Your phone number is required';
        hasError = true;
      }

      if (!formData.website) {
        newErrors.website = 'Your website is required';
        hasError = true;
      }

      if (!formData.preferredContact) {
        newErrors.preferredContact = 'Please select a preferred contact method';
        hasError = true;
      }

      setErrors(newErrors);
      return !hasError;
    }

    // Standard validation for other question types
    if (!currentQ.required || !currentQ.field) return true;

    const fieldValue = formData[currentQ.field];

    // Skip validation if question depends on a condition that's not met
    if (currentQ.dependsOn && formData[currentQ.dependsOn.field] !== currentQ.dependsOn.value) {
      return true;
    }

    if (currentQ.type === 'checkboxes') {
      if (!fieldValue || (fieldValue as string[]).length === 0) {
        setErrors({ [currentQ.field]: 'Please select at least one option' });
        return false;
      }
    } else if (!fieldValue) {
      setErrors({ [currentQ.field]: 'This field is required' });
      return false;
    } else if (currentQ.type === 'email' && !/\S+@\S+\.\S+/.test(fieldValue as string)) {
      setErrors({ [currentQ.field]: 'Please enter a valid email address' });
      return false;
    }

    setErrors({});
    return true;
  };

  const shouldShowQuestion = (question: Question): boolean => {
    if (!question.dependsOn) return true;
    return formData[question.dependsOn.field] === question.dependsOn.value;
  };

  const getNextQuestionIndex = (currentIndex: number): number => {
    let nextIndex = currentIndex + 1;
    while (nextIndex < questions.length) {
      const dependency = questions[nextIndex].dependsOn;
      // If no dependency exists, keep this question
      if (!dependency) {
        break;
      }
      // If dependency exists, check if the field and value match
      if (dependency.field && formData[dependency.field] !== dependency.value) {
        nextIndex++;
      } else {
        break;
      }
    }
    return nextIndex;
  };

  const handleNext = () => {
    if (currentQuestion === questions.length - 1) {
      handleSubmit();
      return;
    }

    if (validateCurrentQuestion()) {
      setAnimateIn(false);
      setTimeout(() => {
        const nextIndex = getNextQuestionIndex(currentQuestion);
        setCurrentQuestion(nextIndex);
        setAnimateIn(true);
      }, 300);
    }
  };

  const getPreviousQuestionIndex = (currentIndex: number): number => {
    let prevIndex = currentIndex - 1;
    while (prevIndex >= 0) {
      const dependency = questions[prevIndex].dependsOn;
      // If no dependency exists, keep this question
      if (!dependency) {
        break;
      }
      // If dependency exists, check if the field and value match
      if (dependency.field && formData[dependency.field] !== dependency.value) {
        prevIndex--;
      } else {
        break;
      }
    }
    return prevIndex;
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setAnimateIn(false);
      setTimeout(() => {
        const prevIndex = getPreviousQuestionIndex(currentQuestion);
        setCurrentQuestion(prevIndex);
        setAnimateIn(true);
      }, 300);
    }
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name as keyof FormData]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleCheckboxChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked } = e.target;
    setFormData(prev => {
      const currentValues = prev[name as keyof FormData] as string[] || [];
      return {
        ...prev,
        [name as keyof FormData]: checked
          ? [...currentValues, value]
          : currentValues.filter(item => item !== value)
      };
    });
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleOptionSelect = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    setSubmitStatus('Submitting...');

    try {
      await fetch('/api/send-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          subject: `New Business Survey from ${formData.name}`,
        }),
      });

      setSubmitStatus('Submitted successfully! We will be in touch soon.');
      setTimeout(() => onClose(), 2000);

    } catch (err) {
      console.error(err);
      setSubmitStatus('Error submitting form. Please try again.');
      setIsSubmitting(false);
    }
  };

  const renderQuestion = () => {
    const question = questions[currentQuestion];

    if (!question) return null;

    const commonClasses = "transition-all duration-300 w-full max-w-xl";
    const animationClasses = animateIn
      ? "opacity-100 translate-y-0"
      : "opacity-0 translate-y-8";

    switch (question.type) {
      case 'contact':
        return (
          <div className={`${commonClasses} ${animationClasses}`}>
            <h2 className="text-3xl font-bold mb-3">{question.title}</h2>

            <div className="space-y-6">
              <div>
                <label className="block text-lg mb-2">Your Name</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Full Name"
                  className="w-full bg-[#2318f6] border-b-2 border-white text-white text-xl py-2 px-4 focus:outline-none focus:border-[#ff007f] placeholder-gray-400"
                />
                {errors.name && <p className="text-[#ff007f] mt-1">{errors.name}</p>}
              </div>

              <div>
                <label className="block text-lg mb-2">Email</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="<EMAIL>"
                  className="w-full bg-[#2318f6] border-b-2 border-white text-white text-xl py-2 px-4 focus:outline-none focus:border-[#ff007f] placeholder-gray-400"
                />
                {errors.email && <p className="text-[#ff007f] mt-1">{errors.email}</p>}
              </div>

              <div>
                <label className="block text-lg mb-2">Phone</label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  placeholder="(*************"
                  className="w-full bg-[#2318f6] border-b-2 border-white text-white text-xl py-2 px-4 focus:outline-none focus:border-[#ff007f] placeholder-gray-400"
                />
                {errors.phone && <p className="text-[#ff007f] mt-1">{errors.phone}</p>}
              </div>

              <div>
                <label className="block text-lg mb-2">Website</label>
                <input
                  type="text"
                  name="website"
                  value={formData.website}
                  onChange={handleInputChange}
                  placeholder="www.yourcompany.com"
                  className="w-full bg-[#2318f6] border-b-2 border-white text-white text-xl py-2 px-4 focus:outline-none focus:border-[#ff007f] placeholder-gray-400"
                />
                {errors.website && <p className="text-[#ff007f] mt-1">{errors.website}</p>}
              </div>

              <div>
                <label className="block text-lg mb-2">Preferred Contact Method</label>
                <div className="flex flex-wrap gap-3">
                  {preferredContactOptions.map((method) => (
                    <div
                      key={method}
                      onClick={() => handleOptionSelect('preferredContact', method)}
                      className={`px-4 py-2 rounded-lg border-2 cursor-pointer transition-all ${formData.preferredContact === method
                        ? 'border-[#ff007f] bg-[#ff007f]/20'
                        : 'border-white/30 hover:border-white/70'
                        }`}
                    >
                      {method}
                    </div>
                  ))}
                </div>
                {errors.preferredContact && <p className="text-[#ff007f] mt-1">{errors.preferredContact}</p>}
              </div>
            </div>
          </div>
        );

      case 'text':
        return (
          <div className={`${commonClasses} ${animationClasses}`}>
            <h2 className="text-3xl font-bold mb-3">{question.title}</h2>
            <p className="text-lg mb-6 text-gray-200">{question.description}</p>
            <input
              type="text"
              name={question.field}
              value={formData[question.field as keyof FormData] || ''}
              onChange={handleInputChange}
              placeholder={question.placeholder}
              className="w-full bg-[#2318f6] border-b-2 border-white text-white text-xl py-3 px-4 focus:outline-none focus:border-[#ff007f] placeholder-gray-400"
            />
            {errors[question.field!] && (
              <p className="text-[#ff007f] mt-2">{errors[question.field!]}</p>
            )}
          </div>
        );

      case 'multiple-choice':
        return (
          <div className={`${commonClasses} ${animationClasses}`}>
            <h2 className="text-3xl font-bold mb-3">{question.title}</h2>
            {question.description && <p className="text-lg mb-6 text-gray-200">{question.description}</p>}
            <div className="space-y-3">
              {question.options?.map((option, idx) => (
                <div
                  key={idx}
                  onClick={() => handleOptionSelect(question.field as keyof FormData, option)}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-150 ${formData[question.field as keyof FormData] === option
                    ? 'border-[#ff007f] bg-[#ff007f]/20'
                    : 'border-white/30 hover:border-white/70'
                    }`}
                >
                  <p className="text-lg">{option}</p>
                </div>
              ))}
            </div>
            {errors[question.field!] && (
              <p className="text-[#ff007f] mt-2">{errors[question.field!]}</p>
            )}
          </div>
        );

      case 'checkboxes':
        return (
          <div className={`${commonClasses} ${animationClasses}`}>
            <h2 className="text-3xl font-bold mb-3">{question.title}</h2>
            {question.description && <p className="text-lg mb-6 text-gray-200">{question.description}</p>}
            <div className="space-y-3">
              {question.options?.map((option, idx) => (
                <div key={idx} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`${question.field}-${idx}`}
                    name={question.field}
                    value={option}
                    checked={(formData[question.field as keyof FormData] as string[] || []).includes(option)}
                    onChange={handleCheckboxChange}
                    className="mr-3 h-5 w-5 rounded border-white accent-[#ff007f]"
                  />
                  <label
                    htmlFor={`${question.field}-${idx}`}
                    className="text-lg cursor-pointer"
                  >
                    {option}
                  </label>
                </div>
              ))}
            </div>
            {errors[question.field!] && (
              <p className="text-[#ff007f] mt-2">{errors[question.field!]}</p>
            )}
          </div>
        );

      case 'final':
        return (
          <div className={`${commonClasses} ${animationClasses} text-center`}>
            <h2 className="text-4xl font-bold mb-4">{question.title}</h2>
            <p className="text-xl mb-8">{question.description}</p>
            <button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className={`bg-[#ff007f] hover:bg-[#d9006c] text-white font-bold py-3 px-8 rounded-lg flex items-center justify-center mx-auto ${isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
                }`}
            >
              {isSubmitting ? 'Submitting...' : 'Submit'}
            </button>
            {submitStatus && (
              <p className={`mt-4 ${submitStatus.includes('success') ? 'text-green-400' :
                submitStatus.includes('Error') ? 'text-[#ff007f]' :
                  'text-white'
                }`}>
                {submitStatus}
              </p>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  const visibleQuestions = questions.filter(shouldShowQuestion);
  const currentVisibleIndex = visibleQuestions.findIndex(q => q.id === questions[currentQuestion].id);
  const progress = ((currentVisibleIndex) / (visibleQuestions.length - 1)) * 100;

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      } else if (e.key === 'Enter' && !e.shiftKey) {
        // Prevent form submission on text fields
        if (document.activeElement?.tagName !== 'INPUT') {
          handleNext();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentQuestion, formData]);

  return (
    <div className="fixed inset-0 z-50 flex flex-col bg-[#2318f6] text-white overflow-y-auto">
      <button
        onClick={onClose}
        className="absolute top-4 right-4 p-2 rounded-full hover:bg-[#ff007f]/20 transition-colors"
        aria-label="Close form"
      >
        <X size={24} />
      </button>

      <div className="w-full h-1 bg-white/20">
        <div
          className="h-full bg-[#ff007f] transition-all duration-300"
          style={{ width: `${progress}%` }}
        ></div>
      </div>

      <div className="flex-1 flex flex-col items-center justify-center p-6">
        {renderQuestion()}
      </div>

      <div className="p-6 flex justify-between">
        <button
          onClick={handlePrevious}
          disabled={currentQuestion === 0}
          className={`flex items-center ${currentQuestion === 0 ? 'invisible' : 'text-white hover:text-[#ff007f]'
            }`}
        >
          <ArrowLeft size={20} className="mr-2" /> Back
        </button>

        {currentQuestion < questions.length - 1 && (
          <button
            onClick={handleNext}
            className="flex items-center text-white hover:text-[#ff007f]"
          >
            Next <ArrowRight size={20} className="ml-2" />
          </button>
        )}
      </div>
    </div>
  );
}