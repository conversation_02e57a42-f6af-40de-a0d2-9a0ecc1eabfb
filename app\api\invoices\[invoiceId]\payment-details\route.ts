import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase/config';
import { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ invoiceId: string }> }
) {
  try {
    const { invoiceId } = await params;

    if (!invoiceId) {
      return NextResponse.json(
        { error: 'Invoice ID is required' },
        { status: 400 }
      );
    }

    // Get the most recent custom order for this invoice
    const customOrdersRef = collection(db, 'custom_orders');
    const q = query(
      customOrdersRef,
      where('invoiceId', '==', invoiceId),
      orderBy('createdAt', 'desc'),
      limit(1)
    );
    
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return NextResponse.json(
        { error: 'Payment details not found' },
        { status: 404 }
      );
    }

    const paymentDoc = querySnapshot.docs[0];
    const payment = {
      id: paymentDoc.id,
      ...paymentDoc.data()
    };

    return NextResponse.json({
      success: true,
      payment,
    });

  } catch (error) {
    console.error('Error fetching payment details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch payment details' },
      { status: 500 }
    );
  }
}
