'use client';

import { useState, useEffect } from 'react';
import { CreditCard, ArrowLeft, Lock } from 'lucide-react';
import { useCart } from '@/lib/context/cart-context';
import StripePaymentForm from '@/components/ui/stripe-payment-form';
import { createOrder } from '@/lib/firebase/orders';

interface ShippingOption {
  id: string;
  name: string;
  description: string;
  price: number;
  estimatedDays: string;
}

interface CustomerInfo {
  email: string;
  phone: string;
  shippingAddress: {
    firstName: string;
    lastName: string;
    line1: string;
    line2: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  billingAddress: any;
  sameAsBilling: boolean;
}

interface PaymentStepProps {
  customerInfo: CustomerInfo;
  selectedShipping: ShippingOption | null;
  onBack: () => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  setIsProcessingPayment: (processing: boolean) => void;
}

export default function PaymentStep({
  customerInfo,
  selectedShipping,
  onBack,
  isLoading,
  setIsLoading,
  setIsProcessingPayment
}: PaymentStepProps) {
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [orderId, setOrderId] = useState<string | null>(null);
  const { state, dispatch } = useCart();

  // Create payment intent when component mounts
  useEffect(() => {
    createPaymentIntent();
  }, []);

  const createPaymentIntent = async () => {
    setIsProcessing(true);
    setPaymentError(null);

    try {
      const response = await fetch('/api/checkout/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerInfo,
          selectedShipping,
          items: state.items,
        }),
      });

      const data = await response.json();

      if (data.clientSecret) {
        setClientSecret(data.clientSecret);
        setOrderId(data.orderId);
      } else {
        throw new Error('Failed to create payment intent');
      }
    } catch (error) {
      console.error('Error creating payment intent:', error);
      setPaymentError(error instanceof Error ? error.message : 'Failed to initialize payment');
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePaymentSuccess = async (paymentIntentId: string) => {
    let firebaseOrderId: string | null = null;
    try {
      setIsProcessing(true);
      setIsProcessingPayment(true); // Show processing overlay

      // Create order in Firebase
      firebaseOrderId = await createOrder({
        orderNumber: orderId!,
        customerEmail: customerInfo.email,
        customerPhone: customerInfo.phone,
        shippingAddress: customerInfo.shippingAddress,
        billingAddress: customerInfo.sameAsBilling ? customerInfo.shippingAddress : customerInfo.billingAddress,
        sameAsBilling: customerInfo.sameAsBilling,
        items: state.items.map(item => ({
          ...item,
          total: item.price * item.quantity
        })),
        subtotal: state.subtotal,
        shippingCost: selectedShipping?.price || 0,
        total: state.subtotal + (selectedShipping?.price || 0),
        shippingMethod: selectedShipping!,
        paymentIntentId,
        paymentStatus: 'paid',
        status: 'confirmed',
      });

      // Confirm order via API
      const confirmResponse = await fetch('/api/checkout/confirm-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId: firebaseOrderId,
          paymentIntentId,
          customerInfo,
          selectedShipping,
          items: state.items,
          subtotal: state.subtotal,
        }),
      });

      const confirmData = await confirmResponse.json();

      if (confirmData.success) {
        console.log('✅ Order confirmed successfully, redirecting to success page');
        console.log('🔗 Redirect URL:', `/success?order_id=${firebaseOrderId}`);

        // Set a flag to indicate order completion before clearing cart
        sessionStorage.setItem('order-just-completed', firebaseOrderId);

        // Clear cart immediately to prevent issues
        localStorage.removeItem('milos-Menagerie-cart');
        dispatch({ type: 'CLEAR_CART' });

        // Use a more reliable redirect method with timeout for mobile
        const redirectUrl = `/success?order_id=${firebaseOrderId}`;

        // Try immediate redirect first
        try {
          window.location.replace(redirectUrl);
        } catch (redirectError) {
          console.error('❌ Immediate redirect failed, trying with timeout:', redirectError);

          // Fallback with timeout for mobile browsers
          setTimeout(() => {
            try {
              window.location.href = redirectUrl;
            } catch (fallbackError) {
              console.error('❌ All redirect methods failed:', fallbackError);
              // Last resort - show success message and manual link
              alert(`Order confirmed! Please go to: ${window.location.origin}${redirectUrl}`);
            }
          }, 100);
        }
        
        // Don't reset processing states on success since we're redirecting
        return;
      } else {
        throw new Error(confirmData.error || 'Failed to confirm order');
      }
    } catch (error) {
      console.error('❌ Error confirming order:', error);
      console.log('🔍 Payment Intent ID:', paymentIntentId);
      console.log('🔍 Firebase Order ID:', firebaseOrderId);

      // Clear the processing overlay
      setIsProcessingPayment(false);

      // Set a more helpful error message
      const errorMessage = `Payment succeeded but order confirmation failed. Your payment has been processed. Please contact support with Payment ID: ${paymentIntentId}`;
      setPaymentError(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePaymentError = (error: string) => {
    setPaymentError(error);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-medium flex items-center">
          <CreditCard size={20} className="mr-2" />
          Payment
        </h2>
        <button
          onClick={onBack}
          className="flex items-center text-gray-600 hover:text-gray-800 text-sm"
        >
          <ArrowLeft size={16} className="mr-1" />
          Back to Shipping
        </button>
      </div>

      {/* Order Summary */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <h3 className="font-medium mb-3">Order Summary</h3>
        
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span>Shipping to:</span>
            <span className="text-right">
              {customerInfo.shippingAddress.firstName} {customerInfo.shippingAddress.lastName}<br />
              {customerInfo.shippingAddress.city}, {customerInfo.shippingAddress.state}
            </span>
          </div>
          
          {selectedShipping && (
            <div className="flex justify-between">
              <span>Shipping method:</span>
              <span className="text-right">
                {selectedShipping.name}<br />
                <span className="text-gray-600">{selectedShipping.estimatedDays}</span>
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Payment Form */}
      <div className="space-y-6">
        {clientSecret ? (
          <StripePaymentForm
            clientSecret={clientSecret}
            onPaymentSuccess={handlePaymentSuccess}
            onPaymentError={handlePaymentError}
            isProcessing={isProcessing}
            setIsProcessing={setIsProcessing}
          />
        ) : (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
            <p className="mt-2 text-gray-600">Initializing payment...</p>
          </div>
        )}

        {/* Billing Address */}
        {!customerInfo.sameAsBilling && (
          <div>
            <h3 className="font-medium mb-4">Billing Address</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-600">
                Billing address form would go here if different from shipping
              </p>
            </div>
          </div>
        )}

        {/* Security Notice */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <Lock size={16} className="text-green-600 mr-2" />
            <span className="text-sm text-green-700">
              Your payment information is secure and encrypted
            </span>
          </div>
        </div>

        {/* Error Display */}
        {paymentError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-700 text-sm">{paymentError}</p>
          </div>
        )}

        {/* Back Button */}
        <div className="flex justify-start pt-6 border-t">
          <button
            onClick={onBack}
            disabled={isProcessing}
            className="bg-gray-200 text-black font-medium py-3 px-6 rounded-lg hover:bg-gray-300 disabled:opacity-50 transition-colors"
          >
            Back to Shipping
          </button>
        </div>

        {/* Terms */}
        <div className="text-center">
          <p className="text-xs text-gray-500">
            By placing your order, you agree to our{' '}
            <a href="/terms" className="underline hover:text-gray-700">Terms of Service</a>
            {' '}and{' '}
            <a href="/privacy" className="underline hover:text-gray-700">Privacy Policy</a>
          </p>
        </div>
      </div>
    </div>
  );
}
