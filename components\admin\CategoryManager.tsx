'use client';

import { useState, useEffect } from 'react';
import { Plus, Edit2, Trash2, Save, X } from 'lucide-react';
import { ProductCategory, addCategory, getCategories, updateCategory, deleteCategory } from '@/lib/firebase/products';

export default function CategoryManager() {
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newCategory, setNewCategory] = useState({ name: '', description: '' });

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setIsLoading(true);
      const fetchedCategories = await getCategories();
      setCategories(fetchedCategories);
    } catch (error) {
      console.error('Error loading categories:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddCategory = async () => {
    if (!newCategory.name.trim()) return;

    try {
      await addCategory(newCategory);
      setNewCategory({ name: '', description: '' });
      setIsAdding(false);
      await loadCategories();
    } catch (error) {
      console.error('Error adding category:', error);
    }
  };

  const handleUpdateCategory = async (categoryId: string, updates: Partial<ProductCategory>) => {
    try {
      await updateCategory(categoryId, updates);
      setEditingId(null);
      await loadCategories();
    } catch (error) {
      console.error('Error updating category:', error);
    }
  };

  const handleDeleteCategory = async (categoryId: string) => {
    if (!confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      return;
    }

    try {
      await deleteCategory(categoryId);
      await loadCategories();
    } catch (error) {
      console.error('Error deleting category:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-editorial font-normal text-black">
          Product Categories
        </h2>
        <button
          onClick={() => setIsAdding(true)}
          className="flex items-center gap-2 bg-black text-white px-4 py-2 rounded hover:bg-gray-800 transition-colors"
        >
          <Plus size={16} />
          Add Category
        </button>
      </div>

      {/* Add New Category Form */}
      {isAdding && (
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <h3 className="font-helvetica font-medium mb-4">Add New Category</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-helvetica font-medium text-gray-700 mb-1">
                Category Name *
              </label>
              <input
                type="text"
                value={newCategory.name}
                onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
                placeholder="e.g., Earrings, Chess Sets, Paintings"
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
              />
            </div>
            <div>
              <label className="block text-sm font-helvetica font-medium text-gray-700 mb-1">
                Description (Optional)
              </label>
              <textarea
                value={newCategory.description}
                onChange={(e) => setNewCategory({ ...newCategory, description: e.target.value })}
                placeholder="Brief description of this category"
                rows={2}
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
              />
            </div>
            <div className="flex gap-2">
              <button
                onClick={handleAddCategory}
                disabled={!newCategory.name.trim()}
                className="flex items-center gap-2 bg-black text-white px-4 py-2 rounded hover:bg-gray-800 disabled:bg-gray-400 transition-colors"
              >
                <Save size={16} />
                Save Category
              </button>
              <button
                onClick={() => {
                  setIsAdding(false);
                  setNewCategory({ name: '', description: '' });
                }}
                className="flex items-center gap-2 bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition-colors"
              >
                <X size={16} />
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Categories List */}
      <div className="space-y-4">
        {categories.length === 0 ? (
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <p className="text-gray-500 font-helvetica">No categories created yet.</p>
            <p className="text-sm text-gray-400 mt-2">Add your first category to get started.</p>
          </div>
        ) : (
          categories.map((category) => (
            <CategoryCard
              key={category.id}
              category={category}
              isEditing={editingId === category.id}
              onEdit={() => setEditingId(category.id)}
              onCancelEdit={() => setEditingId(null)}
              onUpdate={(updates) => handleUpdateCategory(category.id, updates)}
              onDelete={() => handleDeleteCategory(category.id)}
            />
          ))
        )}
      </div>
    </div>
  );
}

interface CategoryCardProps {
  category: ProductCategory;
  isEditing: boolean;
  onEdit: () => void;
  onCancelEdit: () => void;
  onUpdate: (updates: Partial<ProductCategory>) => void;
  onDelete: () => void;
}

function CategoryCard({ category, isEditing, onEdit, onCancelEdit, onUpdate, onDelete }: CategoryCardProps) {
  const [editForm, setEditForm] = useState({
    name: category.name,
    description: category.description || ''
  });

  const handleSave = () => {
    onUpdate(editForm);
  };

  const handleCancel = () => {
    setEditForm({
      name: category.name,
      description: category.description || ''
    });
    onCancelEdit();
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      {isEditing ? (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-helvetica font-medium text-gray-700 mb-1">
              Category Name *
            </label>
            <input
              type="text"
              value={editForm.name}
              onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-black"
            />
          </div>
          <div>
            <label className="block text-sm font-helvetica font-medium text-gray-700 mb-1">
              Description (Optional)
            </label>
            <textarea
              value={editForm.description}
              onChange={(e) => setEditForm({ ...editForm, description: e.target.value })}
              rows={2}
              className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-black"
            />
          </div>
          <div className="flex gap-2">
            <button
              onClick={handleSave}
              disabled={!editForm.name.trim()}
              className="flex items-center gap-2 bg-black text-white px-3 py-1 rounded text-sm hover:bg-gray-800 disabled:bg-gray-400 transition-colors"
            >
              <Save size={14} />
              Save
            </button>
            <button
              onClick={handleCancel}
              className="flex items-center gap-2 bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600 transition-colors"
            >
              <X size={14} />
              Cancel
            </button>
          </div>
        </div>
      ) : (
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-helvetica font-medium text-lg text-black">
              {category.name}
            </h3>
            {category.description && (
              <p className="text-gray-600 text-sm mt-1 font-helvetica">
                {category.description}
              </p>
            )}
          </div>
          <div className="flex gap-2">
            <button
              onClick={onEdit}
              className="p-2 text-gray-600 hover:text-black hover:bg-gray-100 rounded transition-colors"
              title="Edit category"
            >
              <Edit2 size={16} />
            </button>
            <button
              onClick={onDelete}
              className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
              title="Delete category"
            >
              <Trash2 size={16} />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
