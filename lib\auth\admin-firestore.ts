import { db } from '../firebase/config';
import { doc, getDoc, collection, query, where, getDocs, setDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { User } from 'firebase/auth';

export interface AdminUser {
  uid: string;
  email: string;
  displayName?: string;
  role: 'super_admin' | 'admin' | 'moderator' | 'artist';
  permissions: string[];
  isActive: boolean;
  createdAt: any;
  lastLoginAt?: any;
  createdBy?: string;
  metadata?: Record<string, any>;
  artistId?: string; // Reference to artist profile if role is 'artist'
}

export interface AdminRole {
  name: string;
  permissions: string[];
  description: string;
}

/**
 * Check if a user is an admin by looking them up in Firestore
 */
export const isUserAdmin = async (user: User): Promise<AdminUser | null> => {
  try {
    if (!user?.uid) return null;

    const adminDoc = await getDoc(doc(db, 'admins', user.uid));
    
    if (!adminDoc.exists()) {
      return null;
    }

    const adminData = adminDoc.data() as AdminUser;
    
    // Check if admin is active
    if (!adminData.isActive) {
      return null;
    }

    return {
      uid: user.uid,
      email: user.email!,
      displayName: adminData.displayName,
      role: adminData.role,
      permissions: adminData.permissions,
      isActive: adminData.isActive,
      createdAt: adminData.createdAt,
      lastLoginAt: adminData.lastLoginAt,
      createdBy: adminData.createdBy,
      metadata: adminData.metadata,
      artistId: adminData.artistId // Include artistId field
    };
  } catch (error) {
    console.error('Error checking admin status:', error);
    return null;
  }
};

/**
 * Check if admin has specific permission
 */
export const hasPermission = (admin: AdminUser, permission: string): boolean => {
  // Super admin or wildcard permission
  if (admin.permissions.includes('*')) {
    return true;
  }
  
  return admin.permissions.includes(permission);
};

/**
 * Get admin by email (for login validation)
 */
export const getAdminByEmail = async (email: string): Promise<AdminUser | null> => {
  try {
    const adminsRef = collection(db, 'admins');
    const q = query(adminsRef, where('email', '==', email), where('isActive', '==', true));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return null;
    }
    
    const adminDoc = querySnapshot.docs[0];
    return {
      uid: adminDoc.id,
      ...adminDoc.data()
    } as AdminUser;
  } catch (error) {
    console.error('Error getting admin by email:', error);
    return null;
  }
};

/**
 * Update admin's last login time
 */
export const updateAdminLastLogin = async (uid: string): Promise<void> => {
  try {
    const adminRef = doc(db, 'admins', uid);
    await updateDoc(adminRef, {
      lastLoginAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error updating last login:', error);
  }
};

/**
 * Create a new admin user
 */
export const createAdminUser = async (
  uid: string,
  adminData: Omit<AdminUser, 'uid' | 'createdAt'>,
  createdByUid: string
): Promise<void> => {
  try {
    const adminRef = doc(db, 'admins', uid);
    await setDoc(adminRef, {
      ...adminData,
      createdAt: serverTimestamp(),
      createdBy: createdByUid,
      isActive: true
    });
  } catch (error) {
    console.error('Error creating admin user:', error);
    throw error;
  }
};

/**
 * Get all admin users (for management)
 */
export const getAllAdmins = async (): Promise<AdminUser[]> => {
  try {
    const adminsRef = collection(db, 'admins');
    const querySnapshot = await getDocs(adminsRef);
    
    return querySnapshot.docs.map(doc => ({
      uid: doc.id,
      ...doc.data()
    } as AdminUser));
  } catch (error) {
    console.error('Error getting all admins:', error);
    return [];
  }
};

/**
 * Deactivate an admin user
 */
export const deactivateAdmin = async (uid: string): Promise<void> => {
  try {
    const adminRef = doc(db, 'admins', uid);
    await updateDoc(adminRef, {
      isActive: false
    });
  } catch (error) {
    console.error('Error deactivating admin:', error);
    throw error;
  }
};

/**
 * Create an artist admin user when application is approved
 */
export const createArtistAdminUser = async (
  firebaseUid: string, 
  artistData: {
    email: string;
    firstName: string;
    lastName: string;
    artistId: string;
  },
  creatorUid: string
): Promise<void> => {
  try {
    const adminUser: AdminUser = {
      uid: firebaseUid,
      email: artistData.email,
      displayName: `${artistData.firstName} ${artistData.lastName}`,
      role: 'artist',
      permissions: ['manage_products', 'create_invoice', 'view_analytics'], // Artist-specific permissions
      isActive: true,
      createdAt: serverTimestamp(),
      createdBy: creatorUid,
      artistId: artistData.artistId,
      metadata: {
        userType: 'artist',
        onboardingComplete: false
      }
    };

    await setDoc(doc(db, 'admins', firebaseUid), adminUser);
  } catch (error) {
    console.error('Error creating artist admin user:', error);
    throw error;
  }
};

/**
 * Check if user is an artist
 */
export const isUserArtist = async (user: User): Promise<AdminUser | null> => {
  try {
    const adminUser = await isUserAdmin(user);
    if (adminUser && adminUser.role === 'artist') {
      return adminUser;
    }
    return null;
  } catch (error) {
    console.error('Error checking artist status:', error);
    return null;
  }
};

/**
 * Available permissions
 */
export const ADMIN_PERMISSIONS = {
  CREATE_INVOICE: 'create_invoice',
  MANAGE_PRODUCTS: 'manage_products',
  VIEW_ANALYTICS: 'view_analytics',
  MANAGE_ADMINS: 'manage_admins',
  MANAGE_ARTISTS: 'manage_artists',
  APPROVE_APPLICATIONS: 'approve_applications',
  SEND_EMAILS: 'send_emails',
  ALL: '*'
} as const;
