'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { onAuthStateChanged, User } from 'firebase/auth';
import { auth } from '@/lib/firebase/config';
import { isUserAdmin, AdminUser } from '@/lib/auth/admin-firestore';
import ArtistDashboard from '@/components/artist/ArtistDashboard';

export default function DashboardPage() {
  const [user, setUser] = useState<User | null>(null);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  const signOut = async () => {
    try {
      await auth.signOut();
      router.push('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      console.log('Auth state changed:', user?.uid);
      
      if (user) {
        const admin = await isUserAdmin(user);
        console.log('Admin check result:', admin);
        
        if (admin && admin.role === 'artist') {
          setUser(user);
          setAdminUser(admin);
        } else if (admin && (admin.role === 'super_admin' || admin.role === 'admin')) {
          // Redirect admins to admin dashboard
          router.push('/admin');
          return;
        } else {
          // Not an artist or admin, redirect to home
          router.push('/');
          return;
        }
      } else {
        // Not logged in, redirect to home
        router.push('/');
        return;
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, [router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  if (!user || !adminUser) {
    return null;
  }

  return <ArtistDashboard user={adminUser} signOut={signOut} />;
}
