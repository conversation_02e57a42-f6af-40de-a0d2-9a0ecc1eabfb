'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Menu, X, ShoppingBag, User } from 'lucide-react';
import { useCart } from '@/lib/context/cart-context';
import CartSidebar from '@/components/ui/cart-sidebar';
import { helvetica, editorialNew } from '@/app/fonts';
import { onAuthStateChanged, User as FirebaseUser } from 'firebase/auth';
import { auth } from '@/lib/firebase/config';
import { isUserAdmin, AdminUser } from '@/lib/auth/admin-firestore';
import cx from 'classnames';

export default function NavBar() {
  const [isOpen, setIsOpen] = useState(false);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isBlinking, setIsBlinking] = useState(false);
  const [user, setUser] = useState<FirebaseUser | null>(null);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const { state } = useCart();

  // Authentication state management
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setUser(firebaseUser);
      
      if (firebaseUser) {
        // Check if user is an admin/artist
        const admin = await isUserAdmin(firebaseUser);
        setAdminUser(admin);
      } else {
        setAdminUser(null);
      }
    });

    return () => unsubscribe();
  }, []);

  // Milo blinking animation - every 5 seconds, blink for 0.5 seconds
  useEffect(() => {
    const blinkInterval = setInterval(() => {
      setIsBlinking(true);
      setTimeout(() => {
        setIsBlinking(false);
      }, 500); // Blink for 0.5 seconds
    }, 10000); // Every 5 seconds

    return () => clearInterval(blinkInterval);
  }, []);

  const formatMenuText = (text: string) => {
    if (!text) return '';
    const firstLetter = text.charAt(0);
    const restOfText = text.slice(1);
    
    return (
      <span>
        <span className="text-6xl">{firstLetter}</span>
        {restOfText}
      </span>
    );
  };

  return (
    <div className={cx(helvetica.variable, editorialNew.variable)}>
      <nav className="fixed top-0 left-0 right-0 z-30 bg-white/95 backdrop-blur-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="flex justify-between items-center h-20">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-3 md:space-x-3 space-x-1">
              <Image
                src="/logo.png"
                alt="Milo's Menagerie Logo"
                width={140}
                height={40}
                className="object-contain"
              />
              <Image
                src={isBlinking ? "/milo blink.png" : "/milo.png"}
                alt="Milo"
                width={110}
                height={40}
                className="object-contain transition-opacity duration-100"
              />
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-6">
              <Link href="/" className="text-black hover:text-gray-600 transition-colors font-helvetica font-bold">
                Explore the Menagerie
              </Link>
              
              {/* Only show these links for non-authenticated users */}
              {!adminUser && (
                <>
                  <Link href="/commissions" className="text-black hover:text-gray-600 transition-colors font-helvetica font-bold">
                    Studio Commissions
                  </Link>
                  <Link href="/about" className="text-black hover:text-gray-600 transition-colors font-helvetica font-bold">
                    About
                  </Link>
                  <Link href="/contact" className="text-black hover:text-gray-600 transition-colors font-helvetica font-bold">
                    Contact
                  </Link>
                </>
              )}
              
              {/* Show different link based on user status */}
              {adminUser?.artistId ? (
                // Approved artist - show dashboard link
                <Link href="/dashboard" className="text-black hover:text-gray-600 transition-colors font-helvetica font-bold">
                  My Dashboard
                </Link>
              ) : null}
              
              {/* User Account Section */}
              {adminUser ? (
                <div className="flex items-center space-x-4">
                  {/* Show admin link for super admins */}
                  {adminUser.role === 'admin' && !adminUser.artistId && (
                    <Link href="/admin" className="text-black hover:text-gray-600 transition-colors font-helvetica font-bold">
                      Admin
                    </Link>
                  )}
                  
                  {/* User indicator */}
                  <div className="flex items-center space-x-2 text-black">
                    <User size={20} />
                    <span className="font-helvetica text-sm">
                      {adminUser.displayName || 'User'}
                    </span>
                  </div>
                </div>
              ) : null}
              
              {/* Cart Button - Only show for non-authenticated users or regular customers */}
              {!adminUser && (
                <button
                  onClick={() => setIsCartOpen(true)}
                  className="relative p-2 text-black hover:text-gray-600 transition-colors"
                >
                  <ShoppingBag size={24} />
                  {state.itemCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-black text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                      {state.itemCount}
                    </span>
                  )}
                </button>
              )}
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden flex items-center space-x-4">
              {/* Mobile Cart Button - Only show for non-authenticated users or regular customers */}
              {!adminUser && (
                <button
                  onClick={() => setIsCartOpen(true)}
                  className="relative p-2 text-black hover:text-gray-600 transition-colors"
                >
                  <ShoppingBag size={24} />
                  {state.itemCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-black text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                      {state.itemCount}
                    </span>
                  )}
                </button>
              )}
              
              <button
                onClick={() => setIsOpen(!isOpen)}
                className="text-black hover:text-gray-600 transition-colors z-50 relative"
              >
                {isOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation Overlay */}
      <div className={`fixed inset-0 z-40 md:hidden transition-all duration-500 ease-in-out ${
        isOpen ? 'opacity-100 visible' : 'opacity-0 invisible'
      }`}>
        {/* Backdrop blur */}
        <div className={`absolute inset-0 bg-white/95 backdrop-blur-md transition-all duration-500 ${
          isOpen ? 'opacity-100' : 'opacity-0'
        }`} />
        
        {/* Close button */}
        <button
          onClick={() => setIsOpen(false)}
          className="absolute top-6 right-6 z-50 p-2 text-black hover:text-gray-600 transition-colors"
        >
          <X size={32} />
        </button>
        
        {/* Menu content */}
        <div className="relative flex flex-col items-start justify-center h-full space-y-12 pl-8">
          <Link 
            href="/" 
            className={`text-black hover:text-gray-600 transition-all duration-700 transform font-editorial font-normal text-4xl ${
              isOpen ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
            }`}
            style={{ transitionDelay: '100ms' }}
            onClick={() => setIsOpen(false)}
          >
            {formatMenuText("Explore the Menagerie")}
          </Link>
          
          {/* Only show these links for non-authenticated users */}
          {!adminUser && (
            <>
              <Link 
                href="/commissions" 
                className={`text-black hover:text-gray-600 transition-all duration-700 transform font-editorial font-normal text-4xl ${
                  isOpen ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
                }`}
                style={{ transitionDelay: '200ms' }}
                onClick={() => setIsOpen(false)}
              >
                {formatMenuText("Studio Commissions")}
              </Link>
              <Link 
                href="/about" 
                className={`text-black hover:text-gray-600 transition-all duration-700 transform font-editorial font-normal text-4xl ${
                  isOpen ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
                }`}
                style={{ transitionDelay: '400ms' }}
                onClick={() => setIsOpen(false)}
              >
                {formatMenuText("About")}
              </Link>
              <Link 
                href="/contact" 
                className={`text-black hover:text-gray-600 transition-all duration-700 transform font-editorial font-normal text-4xl ${
                  isOpen ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
                }`}
                style={{ transitionDelay: '500ms' }}
                onClick={() => setIsOpen(false)}
              >
                {formatMenuText("Contact")}
              </Link>
            </>
          )}
          
          {/* Show different link based on user status */}
          {adminUser?.artistId ? (
            // Approved artist - show dashboard link
            <Link 
              href="/dashboard"
              className={`text-black hover:text-gray-600 transition-all duration-700 transform font-editorial font-normal text-4xl ${
                isOpen ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
              }`}
              style={{ transitionDelay: '300ms' }}
              onClick={() => setIsOpen(false)}
            >
              {formatMenuText("My Dashboard")}
            </Link>
          ) : null}          {/* Show admin link for super admins */}
          {adminUser?.role === 'admin' && !adminUser.artistId && (
            <Link 
              href="/admin" 
              className={`text-black hover:text-gray-600 transition-all duration-700 transform font-editorial font-normal text-4xl ${
                isOpen ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
              }`}
              style={{ transitionDelay: '600ms' }}
              onClick={() => setIsOpen(false)}
            >
              {formatMenuText("Admin")}
            </Link>
          )}
          
          {/* User info section */}
          {adminUser && (
            <div className={`flex items-center space-x-3 text-black transition-all duration-700 transform ${
              isOpen ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
            }`}
            style={{ transitionDelay: '700ms' }}>
              <User size={24} />
              <span className="font-editorial text-2xl">
                {adminUser.displayName || 'User'}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Cart Sidebar - Only show for non-authenticated users or regular customers */}
      {!adminUser && (
        <CartSidebar isOpen={isCartOpen} onClose={() => setIsCartOpen(false)} />
      )}
    </div>
  );
}
