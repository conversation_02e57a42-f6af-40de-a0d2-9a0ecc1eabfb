'use client';

import { useState } from 'react';
import { User } from 'firebase/auth';
import { submitArtistApplication, ArtistApplication } from '@/lib/firebase/artists';
import { helvetica, editorialNew } from '@/app/fonts';
import cx from 'classnames';

interface ArtistApplicationFormProps {
  user: User | null;
  onSubmit: (applicationData: Omit<ArtistApplication, 'id' | 'status' | 'submittedAt'>) => Promise<void>;
}

const artMediumOptions = [
  'Painting', 'Drawing', 'Digital Art', 'Photography', 'Sculpture', 
  'Ceramics', 'Jewelry', 'Textiles', 'Printmaking', 'Mixed Media', 'Other'
];

const experienceLevels = [
  { value: 'beginner', label: 'Beginner (0-2 years)' },
  { value: 'intermediate', label: 'Intermediate (2-5 years)' },
  { value: 'professional', label: 'Professional (5+ years)' },
  { value: 'expert', label: 'Expert (10+ years)' }
];

const salesChannels = [
  { value: 'online_store', label: 'Online Store/Website' },
  { value: 'art_fairs', label: 'Art Fairs & Markets' },
  { value: 'galleries', label: 'Galleries' },
  { value: 'social_media', label: 'Social Media' },
  { value: 'none', label: 'None - New to selling' }
];

const revenueRanges = [
  '$0 - $500/month',
  '$500 - $1,000/month', 
  '$1,000 - $2,500/month',
  '$2,500 - $5,000/month',
  '$5,000+/month',
  'Prefer not to say'
];

export default function ArtistApplicationForm({ user, onSubmit }: ArtistApplicationFormProps) {
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;
  
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    email: user?.email || '',
    phone: '',
    
    // Business Information
    businessName: '',
    website: '',
    socialMedia: {
      instagram: '',
      facebook: '',
      twitter: '',
      tiktok: '',
      other: ''
    },
    
    // Art Information
    artStyle: '',
    artMediums: [] as string[],
    experience: '',
    portfolioLinks: [''],
    
    // Business Details
    currentSalesChannels: [] as string[],
    monthlyRevenue: '',
    shippingCapability: false,
    
    // Application Details
    motivation: '',
    uniqueValue: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
        if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
        if (!formData.email.trim()) newErrors.email = 'Email is required';
        break;
      
      case 2:
        if (!formData.artStyle.trim()) newErrors.artStyle = 'Art style is required';
        if (formData.artMediums.length === 0) newErrors.artMediums = 'Select at least one art medium';
        if (!formData.experience) newErrors.experience = 'Experience level is required';
        break;
      
      case 3:
        if (formData.currentSalesChannels.length === 0) newErrors.currentSalesChannels = 'Select at least one option';
        if (!formData.monthlyRevenue) newErrors.monthlyRevenue = 'Revenue range is required';
        break;
      
      case 4:
        if (!formData.motivation.trim()) newErrors.motivation = 'Please tell us why you want to join';
        if (!formData.uniqueValue.trim()) newErrors.uniqueValue = 'Please describe what makes you unique';
        if (formData.motivation.length < 50) newErrors.motivation = 'Please provide more detail (at least 50 characters)';
        if (formData.uniqueValue.length < 50) newErrors.uniqueValue = 'Please provide more detail (at least 50 characters)';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return;

    setLoading(true);
    try {
      const applicationData: Omit<ArtistApplication, 'id' | 'status' | 'submittedAt'> = {
        ...formData,
        firebaseUid: user?.uid,
      };

      await onSubmit(applicationData);
    } catch (error) {
      console.error('Error submitting application:', error);
      alert('Failed to submit application. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const addPortfolioLink = () => {
    setFormData(prev => ({
      ...prev,
      portfolioLinks: [...prev.portfolioLinks, '']
    }));
  };

  const removePortfolioLink = (index: number) => {
    setFormData(prev => ({
      ...prev,
      portfolioLinks: prev.portfolioLinks.filter((_, i) => i !== index)
    }));
  };

  const updatePortfolioLink = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      portfolioLinks: prev.portfolioLinks.map((link, i) => i === index ? value : link)
    }));
  };

  const toggleArtMedium = (medium: string) => {
    setFormData(prev => ({
      ...prev,
      artMediums: prev.artMediums.includes(medium)
        ? prev.artMediums.filter(m => m !== medium)
        : [...prev.artMediums, medium]
    }));
  };

  const toggleSalesChannel = (channel: string) => {
    setFormData(prev => ({
      ...prev,
      currentSalesChannels: prev.currentSalesChannels.includes(channel)
        ? prev.currentSalesChannels.filter(c => c !== channel)
        : [...prev.currentSalesChannels, channel]
    }));
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <h3 className={cx(editorialNew.className, "text-2xl font-bold mb-4")}>
                Personal Information
              </h3>
              <p className="text-gray-600 mb-6">Tell us about yourself</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  First Name *
                </label>
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                />
                {errors.firstName && <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Last Name *
                </label>
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                />
                {errors.lastName && <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email *
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                disabled={!!user?.email}
              />
              {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Phone Number
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business/Artist Name
              </label>
              <input
                type="text"
                value={formData.businessName}
                onChange={(e) => setFormData(prev => ({ ...prev, businessName: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                placeholder="e.g., 'Sarah's Studio' or 'John Smith Art'"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Website
              </label>
              <input
                type="url"
                value={formData.website}
                onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                placeholder="https://yourwebsite.com"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-4">
                Social Media (Optional)
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <input
                  type="text"
                  value={formData.socialMedia.instagram}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    socialMedia: { ...prev.socialMedia, instagram: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="Instagram username"
                />
                <input
                  type="text"
                  value={formData.socialMedia.facebook}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    socialMedia: { ...prev.socialMedia, facebook: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="Facebook page"
                />
                <input
                  type="text"
                  value={formData.socialMedia.tiktok}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    socialMedia: { ...prev.socialMedia, tiktok: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="TikTok username"
                />
                <input
                  type="text"
                  value={formData.socialMedia.other}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    socialMedia: { ...prev.socialMedia, other: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="Other social media"
                />
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div>
              <h3 className={cx(editorialNew.className, "text-2xl font-bold mb-4")}>
                Your Art
              </h3>
              <p className="text-gray-600 mb-6">Tell us about your artistic practice</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Art Style/Genre *
              </label>
              <input
                type="text"
                value={formData.artStyle}
                onChange={(e) => setFormData(prev => ({ ...prev, artStyle: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                placeholder="e.g., 'Abstract expressionism', 'Botanical illustration', 'Street photography'"
              />
              {errors.artStyle && <p className="text-red-500 text-sm mt-1">{errors.artStyle}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Art Mediums * (Select all that apply)
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {artMediumOptions.map((medium) => (
                  <label key={medium} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.artMediums.includes(medium)}
                      onChange={() => toggleArtMedium(medium)}
                      className="rounded border-gray-300 text-black focus:ring-black"
                    />
                    <span className="text-sm">{medium}</span>
                  </label>
                ))}
              </div>
              {errors.artMediums && <p className="text-red-500 text-sm mt-1">{errors.artMediums}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Experience Level *
              </label>
              <div className="space-y-2">
                {experienceLevels.map((level) => (
                  <label key={level.value} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="radio"
                      name="experience"
                      value={level.value}
                      checked={formData.experience === level.value}
                      onChange={(e) => setFormData(prev => ({ ...prev, experience: e.target.value }))}
                      className="border-gray-300 text-black focus:ring-black"
                    />
                    <span className="text-sm">{level.label}</span>
                  </label>
                ))}
              </div>
              {errors.experience && <p className="text-red-500 text-sm mt-1">{errors.experience}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Portfolio Links
              </label>
              <p className="text-sm text-gray-500 mb-3">
                Share links to your work (Instagram, website, online portfolio, etc.)
              </p>
              {formData.portfolioLinks.map((link, index) => (
                <div key={index} className="flex gap-2 mb-2">
                  <input
                    type="url"
                    value={link}
                    onChange={(e) => updatePortfolioLink(index, e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                    placeholder="https://..."
                  />
                  {formData.portfolioLinks.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removePortfolioLink(index)}
                      className="px-3 py-2 text-red-500 hover:text-red-700"
                    >
                      Remove
                    </button>
                  )}
                </div>
              ))}
              <button
                type="button"
                onClick={addPortfolioLink}
                className="text-sm text-blue-500 hover:text-blue-700"
              >
                + Add another link
              </button>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <h3 className={cx(editorialNew.className, "text-2xl font-bold mb-4")}>
                Business Details
              </h3>
              <p className="text-gray-600 mb-6">Tell us about your current art business</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Current Sales Channels * (Select all that apply)
              </label>
              <div className="space-y-2">
                {salesChannels.map((channel) => (
                  <label key={channel.value} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.currentSalesChannels.includes(channel.value)}
                      onChange={() => toggleSalesChannel(channel.value)}
                      className="rounded border-gray-300 text-black focus:ring-black"
                    />
                    <span className="text-sm">{channel.label}</span>
                  </label>
                ))}
              </div>
              {errors.currentSalesChannels && <p className="text-red-500 text-sm mt-1">{errors.currentSalesChannels}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Approximate Monthly Art Revenue *
              </label>
              <select
                value={formData.monthlyRevenue}
                onChange={(e) => setFormData(prev => ({ ...prev, monthlyRevenue: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
              >
                <option value="">Select range</option>
                {revenueRanges.map((range) => (
                  <option key={range} value={range}>{range}</option>
                ))}
              </select>
              {errors.monthlyRevenue && <p className="text-red-500 text-sm mt-1">{errors.monthlyRevenue}</p>}
            </div>

            <div>
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.shippingCapability}
                  onChange={(e) => setFormData(prev => ({ ...prev, shippingCapability: e.target.checked }))}
                  className="rounded border-gray-300 text-black focus:ring-black"
                />
                <span className="text-sm">
                  I can handle packaging and shipping my artwork to customers
                </span>
              </label>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div>
              <h3 className={cx(editorialNew.className, "text-2xl font-bold mb-4")}>
                Tell Us About You
              </h3>
              <p className="text-gray-600 mb-6">Help us understand why you'd be a great fit</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Why do you want to join Milo's Menagerie? *
              </label>
              <textarea
                value={formData.motivation}
                onChange={(e) => setFormData(prev => ({ ...prev, motivation: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                rows={4}
                placeholder="Tell us what attracts you to our platform and how you align with our mission..."
              />
              <p className="text-sm text-gray-500 mt-1">{formData.motivation.length} characters (minimum 50)</p>
              {errors.motivation && <p className="text-red-500 text-sm mt-1">{errors.motivation}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                What makes your art unique? *
              </label>
              <textarea
                value={formData.uniqueValue}
                onChange={(e) => setFormData(prev => ({ ...prev, uniqueValue: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                rows={4}
                placeholder="Describe your artistic style, inspiration, techniques, or what sets your work apart..."
              />
              <p className="text-sm text-gray-500 mt-1">{formData.uniqueValue.length} characters (minimum 50)</p>
              {errors.uniqueValue && <p className="text-red-500 text-sm mt-1">{errors.uniqueValue}</p>}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-gray-500">Step {currentStep} of {totalSteps}</span>
          <span className="text-sm text-gray-500">{Math.round((currentStep / totalSteps) * 100)}% Complete</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-black h-2 rounded-full transition-all duration-300"
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          />
        </div>
      </div>

      <form onSubmit={(e) => e.preventDefault()}>
        {renderStep()}

        {/* Navigation */}
        <div className="flex justify-between mt-8 pt-6 border-t">
          <button
            type="button"
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Previous
          </button>

          {currentStep < totalSteps ? (
            <button
              type="button"
              onClick={handleNext}
              className="px-6 py-2 bg-black text-white rounded-md hover:bg-gray-800"
            >
              Next
            </button>
          ) : (
            <button
              type="button"
              onClick={handleSubmit}
              disabled={loading}
              className="px-6 py-2 bg-black text-white rounded-md hover:bg-gray-800 disabled:opacity-50"
            >
              {loading ? 'Submitting...' : 'Submit Application'}
            </button>
          )}
        </div>
      </form>
    </div>
  );
}
