
import Image from 'next/image';
import Link from 'next/link';
import { useState, useRef, useEffect } from 'react';
import { Share2 } from 'lucide-react';
import { useCart } from '@/lib/context/cart-context';
import { ProductImage } from '@/lib/firebase/products';
import { useProductAnalytics } from '@/lib/hooks/use-analytics';
import { onAuthStateChanged, User as FirebaseUser } from 'firebase/auth';
import { auth } from '@/lib/firebase/config';
import { isUserAdmin, AdminUser } from '@/lib/auth/admin-firestore';

interface ProductCardProps {
  id: string;
  title: string;
  description: string;
  images: ProductImage[];
  price: number;
  stock?: number; // Available inventory quantity
  size?: 'small' | 'medium' | 'large' | 'wide';
  onRequest?: (id: string) => void;
  artistId?: string; // Add artistId prop for order tracking
}

export default function ProductCard({ 
  id, 
  title, 
  description, 
  images = [],
  price,
  stock,
  size = 'medium',
  onRequest,
  artistId 
}: ProductCardProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [imageError, setImageError] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const { dispatch } = useCart();
  
  // Analytics tracking
  const { trackEvent } = useProductAnalytics(id);
  
  // Authentication state management
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        // Check if user is an admin/artist
        const admin = await isUserAdmin(firebaseUser);
        setAdminUser(admin);
      } else {
        setAdminUser(null);
      }
    });

    return () => unsubscribe();
  }, []);
  
  // Touch/swipe handling
  const touchStartX = useRef<number>(0);
  const touchEndX = useRef<number>(0);
  
  const sortedImages = images.sort((a, b) => a.order - b.order);
  const currentImage = sortedImages[currentImageIndex];

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % sortedImages.length);
    setImageError(false);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + sortedImages.length) % sortedImages.length);
    setImageError(false);
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.targetTouches[0].clientX;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    touchEndX.current = e.targetTouches[0].clientX;
  };

  const handleTouchEnd = () => {
    if (!touchStartX.current || !touchEndX.current) return;
    
    const distance = touchStartX.current - touchEndX.current;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && sortedImages.length > 1) {
      nextImage();
    }
    if (isRightSwipe && sortedImages.length > 1) {
      prevImage();
    }
  };

  const handleImageClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Track image click
    trackEvent('product_click', { 
      imageIndex: currentImageIndex,
      clickTarget: 'image'
    });
    // Navigate to product page instead of opening modal
    window.location.href = `/product/${id}`;
  };

  const handleCardClick = () => {
    // Track card click
    trackEvent('product_click', { 
      clickTarget: 'card'
    });
    // Navigate to individual product page instead of modal
    window.location.href = `/product/${id}`;
  };

  const sizeClasses = {
    small: 'col-span-1 row-span-1 h-72',
    medium: 'col-span-1 row-span-2 h-96',
    large: 'col-span-2 row-span-2 h-96',
    wide: 'col-span-2 row-span-1 h-72'
  };

  const handleAddToCart = (e: React.MouseEvent) => {
    console.log('🛒 ADD TO CART CLICKED! Product ID:', id);
    e.stopPropagation();
    
    // Check stock before adding
    if (typeof stock === 'number' && stock === 0) {
      return; // Don't add if out of stock
    }
    
    setIsAdding(true);
    
    // Track add to cart event
    console.log('🔥 Calling trackEvent for add_to_cart');
    trackEvent('add_to_cart', {
      price,
      imageIndex: currentImageIndex
    });
    
    dispatch({
      type: 'ADD_ITEM',
      payload: {
        id,
        title,
        description,
        imageUrl: currentImage?.url || '',
        price,
        maxStock: stock, // Include stock limit for cart validation
        artistId, // Include artistId for order tracking
      },
    });
    
    setTimeout(() => {
      setIsAdding(false);
    }, 600);
  };

  const handleShare = (e: React.MouseEvent) => {
    e.stopPropagation();
    
    // Track share click event
    trackEvent('share_click', {
      shareMethod: (navigator.share && typeof navigator.share === 'function') ? 'native' : 'clipboard'
    });
    
    const url = `${window.location.origin}/product/${id}`;
    
    if (navigator.share && typeof navigator.share === 'function') {
      navigator.share({
        title: title,
        url: url,
      });
    } else {
      navigator.clipboard.writeText(url);
      alert('Product link copied to clipboard!');
    }
  };

  const formatTitle = (title: string) => {
    if (!title) return '';
    const firstLetter = title.charAt(0);
    const restOfTitle = title.slice(1);
    
    return (
      <span>
        <span className="text-2xl">{firstLetter}</span>
        {restOfTitle}
      </span>
    );
  };

  return (
    <>
      <div 
        className={`${sizeClasses[size]} bg-white border border-gray-300 rounded-lg overflow-hidden flex flex-col cursor-pointer hover:shadow-lg transition-shadow`}
        onClick={handleCardClick}
      >
        <div 
          className="flex-1 relative overflow-hidden group"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          onClick={handleImageClick}
        >
          {currentImage && !imageError ? (
            <>
              <Image
                src={currentImage.url}
                alt={currentImage.alt || title}
                fill
                className="object-cover"
                onError={() => setImageError(true)}
              />
              
              {sortedImages.length > 1 && (
                <>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      prevImage();
                    }}
                    className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hidden md:block"
                  >
                    ←
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      nextImage();
                    }}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hidden md:block"
                  >
                    →
                  </button>
                  <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
                    {sortedImages.map((_, index) => (
                      <div
                        key={index}
                        className={`w-2 h-2 rounded-full ${
                          index === currentImageIndex ? 'bg-white' : 'bg-white bg-opacity-50'
                        }`}
                      />
                    ))}
                  </div>
                </>
              )}
            </>
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-100 text-gray-400">
              No image available
            </div>
          )}
        </div>
        
        <div className="p-3 flex flex-col justify-between min-h-[100px]">
          <div className="mb-2">
            <h3 className="font-editorial font-normal text-base text-black mb-1 leading-tight">
              {formatTitle(title)}
            </h3>
            <p className="text-gray-600 text-xs font-helvetica line-clamp-2 leading-tight mb-1">
              {description}
            </p>
            <p className="text-black font-helvetica font-medium text-sm">
              ${price.toFixed(2)}
            </p>
            {/* Stock Information */}
            {typeof stock === 'number' && (
              <p className={`text-xs font-helvetica mt-1 ${
                stock === 0 
                  ? 'text-red-600' 
                  : stock <= 5 
                    ? 'text-orange-600' 
                    : 'text-gray-500'
              }`}>
                {stock === 0 
                  ? 'Out of Stock' 
                  : stock <= 5 
                    ? `Only ${stock} left` 
                    : `${stock} in stock`
                }
              </p>
            )}
          </div>
          
          <div className="flex space-x-2">
            {/* Add to Cart button - Only show for non-authenticated users or regular customers */}
            {!adminUser && (
              <button
                onClick={handleAddToCart}
                disabled={isAdding || (typeof stock === 'number' && stock === 0)}
                className={`flex-1 font-helvetica font-medium py-2 px-3 rounded text-xs transition-all duration-300 ${
                  typeof stock === 'number' && stock === 0
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : isAdding 
                      ? 'scale-95 bg-green-600 transform text-white' 
                      : 'bg-black text-white hover:bg-gray-800 active:scale-95'
                }`}
              >
                {typeof stock === 'number' && stock === 0 
                  ? 'Out of Stock' 
                  : isAdding 
                    ? 'Added!' 
                    : 'Add to Cart'
                }
              </button>
            )}
            
            <button
              onClick={handleShare}
              className={`p-2 border border-gray-300 rounded text-gray-600 hover:text-gray-800 hover:border-gray-400 transition-colors ${
                !adminUser ? '' : 'flex-1'
              }`}
              title="Share product"
            >
              <Share2 size={16} />
            </button>
          </div>
        </div>
      </div>
    </>
  );
}







