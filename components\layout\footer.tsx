'use client';

import cx from 'classnames';
import { helvetica, editorialNew } from '../../app/fonts';
import React, { useState, useRef } from 'react';
import useIntersectionObserver from '@/lib/hooks/use-intersection-observer';
import Image from 'next/image';

export default function Footer() {
  const footerRef = useRef<HTMLDivElement>(null);
  const footerVisible = useIntersectionObserver(footerRef, { threshold: 0.2, freezeOnceVisible: true });

  return (
    <footer ref={footerRef} className="bg-white text-black py-12 md:py-16" aria-labelledby="footer-title">
      <div className={`max-w-7xl mx-auto px-4 md:px-6 transition-all duration-700 ${footerVisible?.isIntersecting ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
        }`}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">

          {/* Company Info */}
          <div className="lg:col-span-2">
            <h3 id="footer-title" className="text-2xl md:text-3xl font-normal mb-4 font-editorial">
              {footerVisible?.isIntersecting && (
                <Image
                  src="/logo.png"
                  alt="Milo's Menagerie Logo"
                  width={140}
                  height={40}
                  className="object-contain"
                />
              )}
            </h3>
            <p className="text-gray-700 mb-4 max-w-md font-helvetica">
              Step into our menagerie where creativity runs wild. Beautifully crafted thingies that inspire exploration, learning, and endless adventure.
            </p>
            <div className="space-y-2">
              <p className="text-gray-700 font-helvetica">
                <span className="font-medium">Email:</span>
                <a href="mailto:<EMAIL>" className="hover:text-black transition-colors ml-2">
                  <EMAIL>
                </a>
              </p>
            </div>

            {/* Social Media Links */}
            <div className="mt-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2 font-helvetica">Connect With Us</h4>
              <div className="flex space-x-4">
                <a
                  href="https://www.instagram.com/milo_menagerie/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-700 hover:text-black transition-colors"
                  aria-label="Instagram"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-gray-300 pt-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="mb-4 md:mb-0">
              <p className="text-gray-600 text-sm font-helvetica">
                © {new Date().getFullYear()} Milo's Menagerie. All rights reserved.
              </p>
            </div>
            <div className="flex space-x-6 text-sm font-helvetica">
              <a href="/privacy" className="text-gray-600 hover:text-black transition-colors">
                Privacy Policy
              </a>
              <a href="/terms" className="text-gray-600 hover:text-black transition-colors">
                Terms of Service
              </a>
              <a href="/contact" className="text-gray-600 hover:text-black transition-colors">
                Contact
              </a>
              <a href="/artist/login" className="text-gray-600 hover:text-black transition-colors">
                Artist Login
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
