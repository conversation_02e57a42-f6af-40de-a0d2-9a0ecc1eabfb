import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase/config';
import { collection, doc, setDoc, serverTimestamp } from 'firebase/firestore';

interface InvoiceData {
  customerName?: string;
  customerEmail?: string;
  title: string;
  description: string;
  amount: number;
  dueDate: string;
  notes: string;
  artistId?: string;
  includeShipping: boolean;
  shippingCost?: number;
  shippingNotes?: string;
}

export async function POST(req: NextRequest) {
  try {
    const invoiceData: InvoiceData = await req.json();

    // For now, we'll trust the frontend authentication
    // In the future, you can add Firebase Admin SDK verification here

    // Validate required fields (customerName and customerEmail are now optional)
    if (!invoiceData.title || !invoiceData.description || !invoiceData.amount) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Calculate total amount including shipping if applicable
    const baseAmount = invoiceData.amount;
    const shippingAmount = invoiceData.includeShipping ? (invoiceData.shippingCost || 0) : 0;
    const totalAmount = baseAmount + shippingAmount;

    // Generate unique invoice ID
    const invoiceId = `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create invoice document
    const invoice = {
      id: invoiceId,
      customerName: invoiceData.customerName,
      customerEmail: invoiceData.customerEmail,
      title: invoiceData.title,
      description: invoiceData.description,
      amount: baseAmount, // Keep original amount separate
      totalAmount: totalAmount, // Total including shipping
      dueDate: invoiceData.dueDate || null,
      notes: invoiceData.notes || '',
      artistId: invoiceData.artistId, // Track which artist created this invoice
      includeShipping: invoiceData.includeShipping,
      shippingCost: invoiceData.includeShipping ? (invoiceData.shippingCost || 0) : 0,
      shippingNotes: invoiceData.includeShipping ? (invoiceData.shippingNotes || '') : '',
      status: 'pending', // pending, paid, cancelled, expired
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      paymentLink: `${req.headers.get('origin')}/pay/${invoiceId}`,
      paidAt: null,
      paymentIntentId: null,
    };

    // Save to Firebase
    const invoiceRef = doc(db, 'invoices', invoiceId);
    await setDoc(invoiceRef, invoice);

    return NextResponse.json({
      success: true,
      invoice: {
        ...invoice,
      },
    });

  } catch (error) {
    console.error('Error creating invoice:', error);
    return NextResponse.json(
      { error: 'Failed to create invoice' },
      { status: 500 }
    );
  }
}
