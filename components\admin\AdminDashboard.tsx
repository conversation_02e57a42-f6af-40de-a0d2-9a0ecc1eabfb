import React, { useState, useEffect } from 'react';
import { Copy, Check } from 'lucide-react';
import { getProducts, addProduct, updateProduct, deleteProduct, uploadProductImages, uploadProductVideos, Product } from '@/lib/firebase/products';
import { AdminUser } from '@/lib/auth/admin-firestore';
import ProductForm from './ProductForm';
import CategoryManager from './CategoryManager';
import Link from 'next/link';

interface AdminDashboardProps {
  user: AdminUser;
  signOut: () => Promise<void>;
}

const ShareUrlButton: React.FC<{ url: string }> = ({ url }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  return (
    <button
      onClick={handleCopy}
      className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 transition-colors"
      title="Copy share URL"
    >
      {copied ? <Check size={14} /> : <Copy size={14} />}
      <span className="text-xs">{copied ? 'Copied!' : 'Copy'}</span>
    </button>
  );
};

export default function AdminDashboard({ user, signOut }: AdminDashboardProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [activeTab, setActiveTab] = useState<'products' | 'categories'>('products');

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      const productsData = await getProducts();
      console.log('Loaded products:', productsData); // Debug line
      setProducts(productsData);
    } catch (error) {
      console.error('Error loading products:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddProduct = async (productData: Omit<Product, 'id'>, imageFiles?: File[], videoFiles?: File[]) => {
    try {
      const productId = await addProduct({
        ...productData,
        images: [], // Start with empty images array
        videos: []  // Start with empty videos array
      });

      const updates: any = {};

      if (imageFiles && imageFiles.length > 0) {
        const uploadedImages = await uploadProductImages(imageFiles, productId);
        updates.images = uploadedImages;
      }

      if (videoFiles && videoFiles.length > 0) {
        const uploadedVideos = await uploadProductVideos(videoFiles, productId);
        updates.videos = uploadedVideos;
      }

      if (Object.keys(updates).length > 0) {
        await updateProduct(productId, updates);
      }

      await loadProducts();
      setShowForm(false);
    } catch (error) {
      console.error('Error adding product:', error);
      throw error;
    }
  };

  const handleUpdateProduct = async (productData: Omit<Product, 'id'>, imageFiles?: File[], videoFiles?: File[]) => {
    if (!editingProduct) return;

    try {
      let updates = { ...productData };

      if (imageFiles && imageFiles.length > 0) {
        const uploadedImages = await uploadProductImages(imageFiles, editingProduct.id!);
        updates.images = [...(productData.images || []), ...uploadedImages];
      }

      if (videoFiles && videoFiles.length > 0) {
        const uploadedVideos = await uploadProductVideos(videoFiles, editingProduct.id!);
        updates.videos = [...(productData.videos || []), ...uploadedVideos];
      }

      await updateProduct(editingProduct.id!, updates);
      await loadProducts();
      setEditingProduct(null);
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  };

  const handleDeleteProduct = async (productId: string) => {
    if (confirm('Are you sure you want to delete this product?')) {
      try {
        await deleteProduct(productId);
        await loadProducts();
      } catch (error) {
        console.error('Error deleting product:', error);
      }
    }
  };

  if (!user) return null;

  return (
    <div className="min-h-screen bg-gray-100">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 py-4 sm:py-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-3 sm:space-y-0">
            <div>
              <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-sm text-gray-600">Welcome back, {user.email}</p>
            </div>
            <div className="flex items-center space-x-2 sm:space-x-4">
              <button
                onClick={signOut}
                className="px-3 py-1 sm:px-4 sm:py-2 bg-red-600 text-white rounded hover:bg-red-700 text-sm"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 py-6 sm:py-8">
        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Link
            href="/admin/create-invoice"
            className="bg-blue-600 text-white p-6 rounded-lg hover:bg-blue-700 transition-colors text-center"
          >
            <div className="text-2xl mb-2">📧</div>
            <h3 className="font-semibold mb-1">Create Invoice</h3>
            <p className="text-sm opacity-90">Send custom payment links</p>
          </Link>

          <Link
            href="/admin/artist-applications"
            className="bg-orange-600 text-white p-6 rounded-lg hover:bg-orange-700 transition-colors text-center"
          >
            <div className="text-2xl mb-2">🎨</div>
            <h3 className="font-semibold mb-1">Artist Applications</h3>
            <p className="text-sm opacity-90">Review artist requests</p>
          </Link>

          <div className="bg-green-600 text-white p-6 rounded-lg text-center">
            <div className="text-2xl mb-2">📦</div>
            <h3 className="font-semibold mb-1">Orders</h3>
            <p className="text-sm opacity-90">View recent orders</p>
          </div>

          <div className="bg-purple-600 text-white p-6 rounded-lg text-center">
            <div className="text-2xl mb-2">📊</div>
            <h3 className="font-semibold mb-1">Analytics</h3>
            <p className="text-sm opacity-90">Sales & performance</p>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex border-b border-gray-200 mb-6">
          <button
            onClick={() => setActiveTab('products')}
            className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
              activeTab === 'products'
                ? 'border-black text-black'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Products ({products.length})
          </button>
          <button
            onClick={() => setActiveTab('categories')}
            className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
              activeTab === 'categories'
                ? 'border-black text-black'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Categories
          </button>
        </div>

        {/* Products Tab */}
        {activeTab === 'products' && (
          <>
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 space-y-2 sm:space-y-0">
              <h2 className="text-lg sm:text-xl font-semibold">Products ({products.length})</h2>
              <button
                onClick={() => setShowForm(true)}
                className="w-full sm:w-auto px-4 py-2 bg-black text-white rounded hover:bg-gray-800"
              >
                Add Product
              </button>
            </div>

            {(showForm || editingProduct) && (
              <ProductForm
                product={editingProduct}
                onSubmit={editingProduct ? handleUpdateProduct : handleAddProduct}
                onCancel={() => {
                  setShowForm(false);
                  setEditingProduct(null);
                }}
              />
            )}

            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading products...</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                {products.map((product) => {
                  const firstImage = product.images?.[0];
                  return (
                    <div key={product.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                      <div className="p-4 sm:p-6">
                        {firstImage ? (
                          <img 
                            src={firstImage.url} 
                            alt={firstImage.alt || product.title}
                            className="w-full h-40 sm:h-48 object-cover rounded mb-4"
                          />
                        ) : (
                          <div className="w-full h-40 sm:h-48 bg-gray-100 rounded mb-4 flex items-center justify-center">
                            <div className="text-gray-400 text-center">
                              <svg className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                              </svg>
                              <p className="text-xs">No image</p>
                            </div>
                          </div>
                        )}
                        <h3 className="font-semibold text-sm sm:text-base">{product.title}</h3>
                        <p className="text-gray-600 text-sm mb-2 line-clamp-2">
                          {product.description}
                        </p>
                        
                        {/* Price and Stock Info */}
                        <div className="flex justify-between items-center mb-3">
                          <p className="font-medium">${product.price.toFixed(2)}</p>
                          {typeof product.stock === 'number' && (
                            <p className={`text-xs px-2 py-1 rounded ${
                              product.stock === 0 
                                ? 'bg-red-100 text-red-700' 
                                : product.stock <= 5 
                                  ? 'bg-orange-100 text-orange-700' 
                                  : 'bg-green-100 text-green-700'
                            }`}>
                              {product.stock === 0 
                                ? 'Out of Stock' 
                                : product.stock <= 5 
                                  ? `${product.stock} left` 
                                  : `${product.stock} in stock`
                              }
                            </p>
                          )}
                        </div>
                        
                        <div className="flex space-x-2 mt-4">
                          <button
                            onClick={() => setEditingProduct(product)}
                            className="flex-1 px-2 py-1 sm:px-3 sm:py-1 bg-blue-500 text-white text-xs sm:text-sm rounded hover:bg-blue-600"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDeleteProduct(product.id!)}
                            className="flex-1 px-2 py-1 sm:px-3 sm:py-1 bg-red-500 text-white text-xs sm:text-sm rounded hover:bg-red-600"
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </>
        )}

        {/* Categories Tab */}
        {activeTab === 'categories' && (
          <CategoryManager />
        )}
      </main>
    </div>
  );
}
