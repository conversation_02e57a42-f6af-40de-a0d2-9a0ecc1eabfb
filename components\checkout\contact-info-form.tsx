'use client';

import { useState } from 'react';
import { User, Mail, Phone } from 'lucide-react';

interface ContactInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

interface ContactInfoFormProps {
  contactInfo: ContactInfo;
  onContactInfoChange: (info: ContactInfo) => void;
  errors: Record<string, string>;
}

export default function ContactInfoForm({
  contactInfo,
  onContactInfoChange,
  errors
}: ContactInfoFormProps) {

  const updateContactInfo = (field: keyof ContactInfo, value: string) => {
    const updatedInfo = { ...contactInfo, [field]: value };
    onContactInfoChange(updatedInfo);
  };

  return (
    <div className="bg-white p-6 rounded-lg border">
      <h3 className="text-lg font-medium mb-4 flex items-center">
        <User size={18} className="mr-2" />
        Contact Information
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">
            First Name *
          </label>
          <input
            type="text"
            value={contactInfo.firstName}
            onChange={(e) => updateContactInfo('firstName', e.target.value)}
            className={`w-full p-3 border rounded-lg text-base ${
              errors.firstName ? 'border-red-500' : 'border-gray-300'
            } focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent`}
            style={{ fontSize: '16px' }}
          />
          {errors.firstName && (
            <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">
            Last Name *
          </label>
          <input
            type="text"
            value={contactInfo.lastName}
            onChange={(e) => updateContactInfo('lastName', e.target.value)}
            className={`w-full p-3 border rounded-lg text-base ${
              errors.lastName ? 'border-red-500' : 'border-gray-300'
            } focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent`}
            style={{ fontSize: '16px' }}
          />
          {errors.lastName && (
            <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div>
          <label className="block text-sm font-medium mb-1">
            Email Address *
          </label>
          <input
            type="email"
            value={contactInfo.email}
            onChange={(e) => updateContactInfo('email', e.target.value)}
            className={`w-full p-3 border rounded-lg text-base ${
              errors.email ? 'border-red-500' : 'border-gray-300'
            } focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent`}
            placeholder="<EMAIL>"
            style={{ fontSize: '16px' }}
          />
          {errors.email && (
            <p className="text-red-500 text-sm mt-1">{errors.email}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">
            Phone Number *
          </label>
          <input
            type="tel"
            value={contactInfo.phone}
            onChange={(e) => updateContactInfo('phone', e.target.value)}
            className={`w-full p-3 border rounded-lg text-base ${
              errors.phone ? 'border-red-500' : 'border-gray-300'
            } focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent`}
            placeholder="(*************"
            style={{ fontSize: '16px' }}
          />
          {errors.phone && (
            <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
          )}
        </div>
      </div>
    </div>
  );
}
