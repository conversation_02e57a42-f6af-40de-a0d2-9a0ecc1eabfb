"use client";
import NavBar from './navbar';
import Footer from './footer';
import { usePathname } from 'next/navigation';

export default function NavBarWrapper() {
  const pathname = usePathname();
  const isAdminRoute = pathname.startsWith('/admin');
  if (isAdminRoute) return null;
  return <NavBar />;
}

export function MainWrapper({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const isAdminRoute = pathname.startsWith('/admin');
  
  return (
    <main className={`flex-1 w-full ${isAdminRoute ? 'pt-0' : 'pt-20'}`}>
      {children}
    </main>
  );
}

export function FooterWrapper() {
  const pathname = usePathname();
  const isAdminRoute = pathname.startsWith('/admin');
  if (isAdminRoute) return null;
  return <Footer />;
}
