import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
});

interface CartItem {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  price: number;
  quantity: number;
}

interface ShippingOption {
  id: string;
  name: string;
  description: string;
  price: number;
  estimatedDays: string;
}

interface CustomerInfo {
  email: string;
  phone: string;
  shippingAddress: {
    firstName: string;
    lastName: string;
    line1: string;
    line2: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  billingAddress: any;
  sameAsBilling: boolean;
}

export async function POST(req: NextRequest) {
  try {
    const { customerInfo, selectedShipping, items }: {
      customerInfo: CustomerInfo;
      selectedShipping: ShippingOption;
      items: CartItem[];
    } = await req.json();

    // Calculate order totals
    const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const shippingCost = selectedShipping?.price || 0;
    const total = subtotal + shippingCost;

    // Generate order ID
    const orderId = `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(total * 100), // Convert to cents
      currency: 'usd',
      metadata: {
        orderId,
        customerEmail: customerInfo.email,
        customerPhone: customerInfo.phone,
        shippingName: `${customerInfo.shippingAddress.firstName} ${customerInfo.shippingAddress.lastName}`,
        shippingLine1: customerInfo.shippingAddress.line1,
        shippingLine2: customerInfo.shippingAddress.line2 || '',
        shippingCity: customerInfo.shippingAddress.city,
        shippingState: customerInfo.shippingAddress.state,
        shippingPostalCode: customerInfo.shippingAddress.postalCode,
        shippingCountry: customerInfo.shippingAddress.country,
        shippingMethod: selectedShipping?.name || '',
        shippingCost: shippingCost.toString(),
        subtotal: subtotal.toString(),
        itemCount: items.length.toString(),
      },
      automatic_payment_methods: {
        enabled: true,
      },
    });

    // Store order data temporarily (in production, use Redis or database)
    // For now, we'll rely on the metadata in the payment intent

    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
      orderId,
      total,
    });

  } catch (error) {
    console.error('Error creating payment intent:', error);
    return NextResponse.json(
      { error: 'Failed to create payment intent' },
      { status: 500 }
    );
  }
}
