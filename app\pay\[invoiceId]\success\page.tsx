'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { CheckCircle, Mail, Download, ArrowLeft } from 'lucide-react';
import { helvetica, editorialNew } from '@/app/fonts';
import Link from 'next/link';
import cx from 'classnames';

interface InvoicePayment {
  id: string;
  invoiceId: string;
  invoiceTitle: string;
  invoiceDescription: string;
  amount: number;
  customerName: string;
  customerEmail: string;
  shippingAddress: any;
  status: string;
  createdAt: any;
}

export default function InvoiceSuccessPage() {
  const params = useParams();
  const invoiceId = params.invoiceId as string;
  
  const [payment, setPayment] = useState<InvoicePayment | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (invoiceId) {
      loadPaymentDetails();
    }
  }, [invoiceId]);

  const loadPaymentDetails = async () => {
    try {
      const response = await fetch(`/api/invoices/${invoiceId}/payment-details`);
      const data = await response.json();

      if (data.success) {
        setPayment(data.payment);
      } else {
        setError(data.error || 'Payment details not found');
      }
    } catch (err) {
      console.error('Error loading payment details:', err);
      setError('Failed to load payment details');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={cx(helvetica.className, "min-h-screen bg-gray-50 flex items-center justify-center")}>
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
          <p className="mt-4 text-gray-600">Loading payment confirmation...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cx(helvetica.className, "min-h-screen bg-gray-50 flex items-center justify-center")}>
        <div className="text-center max-w-md mx-auto px-4">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle size={32} className="text-green-600" />
          </div>
          <h1 className={cx(editorialNew.className, "text-2xl mb-4")}>Payment Successful!</h1>
          <p className="text-gray-600 mb-6">
            Your payment has been processed successfully. {error}
          </p>
          <p className="text-sm text-gray-500 mb-6">
            You should receive a confirmation email shortly with your payment details.
            <br />
            <strong>Invoice ID:</strong> {invoiceId}
          </p>
          <Link 
            href="/"
            className="bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors inline-block"
          >
            Return to Store
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className={cx(helvetica.className, "min-h-screen bg-gray-50")}>
      <div className="max-w-2xl mx-auto px-4 py-8">
        <div className="bg-white p-8 rounded-lg shadow-sm text-center">
          {/* Success Icon */}
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle size={40} className="text-green-600" />
          </div>
          
          {/* Success Message */}
          <h1 className={cx(editorialNew.className, "text-3xl font-normal mb-4")}>
            Payment Successful!
          </h1>
          
          <p className="text-gray-600 mb-8">
            Thank you for your payment. Your order has been confirmed and we'll begin processing it shortly.
          </p>

          {/* Payment Details */}
          {payment && (
            <div className="bg-gray-50 p-6 rounded-lg mb-8 text-left">
              <h3 className="font-medium mb-4">Payment Details</h3>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Invoice:</span>
                  <span className="font-medium">{payment.invoiceTitle}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Amount:</span>
                  <span className="font-medium">${payment.amount.toFixed(2)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Customer:</span>
                  <span className="font-medium">{payment.customerName}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Email:</span>
                  <span className="font-medium">{payment.customerEmail}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <span className="font-medium text-green-600 capitalize">{payment.status}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Date:</span>
                  <span className="font-medium">
                    {payment.createdAt ? new Date(payment.createdAt.seconds * 1000).toLocaleDateString() : 'Just now'}
                  </span>
                </div>
              </div>

              {/* Shipping Address */}
              {payment.shippingAddress && (
                <div className="mt-6 pt-4 border-t">
                  <h4 className="font-medium mb-2">Shipping Address</h4>
                  <div className="text-sm text-gray-600">
                    <p>{payment.shippingAddress.firstName} {payment.shippingAddress.lastName}</p>
                    <p>{payment.shippingAddress.line1}</p>
                    {payment.shippingAddress.line2 && <p>{payment.shippingAddress.line2}</p>}
                    <p>
                      {payment.shippingAddress.city}, {payment.shippingAddress.state} {payment.shippingAddress.postalCode}
                    </p>
                    <p>{payment.shippingAddress.country}</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Next Steps */}
          <div className="bg-blue-50 p-6 rounded-lg mb-8 text-left">
            <h3 className="font-medium mb-3 flex items-center">
              <Mail size={18} className="mr-2" />
              What happens next?
            </h3>
            <ul className="text-sm text-gray-600 space-y-2">
              <li>• You'll receive a confirmation email with your payment receipt</li>
              <li>• We'll begin processing your order immediately</li>
              <li>• You'll receive shipping updates via email</li>
              <li>• If you have questions, contact us with your invoice ID: <strong>{invoiceId}</strong></li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={() => window.print()}
              className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
            >
              <Download size={16} className="mr-2" />
              Print Receipt
            </button>
            
            <Link
              href="/"
              className="block w-full bg-gray-200 text-black py-3 px-6 rounded-lg hover:bg-gray-300 transition-colors flex items-center justify-center"
            >
              <ArrowLeft size={16} className="mr-2" />
              Return to Store
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
