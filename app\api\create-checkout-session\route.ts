import { NextRequest, NextResponse } from 'next/server';
import Strip<PERSON> from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
});

export async function POST(req: NextRequest) {
  try {
    const { items, shippingAddress } = await req.json();

    const sessionConfig: any = {
      payment_method_types: ['card'],
      line_items: items,
      mode: 'payment',
      success_url: `${req.headers.get('origin')}/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${req.headers.get('origin')}/cart`,
      billing_address_collection: 'required',
    };

    // If we have a shipping address, store it in metadata and skip collection
    if (shippingAddress && shippingAddress.name && shippingAddress.line1) {
      // Store the shipping address in metadata for our records
      sessionConfig.metadata = {
        shipping_name: shippingAddress.name,
        shipping_line1: shippingAddress.line1,
        shipping_line2: shippingAddress.line2 || '',
        shipping_city: shippingAddress.city,
        shipping_state: shippingAddress.state || '',
        shipping_postal_code: shippingAddress.postalCode,
        shipping_country: shippingAddress.country,
        has_shipping_address: 'true'
      };

      // Don't include shipping_address_collection at all to skip it
      // (removing the property entirely instead of setting to null)
    } else {
      // Fallback to collecting shipping address in Stripe
      sessionConfig.shipping_address_collection = {
        allowed_countries: ['US', 'CA', 'GB', 'AU', 'DE', 'FR', 'JP', 'KR', 'SG', 'NZ', 'IT', 'ES', 'NL', 'SE', 'NO'],
      };
    }

    const session = await stripe.checkout.sessions.create(sessionConfig);

    return NextResponse.json({ url: session.url });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json(
      { error: 'Error creating checkout session' },
      { status: 500 }
    );
  }
}
