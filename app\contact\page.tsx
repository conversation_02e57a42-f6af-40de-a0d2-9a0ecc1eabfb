'use client';

import React, { useState } from 'react';
import cx from 'classnames';
import { helvetica, editorialNew } from '../fonts';

interface ContactFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
}

export default function Contact() {
  const [formData, setFormData] = useState<ContactFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });

  const [status, setStatus] = useState('');
  const [showPopup, setShowPopup] = useState(false);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    setStatus('Submitting...');
    try {
      await fetch('/api/send-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          to: "<EMAIL>",
          ...formData,
          subject: `Contact Form: ${formData.subject}`
        }),
      });
      
      setStatus('Your message has been sent successfully!');
      setShowPopup(true);
      
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      });
      
      setTimeout(() => {
        setShowPopup(false);
      }, 3000);
    } catch (err) {
      console.error(err);
      setStatus('Error sending message. Please try again.');
    }
  };

  return (
    <div className={cx(helvetica.variable, editorialNew.variable, 'min-h-screen flex flex-col bg-white text-black font-helvetica')}>
      {/* Hero Header Section */}
      <div className="w-full py-16 md:py-20">
        <div className="max-w-4xl mx-auto px-5 text-center">
          <h1 className="text-5xl md:text-6xl lg:text-7xl font-editorial font-normal leading-[0.9] mb-4 text-black text-left">
            Get in Touch
          </h1>
          <p className="text-lg sm:text-xl lg:text-2xl font-light max-w-2xl text-black text-left">
            Have questions? We'd love to hear from you.
          </p>
        </div>
      </div>

      <div className="flex-1 w-full max-w-4xl mx-auto px-5 py-6">
        <div className="bg-white border border-gray-200 shadow-lg rounded-2xl p-8 md:p-10">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-6">
              <h3 className="text-2xl font-editorial font-normal text-black border-b border-gray-200 pb-3 text-left">
                Contact Information
              </h3>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="firstName" className="block text-sm font-medium mb-2 text-black font-helvetica">First Name*</label>
                  <input
                    type="text"
                    id="firstName"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleChange}
                    required
                    className="w-full p-4 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 placeholder-gray-500 font-helvetica"
                    placeholder="Your first name"
                  />
                </div>
                
                <div>
                  <label htmlFor="lastName" className="block text-sm font-medium mb-2 text-black font-helvetica">Last Name*</label>
                  <input
                    type="text"
                    id="lastName"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleChange}
                    required
                    className="w-full p-4 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 placeholder-gray-500 font-helvetica"
                    placeholder="Your last name"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium mb-2 text-black font-helvetica">Email Address*</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full p-4 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 placeholder-gray-500 font-helvetica"
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium mb-2 text-black font-helvetica">Phone Number</label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="w-full p-4 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 placeholder-gray-500 font-helvetica"
                    placeholder="(*************"
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="subject" className="block text-sm font-medium mb-2 text-black font-helvetica">Subject*</label>
                <select
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  required
                  className="w-full p-4 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 font-helvetica"
                >
                  <option value="">Select a subject</option>
                  <option value="General Inquiry">General Inquiry</option>
                  <option value="Product Question">Product Question</option>
                  <option value="Custom Order">Custom Order Request</option>
                  <option value="Wholesale Inquiry">Wholesale Inquiry</option>
                  <option value="Technical Support">Technical Support</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              
              <div>
                <label htmlFor="message" className="block text-sm font-medium mb-2 text-black font-helvetica">Message*</label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  required
                  rows={6}
                  className="w-full p-4 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 placeholder-gray-500 resize-none font-helvetica"
                  placeholder="Tell us about your inquiry..."
                ></textarea>
              </div>
              
              <div className="flex justify-center pt-6">
                <button
                  type="submit"
                  className="bg-black text-white font-bold py-4 px-12 text-lg rounded-xl hover:bg-gray-800 transition-colors font-helvetica"
                >
                  Send Message
                </button>
              </div>
            </div>
          </form>
        </div>

        {status && (
          <div className="mt-6 text-center">
            <div className={cx(
              'inline-block px-6 py-3 rounded-xl border font-medium font-helvetica',
              {
                'bg-green-50 border-green-300 text-green-800': status.includes('success'),
                'bg-red-50 border-red-300 text-red-800': status.includes('Error'),
                'bg-blue-50 border-blue-300 text-blue-800': !status.includes('success') && !status.includes('Error')
              }
            )}>
              {status}
            </div>
          </div>
        )}

        {/* Popup */}
        {showPopup && (
          <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50 p-4">
            <div className="bg-white border border-gray-200 shadow-2xl rounded-2xl p-8 md:p-12 max-w-md w-full text-center">
              <h3 className="text-2xl font-editorial font-normal mb-4 text-black">Message Sent!</h3>
              <p className="mb-8 font-light text-black text-lg font-helvetica">Thanks for reaching out! We'll get back to you soon.</p>
              <button
                onClick={() => setShowPopup(false)}
                className="bg-black text-white font-bold py-3 px-10 rounded-xl hover:bg-gray-800 transition-colors font-helvetica"
              >
                Close
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
