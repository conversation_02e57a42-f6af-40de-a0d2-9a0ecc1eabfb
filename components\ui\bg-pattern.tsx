import React from 'react';
import { cn } from '@/lib/utils';

type BGVariantType = 'dots' | 'diagonal-stripes' | 'grid' | 'horizontal-lines' | 'vertical-lines' | 'checkerboard';
type BGMaskType =
    | 'fade-center'
    | 'fade-edges'
    | 'fade-top'
    | 'fade-bottom'
    | 'fade-left'
    | 'fade-right'
    | 'fade-x'
    | 'fade-y'
    | 'none';

type BGPatternProps = React.ComponentProps<'div'> & {
    variant?: BGVariantType;
    mask?: BGMaskType;
    size?: number;
    fill?: string;
};

// Fixed mask classes with proper colors instead of CSS variables
const maskClasses: Record<BGMaskType, string> = {
    'fade-edges': '[mask-image:radial-gradient(ellipse_at_center,white,transparent)]',
    'fade-center': '[mask-image:radial-gradient(ellipse_at_center,transparent,white)]',
    'fade-top': '[mask-image:linear-gradient(to_bottom,transparent,white)]',
    'fade-bottom': '[mask-image:linear-gradient(to_bottom,white,transparent)]',
    'fade-left': '[mask-image:linear-gradient(to_right,transparent,white)]',
    'fade-right': '[mask-image:linear-gradient(to_right,white,transparent)]',
    'fade-x': '[mask-image:linear-gradient(to_right,transparent,white,transparent)]',
    'fade-y': '[mask-image:linear-gradient(to_bottom,transparent,white,transparent)]',
    none: '',
};

function getBgImage(variant: BGVariantType, fill: string, size: number) {
    switch (variant) {
        case 'dots':
            // Make dots more visible by increasing the dot size to 2px
            return `radial-gradient(${fill} 2px, transparent 2px)`;
        case 'grid':
            return `linear-gradient(to right, ${fill} 1px, transparent 1px), linear-gradient(to bottom, ${fill} 1px, transparent 1px)`;
        case 'diagonal-stripes':
            return `repeating-linear-gradient(45deg, ${fill}, ${fill} 1px, transparent 1px, transparent ${size}px)`;
        case 'horizontal-lines':
            return `linear-gradient(to bottom, ${fill} 1px, transparent 1px)`;
        case 'vertical-lines':
            return `linear-gradient(to right, ${fill} 1px, transparent 1px)`;
        case 'checkerboard':
            return `linear-gradient(45deg, ${fill} 25%, transparent 25%), linear-gradient(-45deg, ${fill} 25%, transparent 25%), linear-gradient(45deg, transparent 75%, ${fill} 75%), linear-gradient(-45deg, transparent 75%, ${fill} 75%)`;
        default:
            return undefined;
    }
}

const BGPattern = ({
    variant = 'grid',
    mask = 'none',
    size = 24,
    fill = '#252525',
    className,
    style,
    ...props
}: BGPatternProps) => {
    const bgSize = `${size}px ${size}px`;
    const backgroundImage = getBgImage(variant, fill, size);

    return (
        <div
            className={cn(
                'absolute inset-0 size-full pointer-events-none',
                // Changed z-index from z-[-10] to z-[1] to be just below content
                'z-[1]',
                maskClasses[mask],
                className
            )}
            style={{
                backgroundImage,
                backgroundSize: bgSize,
                ...style,
            }}
            {...props}
        />
    );
};

BGPattern.displayName = 'BGPattern';
export { BGPattern };