'use client';

import { useState, useEffect, useRef } from 'react';
import { AdminUser } from '@/lib/auth/admin-firestore';
import { Artist, getArtistByFirebaseUid, updateArtist, uploadArtistProfileImage, deleteArtistProfileImage } from '@/lib/firebase/artists';
import { helvetica, editorialNew } from '@/app/fonts';
import cx from 'classnames';

interface ArtistProfileSettingsProps {
  user: AdminUser;
  onClose: () => void;
}

const ART_MEDIUMS = [
  'Painting', 'Drawing', 'Sculpture', 'Photography', 'Digital Art', 
  'Printmaking', 'Ceramics', 'Jewelry', 'Textiles', 'Mixed Media', 'Other'
];

const ART_STYLES = [
  'Abstract', 'Realistic', 'Impressionist', 'Modern', 'Contemporary', 
  'Traditional', 'Folk Art', 'Street Art', 'Minimalist', 'Experimental'
];

export default function ArtistProfileSettings({ user, onClose }: ArtistProfileSettingsProps) {
  const [artist, setArtist] = useState<Artist | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  
  // Upload state
  const [uploadingProfileImage, setUploadingProfileImage] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Form state
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    businessName: '',
    bio: '',
    website: '',
    profileImageUrl: '',
    bannerImageUrl: '',
    artStyle: '',
    artMediums: [] as string[],
    socialMedia: {
      instagram: '',
      facebook: '',
      twitter: '',
      tiktok: '',
      other: ''
    }
  });

  useEffect(() => {
    loadArtistProfile();
  }, []);

  const loadArtistProfile = async () => {
    try {
      const artistData = await getArtistByFirebaseUid(user.uid);
      if (artistData) {
        setArtist(artistData);
        setFormData({
          firstName: artistData.firstName || '',
          lastName: artistData.lastName || '',
          businessName: artistData.businessName || '',
          bio: artistData.bio || '',
          website: artistData.website || '',
          profileImageUrl: artistData.profileImageUrl || '',
          bannerImageUrl: artistData.bannerImageUrl || '',
          artStyle: artistData.artStyle || '',
          artMediums: artistData.artMediums || [],
          socialMedia: {
            instagram: artistData.socialMedia?.instagram || '',
            facebook: artistData.socialMedia?.facebook || '',
            twitter: artistData.socialMedia?.twitter || '',
            tiktok: artistData.socialMedia?.tiktok || '',
            other: artistData.socialMedia?.other || ''
          }
        });
      }
    } catch (error) {
      console.error('Error loading artist profile:', error);
      setError('Failed to load profile data');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    if (field.startsWith('socialMedia.')) {
      const socialField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        socialMedia: {
          ...prev.socialMedia,
          [socialField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleMediumToggle = (medium: string) => {
    setFormData(prev => ({
      ...prev,
      artMediums: prev.artMediums.includes(medium)
        ? prev.artMediums.filter(m => m !== medium)
        : [...prev.artMediums, medium]
    }));
  };

  const handleProfileImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !artist) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please select a valid image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('Image must be less than 5MB');
      return;
    }

    setUploadingProfileImage(true);
    setError('');

    try {
      const imageUrl = await uploadArtistProfileImage(artist.id, file);
      setFormData(prev => ({
        ...prev,
        profileImageUrl: imageUrl
      }));
    } catch (error) {
      console.error('Error uploading image:', error);
      setError('Failed to upload image');
    } finally {
      setUploadingProfileImage(false);
    }
  };

  const handleDeleteProfileImage = async () => {
    if (!artist || !formData.profileImageUrl) return;

    try {
      await deleteArtistProfileImage(artist.id, formData.profileImageUrl);
      setFormData(prev => ({
        ...prev,
        profileImageUrl: ''
      }));
    } catch (error) {
      console.error('Error deleting image:', error);
      setError('Failed to delete image');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!artist) return;

    setSaving(true);
    setError('');

    try {
      // Format website URL if provided
      let websiteUrl = formData.website.trim();
      if (websiteUrl && !websiteUrl.startsWith('http://') && !websiteUrl.startsWith('https://')) {
        websiteUrl = `https://${websiteUrl}`;
      }

      await updateArtist(artist.id, {
        firstName: formData.firstName,
        lastName: formData.lastName,
        businessName: formData.businessName,
        bio: formData.bio,
        website: websiteUrl,
        profileImageUrl: formData.profileImageUrl,
        bannerImageUrl: formData.bannerImageUrl,
        artStyle: formData.artStyle,
        artMediums: formData.artMediums,
        socialMedia: formData.socialMedia
      });
      
      onClose();
    } catch (error) {
      console.error('Error updating profile:', error);
      setError('Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
            <p>Loading profile...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <h2 className={cx(editorialNew.className, "text-2xl font-bold")}>
              Edit Profile
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                First Name *
              </label>
              <input
                type="text"
                value={formData.firstName}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
                required
              />
            </div>
            <div>
              <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                Last Name *
              </label>
              <input
                type="text"
                value={formData.lastName}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
                required
              />
            </div>
          </div>

          <div>
            <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
              Business/Artist Name (optional)
            </label>
            <input
              type="text"
              value={formData.businessName}
              onChange={(e) => handleInputChange('businessName', e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
              placeholder="Your brand or business name"
            />
          </div>

          <div>
            <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
              Bio (optional)
            </label>
            <textarea
              value={formData.bio}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              rows={4}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
              placeholder="Tell customers about yourself and your art..."
            />
          </div>

          {/* Images */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                Profile Image (optional)
              </label>
              <div className="space-y-3">
                {formData.profileImageUrl && (
                  <div className="relative inline-block">
                    <img
                      src={formData.profileImageUrl}
                      alt="Profile preview"
                      className="w-24 h-24 object-cover rounded-full border-2 border-gray-200"
                    />
                    <button
                      type="button"
                      onClick={handleDeleteProfileImage}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                    >
                      ×
                    </button>
                  </div>
                )}
                <div className="flex items-center space-x-2">
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleProfileImageUpload}
                    className="hidden"
                  />
                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={uploadingProfileImage}
                    className="bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-md px-4 py-2 text-sm font-medium text-gray-700 disabled:opacity-50"
                  >
                    {uploadingProfileImage ? 'Uploading...' : formData.profileImageUrl ? 'Change Image' : 'Upload Image'}
                  </button>
                  <span className="text-xs text-gray-500">Max 5MB</span>
                </div>
              </div>
            </div>
            <div>
              <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                Banner Image URL (optional)
              </label>
              <input
                type="url"
                value={formData.bannerImageUrl}
                onChange={(e) => handleInputChange('bannerImageUrl', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
                placeholder="https://..."
              />
            </div>
          </div>

          {/* Art Info */}
          <div>
            <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
              Art Style
            </label>
            <select
              value={formData.artStyle}
              onChange={(e) => handleInputChange('artStyle', e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
            >
              <option value="">Select a style</option>
              {ART_STYLES.map(style => (
                <option key={style} value={style}>{style}</option>
              ))}
            </select>
          </div>

          <div>
            <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-2")}>
              Art Mediums
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {ART_MEDIUMS.map(medium => (
                <label key={medium} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.artMediums.includes(medium)}
                    onChange={() => handleMediumToggle(medium)}
                    className="rounded border-gray-300"
                  />
                  <span className={cx(helvetica.className, "text-sm")}>{medium}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Contact Info */}
          <div>
            <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
              Website (optional)
            </label>
            <input
              type="text"
              value={formData.website}
              onChange={(e) => handleInputChange('website', e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
              placeholder="detailongo.com or https://detailongo.com"
            />
          </div>

          {/* Social Media */}
          <div>
            <h3 className={cx(editorialNew.className, "text-lg font-semibold mb-3")}>Social Media (optional)</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                  Instagram
                </label>
                <input
                  type="text"
                  value={formData.socialMedia.instagram}
                  onChange={(e) => handleInputChange('socialMedia.instagram', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="@username"
                />
              </div>
              <div>
                <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                  Facebook
                </label>
                <input
                  type="text"
                  value={formData.socialMedia.facebook}
                  onChange={(e) => handleInputChange('socialMedia.facebook', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="facebook.com/username"
                />
              </div>
              <div>
                <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                  Twitter
                </label>
                <input
                  type="text"
                  value={formData.socialMedia.twitter}
                  onChange={(e) => handleInputChange('socialMedia.twitter', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="@username"
                />
              </div>
              <div>
                <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                  TikTok
                </label>
                <input
                  type="text"
                  value={formData.socialMedia.tiktok}
                  onChange={(e) => handleInputChange('socialMedia.tiktok', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="@username"
                />
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              disabled={saving}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-black text-white rounded-md hover:bg-gray-800 disabled:opacity-50"
              disabled={saving}
            >
              {saving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
