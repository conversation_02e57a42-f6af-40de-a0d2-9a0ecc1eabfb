'use client';

import { useState, useEffect } from 'react';
import { onAuthStateChanged, User } from 'firebase/auth';
import { auth } from '@/lib/firebase/config';
import { isUserAdmin, AdminUser } from '@/lib/auth/admin-firestore';
import Modal from '@/components/shared/modal';
import { helvetica, editorialNew } from '@/app/fonts';
import { FileText, DollarSign, User as UserIcon, Mail, Calendar, Send } from 'lucide-react';
import cx from 'classnames';

interface InvoiceData {
  customerName?: string;
  customerEmail?: string;
  title: string;
  description: string;
  amount: number;
  dueDate: string;
  notes: string;
  artistId?: string;
  includeShipping: boolean;
  shippingCost?: number;
  shippingNotes?: string;
}

export default function CreateInvoicePage() {
  const [user, setUser] = useState<User | null>(null);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [authLoading, setAuthLoading] = useState(true);

  const handleBack = () => {
    window.history.back();
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        const admin = await isUserAdmin(user);
        if (admin && ['super_admin', 'admin', 'artist'].includes(admin.role)) {
          setUser(user);
          setAdminUser(admin);
          
          // For artists, automatically set artistId in invoice data
          if (admin.role === 'artist' && admin.artistId) {
            setInvoiceData(prev => ({ ...prev, artistId: admin.artistId }));
          }
        }
      }
      setAuthLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const [invoiceData, setInvoiceData] = useState<InvoiceData>({
    customerName: '',
    customerEmail: '',
    title: '',
    description: '',
    amount: 0,
    dueDate: '',
    notes: '',
    includeShipping: false,
    shippingCost: 0,
    shippingNotes: ''
  });
  const [popup, setPopup] = useState<{ message: string; type?: 'success' | 'error' } | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [createdInvoice, setCreatedInvoice] = useState<any>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const updateInvoiceData = (field: keyof InvoiceData, value: string | number | boolean) => {
    setInvoiceData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Customer name and email are now optional
    if (invoiceData.customerEmail && invoiceData.customerEmail.trim() && !/\S+@\S+\.\S+/.test(invoiceData.customerEmail)) {
      newErrors.customerEmail = 'Please enter a valid email';
    }
    if (!invoiceData.title.trim()) newErrors.title = 'Invoice title is required';
    if (!invoiceData.description.trim()) newErrors.description = 'Description is required';
    if (!invoiceData.amount || invoiceData.amount <= 0) newErrors.amount = 'Amount must be greater than 0';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreateInvoice = async () => {
    if (!validateForm()) return;
    if (!user) {
      setPopup({ message: 'Please sign in to create invoices', type: 'error' });
      return;
    }

    setIsCreating(true);
    try {
      // Get the current user's ID token for authentication
      const idToken = await user.getIdToken();
      
      const invoicePayload = { ...invoiceData };
      const response = await fetch('/api/admin/create-invoice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`,
        },
        body: JSON.stringify(invoicePayload),
      });

      const result = await response.json();

      if (result.success) {
        setCreatedInvoice(result.invoice);
      } else {
        setPopup({ message: 'Failed to create invoice: ' + result.error, type: 'error' });
      }
    } catch (error) {
      console.error('Error creating invoice:', error);
      setPopup({ message: 'Failed to create invoice. Please try again.', type: 'error' });
    } finally {
      setIsCreating(false);
    }
  };

  const copyPaymentLink = () => {
    if (createdInvoice) {
      const link = `${window.location.origin}/pay/${createdInvoice.id}`;
      navigator.clipboard.writeText(link);
      setPopup({ message: 'Payment link copied to clipboard!', type: 'success' });
    }
  };

  const sendInvoiceEmail = async () => {
    if (!createdInvoice) return;

    try {
      const response = await fetch('/api/admin/send-invoice-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ invoiceId: createdInvoice.id }),
      });

      const result = await response.json();
      if (result.success) {
        setPopup({ message: 'Invoice email sent successfully!', type: 'success' });
      } else {
        setPopup({ message: 'Failed to send email: ' + result.error, type: 'error' });
      }
    } catch (error) {
  console.error('Error sending email:', error);
  setPopup({ message: 'Failed to send email. Please try again.', type: 'error' });
    }
  };

  // Loading state
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Authentication check
  if (!user || !adminUser || !['super_admin', 'admin', 'artist'].includes(adminUser.role)) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className={cx(editorialNew.className, "text-2xl font-bold text-gray-900 mb-4")}>
            Access Denied
          </h1>
          <p className="text-gray-600 mb-6">
            You need to be signed in as an admin or artist to create invoices.
          </p>
          <button
            onClick={() => window.location.href = '/admin'}
            className="bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors"
          >
            Go to Admin Login
          </button>
        </div>
      </div>
    );
  }

  if (createdInvoice) {
    return (
      <div className={cx(helvetica.className, "min-h-screen bg-gray-50")}> 
        {popup && (
          <Modal showModal={true} setShowModal={() => setPopup(null)}>
            <div className={`p-6 text-center ${popup.type === 'error' ? 'text-red-600' : 'text-green-600'}`}>
              <p className="mb-4 text-lg">{popup.message}</p>
              <button className="bg-black text-white px-4 py-2 rounded" onClick={() => setPopup(null)}>Close</button>
            </div>
          </Modal>
        )}
        <div className="max-w-2xl mx-auto px-4 py-8">
          <div className="bg-white p-8 rounded-lg shadow-sm text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <FileText size={32} className="text-green-600" />
            </div>
            
            <h1 className={cx(editorialNew.className, "text-2xl font-normal mb-4")}>
              Invoice Created Successfully!
            </h1>
            
            <div className="bg-gray-50 p-4 rounded-lg mb-6">
              <p className="text-sm text-gray-600 mb-2">Payment Link:</p>
              <p className="font-mono text-sm bg-white p-2 rounded border break-all">
                {window.location.origin}/pay/{createdInvoice.id}
              </p>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg mb-6 text-left">
              <h3 className="font-medium text-gray-900 mb-3">Invoice Details:</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Amount:</span>
                  <span className="font-medium">${createdInvoice.amount.toFixed(2)}</span>
                </div>
                {createdInvoice.includeShipping ? (
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Shipping:</span>
                      <span className="font-medium">Included (${createdInvoice.shippingCost?.toFixed(2) || '0.00'})</span>
                    </div>
                    <div className="flex justify-between border-t pt-2">
                      <span className="font-medium text-gray-900">Total:</span>
                      <span className="font-medium">${(createdInvoice.amount + (createdInvoice.shippingCost || 0)).toFixed(2)}</span>
                    </div>
                    {createdInvoice.shippingNotes && (
                      <div className="pt-2 border-t">
                        <span className="text-gray-600">Shipping Notes:</span>
                        <p className="text-gray-800 mt-1">{createdInvoice.shippingNotes}</p>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-gray-600 text-xs">
                    Shipping will be calculated at checkout
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-3">
              <button
                onClick={copyPaymentLink}
                className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Copy Payment Link
              </button>
              
              <button
                onClick={sendInvoiceEmail}
                className="w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center"
              >
                <Send size={16} className="mr-2" />
                Send Email to Customer
              </button>
              
              <button
                onClick={() => {
                  setCreatedInvoice(null);
                  setInvoiceData({
                    customerName: '',
                    customerEmail: '',
                    title: '',
                    description: '',
                    amount: 0,
                    dueDate: '',
                    notes: '',
                    includeShipping: false,
                    shippingCost: 0,
                    shippingNotes: ''
                  });
                }}
                className="w-full bg-gray-200 text-black py-3 px-6 rounded-lg hover:bg-gray-300 transition-colors"
              >
                Create Another Invoice
              </button>
              
              <button
                onClick={handleBack}
                className="w-full bg-gray-100 text-black py-3 px-6 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center"
              >
                <svg width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" className="mr-2">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7"/>
                </svg>
                Return to Admin
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cx(helvetica.className, "min-h-screen bg-gray-50")}> 
      <div className="max-w-2xl mx-auto px-4 pt-8 pb-2 flex items-center">
        <button
          onClick={handleBack}
          className="bg-gray-100 hover:bg-gray-200 text-black px-4 py-2 rounded flex items-center gap-2 shadow-sm border border-gray-300"
        >
          <svg width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7"/></svg>
          Back
        </button>
      </div>
      {popup && (
        <Modal showModal={true} setShowModal={() => setPopup(null)}>
          <div className={`p-6 text-center ${popup.type === 'error' ? 'text-red-600' : 'text-green-600'}`}>
            <p className="mb-4 text-lg">{popup.message}</p>
            <button className="bg-black text-white px-4 py-2 rounded" onClick={() => setPopup(null)}>Close</button>
          </div>
        </Modal>
      )}
      <div className="max-w-2xl mx-auto px-4 py-8">
        <div className="bg-white p-8 rounded-lg shadow-sm">
          <h1 className={cx(editorialNew.className, "text-3xl font-normal mb-2")}>
            Create Custom Invoice
          </h1>
          <p className="text-gray-600 mb-8">
            Create a custom payment link for special orders, quotes, or custom work.
          </p>

          <div className="space-y-6">
            {/* Customer Information */}
            <div>
              <h3 className="text-lg font-medium mb-4 flex items-center">
                <UserIcon size={18} className="mr-2" />
                Customer Information
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Customer Name (optional)</label>
                  <input
                    type="text"
                    value={invoiceData.customerName}
                    onChange={(e) => updateInvoiceData('customerName', e.target.value)}
                    className={`w-full p-3 border rounded-lg ${
                      errors.customerName ? 'border-red-500' : 'border-gray-300'
                    } focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent`}
                    placeholder="John Doe"
                  />
                  {errors.customerName && (
                    <p className="text-red-500 text-sm mt-1">{errors.customerName}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Customer Email (optional)</label>
                  <input
                    type="email"
                    value={invoiceData.customerEmail}
                    onChange={(e) => updateInvoiceData('customerEmail', e.target.value)}
                    className={`w-full p-3 border rounded-lg ${
                      errors.customerEmail ? 'border-red-500' : 'border-gray-300'
                    } focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent`}
                    placeholder="<EMAIL>"
                  />
                  {errors.customerEmail && (
                    <p className="text-red-500 text-sm mt-1">{errors.customerEmail}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Invoice Details */}
            <div>
              <h3 className="text-lg font-medium mb-4 flex items-center">
                <FileText size={18} className="mr-2" />
                Invoice Details
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Invoice Title *</label>
                  <input
                    type="text"
                    value={invoiceData.title}
                    onChange={(e) => updateInvoiceData('title', e.target.value)}
                    className={`w-full p-3 border rounded-lg ${
                      errors.title ? 'border-red-500' : 'border-gray-300'
                    } focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent`}
                    placeholder="Custom Pet Portrait Commission"
                  />
                  {errors.title && (
                    <p className="text-red-500 text-sm mt-1">{errors.title}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Description *</label>
                  <textarea
                    value={invoiceData.description}
                    onChange={(e) => updateInvoiceData('description', e.target.value)}
                    rows={4}
                    className={`w-full p-3 border rounded-lg ${
                      errors.description ? 'border-red-500' : 'border-gray-300'
                    } focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent`}
                    placeholder="Detailed description of the custom work, including specifications, timeline, etc."
                  />
                  {errors.description && (
                    <p className="text-red-500 text-sm mt-1">{errors.description}</p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1 flex items-center">
                      <DollarSign size={16} className="mr-1" />
                      Amount *
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={invoiceData.amount || ''}
                      onChange={(e) => updateInvoiceData('amount', parseFloat(e.target.value) || 0)}
                      className={`w-full p-3 border rounded-lg ${
                        errors.amount ? 'border-red-500' : 'border-gray-300'
                      } focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent`}
                      placeholder="150.00"
                    />
                    {errors.amount && (
                      <p className="text-red-500 text-sm mt-1">{errors.amount}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1 flex items-center">
                      <Calendar size={16} className="mr-1" />
                      Due Date
                    </label>
                    <input
                      type="date"
                      value={invoiceData.dueDate}
                      onChange={(e) => updateInvoiceData('dueDate', e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Additional Notes</label>
                  <textarea
                    value={invoiceData.notes}
                    onChange={(e) => updateInvoiceData('notes', e.target.value)}
                    rows={3}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                    placeholder="Any additional notes or terms for the customer..."
                  />
                </div>
              </div>
            </div>

            {/* Shipping Options */}
            <div>
              <h3 className="text-lg font-medium mb-4 flex items-center">
                <Send size={18} className="mr-2" />
                Shipping Options
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="includeShipping"
                    checked={invoiceData.includeShipping}
                    onChange={(e) => updateInvoiceData('includeShipping', e.target.checked)}
                    className="w-4 h-4 text-black bg-gray-100 border-gray-300 rounded focus:ring-black focus:ring-2"
                  />
                  <label htmlFor="includeShipping" className="ml-2 text-sm font-medium">
                    Include shipping costs in this invoice
                  </label>
                </div>

                {invoiceData.includeShipping && (
                  <>
                    <div>
                      <label className="block text-sm font-medium mb-1 flex items-center">
                        <DollarSign size={16} className="mr-1" />
                        Shipping Cost
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        value={invoiceData.shippingCost || ''}
                        onChange={(e) => updateInvoiceData('shippingCost', parseFloat(e.target.value) || 0)}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                        placeholder="25.00"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Shipping Notes</label>
                      <textarea
                        value={invoiceData.shippingNotes || ''}
                        onChange={(e) => updateInvoiceData('shippingNotes', e.target.value)}
                        rows={2}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                        placeholder="Shipping method, estimated delivery time, handling instructions, etc."
                      />
                    </div>

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <p className="text-sm text-blue-700 font-medium">
                        Total Amount: ${((invoiceData.amount || 0) + (invoiceData.shippingCost || 0)).toFixed(2)}
                      </p>
                      <p className="text-xs text-blue-600 mt-1">
                        Base amount: ${(invoiceData.amount || 0).toFixed(2)} + Shipping: ${(invoiceData.shippingCost || 0).toFixed(2)}
                      </p>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Create Button */}
            <div className="pt-4">
              <button
                onClick={handleCreateInvoice}
                disabled={isCreating}
                className="w-full bg-black text-white font-medium py-3 px-6 rounded-lg hover:bg-gray-800 disabled:opacity-50 transition-colors flex items-center justify-center"
              >
                {isCreating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating Invoice...
                  </>
                ) : (
                  <>
                    <FileText size={16} className="mr-2" />
                    Create Invoice & Generate Payment Link
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
