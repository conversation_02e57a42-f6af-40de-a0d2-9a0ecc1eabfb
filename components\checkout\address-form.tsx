'use client';

import { MapPin } from 'lucide-react';

interface Address {
  line1: string;
  line2: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

interface AddressFormProps {
  address: Address;
  onAddressChange: (updates: Partial<Address>) => void;
  errors: Record<string, string>;
  title: string;
}

const countries = [
  { code: 'US', name: 'United States' },
  { code: 'CA', name: 'Canada' },
  { code: 'GB', name: 'United Kingdom' },
  { code: 'AU', name: 'Australia' },
  { code: 'DE', name: 'Germany' },
  { code: 'FR', name: 'France' },
  { code: 'JP', name: 'Japan' },
  { code: 'OTHER', name: 'Other' }
];

export default function AddressForm({
  address,
  onAddressChange,
  errors,
  title
}: AddressFormProps) {

  const updateAddress = (field: keyof Address, value: string) => {
    onAddressChange({ [field]: value });
  };

  return (
    <div className="bg-white p-6 rounded-lg border">
      <h3 className="text-lg font-medium mb-4 flex items-center">
        <MapPin size={18} className="mr-2" />
        {title}
      </h3>
      
      <div className="space-y-4">
        {/* Address Line 1 */}
        <div>
          <label className="block text-sm font-medium mb-1">Address Line 1 *</label>
          <input
            type="text"
            value={address.line1}
            onChange={(e) => updateAddress('line1', e.target.value)}
            className={`w-full p-3 border rounded-lg text-base ${
              errors.line1 ? 'border-red-500' : 'border-gray-300'
            } focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent`}
            placeholder="123 Main Street"
            style={{ fontSize: '16px' }}
          />
          {errors.line1 && (
            <p className="text-red-500 text-sm mt-1">{errors.line1}</p>
          )}
        </div>

        {/* Address Line 2 */}
        <div>
          <label className="block text-sm font-medium mb-1">Address Line 2</label>
          <input
            type="text"
            value={address.line2}
            onChange={(e) => updateAddress('line2', e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-lg text-base focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
            placeholder="Apartment, suite, etc. (optional)"
            style={{ fontSize: '16px' }}
          />
        </div>

        {/* City, State, Postal Code */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">City *</label>
            <input
              type="text"
              value={address.city}
              onChange={(e) => updateAddress('city', e.target.value)}
              className={`w-full p-3 border rounded-lg text-base ${
                errors.city ? 'border-red-500' : 'border-gray-300'
              } focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent`}
              style={{ fontSize: '16px' }}
            />
            {errors.city && (
              <p className="text-red-500 text-sm mt-1">{errors.city}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              {address.country === 'US' ? 'State *' : 'State/Province'}
            </label>
            <input
              type="text"
              value={address.state}
              onChange={(e) => updateAddress('state', e.target.value)}
              className={`w-full p-3 border rounded-lg text-base ${
                errors.state ? 'border-red-500' : 'border-gray-300'
              } focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent`}
              style={{ fontSize: '16px' }}
            />
            {errors.state && (
              <p className="text-red-500 text-sm mt-1">{errors.state}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              {address.country === 'US' ? 'ZIP Code *' : 'Postal Code *'}
            </label>
            <input
              type="text"
              value={address.postalCode}
              onChange={(e) => updateAddress('postalCode', e.target.value)}
              className={`w-full p-3 border rounded-lg text-base ${
                errors.postalCode ? 'border-red-500' : 'border-gray-300'
              } focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent`}
              style={{ fontSize: '16px' }}
            />
            {errors.postalCode && (
              <p className="text-red-500 text-sm mt-1">{errors.postalCode}</p>
            )}
          </div>
        </div>

        {/* Country */}
        <div>
          <label className="block text-sm font-medium mb-1">Country *</label>
          <select
            value={address.country}
            onChange={(e) => updateAddress('country', e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-lg text-base focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
            style={{ fontSize: '16px' }}
          >
            {countries.map(country => (
              <option key={country.code} value={country.code}>
                {country.name}
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  );
}
