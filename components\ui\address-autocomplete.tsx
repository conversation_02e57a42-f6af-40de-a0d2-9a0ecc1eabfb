'use client';

import { useState, useEffect, useRef } from 'react';
import { MapPin } from 'lucide-react';

interface Address {
  firstName: string;
  lastName: string;
  line1: string;
  line2: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

interface AddressAutocompleteProps {
  address: Address;
  onAddressChange: (updates: Partial<Address>) => void;
  errors: Record<string, string>;
  fieldPrefix: string;
}

declare global {
  interface Window {
    google: any;
    initGooglePlaces: () => void;
  }
}

export default function AddressAutocomplete({
  address,
  onAddressChange,
  errors,
  fieldPrefix
}: AddressAutocompleteProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const autocompleteRef = useRef<HTMLInputElement>(null);
  const autocompleteInstance = useRef<any>(null);

  useEffect(() => {
    // Check if Google Places API is already loaded
    if (window.google && window.google.maps && window.google.maps.places) {
      setIsLoaded(true);
      return;
    }

    // Check if script is already being loaded
    const existingScript = document.querySelector('script[src*="maps.googleapis.com"]');
    if (existingScript) {
      // Script exists, wait for it to load
      const checkLoaded = () => {
        if (window.google && window.google.maps && window.google.maps.places) {
          setIsLoaded(true);
        } else {
          setTimeout(checkLoaded, 100);
        }
      };
      checkLoaded();
      return;
    }

    // Load Google Places API for the first time
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyD4gsHevyhIxApmvBF0PQYUM4ZytptVX-E&libraries=places&callback=initGooglePlaces`;
    script.async = true;
    script.defer = true;

    window.initGooglePlaces = () => {
      setIsLoaded(true);
    };

    document.head.appendChild(script);
  }, []);

  useEffect(() => {
    if (isLoaded && autocompleteRef.current && !autocompleteInstance.current) {
      autocompleteInstance.current = new window.google.maps.places.Autocomplete(
        autocompleteRef.current,
        {
          types: ['address'],
          componentRestrictions: { country: ['us', 'ca', 'gb', 'au', 'de', 'fr', 'jp'] }
        }
      );

      autocompleteInstance.current.addListener('place_changed', () => {
        const place = autocompleteInstance.current.getPlace();

        if (place.address_components) {
          const addressComponents = place.address_components;
          const updates: Partial<Address> = {};

          // Parse address components - ONLY update address fields, preserve names
          addressComponents.forEach((component: any) => {
            const types = component.types;

            if (types.includes('street_number')) {
              updates.line1 = component.long_name + ' ' + (updates.line1 || '');
            }
            if (types.includes('route')) {
              updates.line1 = (updates.line1 || '') + component.long_name;
            }
            if (types.includes('locality')) {
              updates.city = component.long_name;
            }
            if (types.includes('administrative_area_level_1')) {
              updates.state = component.short_name;
            }
            if (types.includes('postal_code')) {
              updates.postalCode = component.long_name;
            }
            if (types.includes('country')) {
              updates.country = component.short_name;
            }
          });

          // Clean up line1
          if (updates.line1) {
            updates.line1 = updates.line1.trim();
          }

          // Only update address fields, completely exclude name fields
          const addressOnlyUpdates: Partial<Address> = {};

          if (updates.line1) addressOnlyUpdates.line1 = updates.line1;
          if (updates.city) addressOnlyUpdates.city = updates.city;
          if (updates.state) addressOnlyUpdates.state = updates.state;
          if (updates.postalCode) addressOnlyUpdates.postalCode = updates.postalCode;
          if (updates.country) addressOnlyUpdates.country = updates.country;

          // Only call onAddressChange if we have address updates
          if (Object.keys(addressOnlyUpdates).length > 0) {
            console.log('Google Places sending address updates:', addressOnlyUpdates);
            onAddressChange(addressOnlyUpdates);
          }
        }
      });
    }
  }, [isLoaded, onAddressChange]);

  return (
    <div className="space-y-4">

      {/* Address Autocomplete */}
      <div>
        <label className="block text-sm font-medium mb-1 flex items-center">
          <MapPin size={16} className="mr-1" />
          Address Line 1 * {isLoaded && <span className="text-xs text-gray-500 ml-2">(Start typing for suggestions)</span>}
        </label>
        <input
          ref={autocompleteRef}
          type="text"
          value={address.line1}
          onChange={(e) => onAddressChange({ line1: e.target.value })}
          className={`w-full p-3 border rounded-lg text-base ${
            errors[`${fieldPrefix}Line1`] ? 'border-red-500' : 'border-gray-300'
          } focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent`}
          placeholder="123 Main Street"
          style={{ fontSize: '16px' }}
        />
        {errors[`${fieldPrefix}Line1`] && (
          <p className="text-red-500 text-sm mt-1">{errors[`${fieldPrefix}Line1`]}</p>
        )}
      </div>

      {/* Address Line 2 */}
      <div>
        <label className="block text-sm font-medium mb-1">Address Line 2</label>
        <input
          type="text"
          value={address.line2}
          onChange={(e) => onAddressChange({ line2: e.target.value })}
          className="w-full p-3 border border-gray-300 rounded-lg text-base focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
          placeholder="Apartment, suite, etc. (optional)"
          style={{ fontSize: '16px' }}
        />
      </div>

      {/* City, State, Postal Code */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">City *</label>
          <input
            type="text"
            value={address.city}
            onChange={(e) => onAddressChange({ city: e.target.value })}
            className={`w-full p-3 border rounded-lg text-base ${
              errors[`${fieldPrefix}City`] ? 'border-red-500' : 'border-gray-300'
            } focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent`}
            style={{ fontSize: '16px' }}
          />
          {errors[`${fieldPrefix}City`] && (
            <p className="text-red-500 text-sm mt-1">{errors[`${fieldPrefix}City`]}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">
            {address.country === 'US' ? 'State *' : 'State/Province'}
          </label>
          <input
            type="text"
            value={address.state}
            onChange={(e) => onAddressChange({ state: e.target.value })}
            className={`w-full p-3 border rounded-lg text-base ${
              errors[`${fieldPrefix}State`] ? 'border-red-500' : 'border-gray-300'
            } focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent`}
            style={{ fontSize: '16px' }}
          />
          {errors[`${fieldPrefix}State`] && (
            <p className="text-red-500 text-sm mt-1">{errors[`${fieldPrefix}State`]}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">
            {address.country === 'US' ? 'ZIP Code *' : 'Postal Code *'}
          </label>
          <input
            type="text"
            value={address.postalCode}
            onChange={(e) => onAddressChange({ postalCode: e.target.value })}
            className={`w-full p-3 border rounded-lg text-base ${
              errors[`${fieldPrefix}PostalCode`] ? 'border-red-500' : 'border-gray-300'
            } focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent`}
            style={{ fontSize: '16px' }}
          />
          {errors[`${fieldPrefix}PostalCode`] && (
            <p className="text-red-500 text-sm mt-1">{errors[`${fieldPrefix}PostalCode`]}</p>
          )}
        </div>
      </div>

      {/* Country */}
      <div>
        <label className="block text-sm font-medium mb-1">Country *</label>
        <select
          value={address.country}
          onChange={(e) => onAddressChange({ country: e.target.value })}
          className="w-full p-3 border border-gray-300 rounded-lg text-base focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
          style={{ fontSize: '16px' }}
        >
          <option value="US">United States</option>
          <option value="CA">Canada</option>
          <option value="GB">United Kingdom</option>
          <option value="AU">Australia</option>
          <option value="DE">Germany</option>
          <option value="FR">France</option>
          <option value="JP">Japan</option>
          <option value="OTHER">Other</option>
        </select>
      </div>
    </div>
  );
}
