"use client"

import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const gradientButtonVariants = cva(
    [
        "gradient-button",
        "inline-flex items-center justify-center",
        "rounded-[11px] min-w-[132px] px-9 py-4",
        "text-base leading-[19px] font-[500] text-white",
        "font-sans font-bold",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2",
        "disabled:pointer-events-none disabled:opacity-50",
        "border-0 outline-none",
        "relative z-10",
    ],
    {
        variants: {
            variant: {
                default: "",
                variant: "gradient-button-variant",
                deepBlue: "gradient-button-deep-blue",
                fast: "gradient-button-fast",
                smooth: "gradient-button-smooth",
            },
            size: {
                default: "px-9 py-4",
                sm: "px-6 py-3 text-sm",
                lg: "px-12 py-5 text-lg",
            }
        },
        defaultVariants: {
            variant: "default",
            size: "default",
        },
    }
)

export interface GradientButtonProps
    extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof gradientButtonVariants> {
    asChild?: boolean
}

const GradientButton = React.forwardRef<HTMLButtonElement, GradientButtonProps>(
    ({ className, variant, size, asChild = false, ...props }, ref) => {
        const Comp = asChild ? Slot : "button"
        return (
            <Comp
                className={cn(gradientButtonVariants({ variant, size, className }))}
                ref={ref}
                {...props}
            />
        )
    }
)
GradientButton.displayName = "GradientButton"

export { GradientButton, gradientButtonVariants }

// Demo component to show all variants
export function GradientButtonDemo() {
    return (
        <div className="flex flex-col gap-6 p-8 bg-gray-100 min-h-screen">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Animated Gradient Buttons</h2>

            <div className="flex flex-wrap gap-4">
                <GradientButton>
                    Default Animated
                </GradientButton>

                <GradientButton variant="variant">
                    Colorful Variant
                </GradientButton>

                <GradientButton variant="deepBlue">
                    Deep Blue Ocean
                </GradientButton>

                <GradientButton variant="fast">
                    Fast Animation
                </GradientButton>

                <GradientButton variant="smooth">
                    Smooth & Slow
                </GradientButton>
            </div>

            <div className="flex flex-wrap gap-4">
                <GradientButton size="sm">
                    Small Button
                </GradientButton>

                <GradientButton size="lg" variant="variant">
                    Large Button
                </GradientButton>
            </div>

            <div className="flex flex-wrap gap-4">
                <GradientButton asChild>
                    <a href="#" className="no-underline">
                        Link as Button
                    </a>
                </GradientButton>

                <GradientButton disabled>
                    Disabled Button
                </GradientButton>
            </div>
        </div>
    )
}