import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Contact Us | Inside Strategy',
  description: 'Connect with our business broker experts for a confidential consultation. Get answers about selling your business and maximizing value.',
  keywords: ['business broker contact', 'sell business consultation', 'business valuation contact', 'business sale experts'],
  alternates: {
    canonical: 'https://insidestrategy.co/contact/',
  },
  openGraph: {
    title: 'Contact Us | Inside Strategy',
    description: 'Connect with our business broker experts for a confidential consultation.',
    url: 'https://insidestrategy.co/contact/',
  },
};

export default function ContactLayout({
  children,
}: {
  children: React.ReactNode;
}): React.ReactElement {
  return <>{children}</>;
}
