'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { onAdminAuthStateChanged, signOutAdmin } from '@/lib/auth/admin';
import { AdminUser } from '@/lib/auth/admin-firestore';
import AdminLogin from '@/components/admin/AdminLogin';
import AdminDashboard from '@/components/admin/AdminDashboard';

export default function AdminPage() {
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const unsubscribe = onAdminAuthStateChanged((admin) => {
      if (admin && admin.role === 'artist') {
        // Redirect artists to their dashboard
        router.push('/dashboard');
        return;
      }
      
      setAdminUser(admin);
      setLoading(false);
    });

    return () => unsubscribe();
  }, [router]);

  // Unified sign-in handler that works for both email/password and Google
  const handleSignIn = async (email: string, password: string) => {
    // This function is called from AdminLogin component
    // The actual authentication is handled within the component
    // We just need to wait for the auth state to change
    return Promise.resolve();
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  if (!adminUser) {
    return <AdminLogin onSignIn={handleSignIn} />;
  }

  // Only admins and super_admins should access this page
  // Artists are redirected to /dashboard in the useEffect
  return <AdminDashboard user={adminUser} signOut={signOutAdmin} />;
}