import { 
  collection, 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  query, 
  where, 
  orderBy, 
  getDocs,
  serverTimestamp,
  Timestamp 
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { db, storage } from './config';
import { createArtistAdminUser } from '../auth/admin-firestore';

// Artist Application Data
export interface ArtistApplication {
  id: string;
  
  // Personal Information
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  
  // Business Information
  businessName?: string;
  website?: string;
  socialMedia: {
    instagram?: string;
    facebook?: string;
    twitter?: string;
    tiktok?: string;
    other?: string;
  };
  
  // Art Information
  artStyle: string;
  artMediums: string[];
  experience: string; // 'beginner' | 'intermediate' | 'professional' | 'expert'
  portfolioLinks: string[];
  
  // Business Details
  currentSalesChannels: string[]; // 'online_store' | 'art_fairs' | 'galleries' | 'social_media' | 'none'
  monthlyRevenue: string; // Range like '$0-500', '$500-2000', etc.
  shippingCapability: boolean;
  
  // Application Details
  motivation: string; // Why they want to join
  uniqueValue: string; // What makes them unique
  
  // System Fields
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: Timestamp;
  reviewedAt?: Timestamp;
  reviewedBy?: string; // Admin UID who reviewed
  rejectionReason?: string;
  
  // Firebase Auth
  firebaseUid?: string; // Set when they login with Google
}

// Approved Artist Profile
export interface Artist {
  id: string;
  
  // From Application
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  businessName?: string;
  website?: string;
  socialMedia: {
    instagram?: string;
    facebook?: string;
    twitter?: string;
    tiktok?: string;
    other?: string;
  };
  artStyle: string;
  artMediums: string[];
  
  // Business Settings
  commissionRate: number; // Platform takes this percentage (default 10%)
  stripeAccountId?: string; // Stripe Connect account ID
  stripeOnboardingComplete: boolean;
  
  // Profile
  bio?: string;
  profileImageUrl?: string;
  bannerImageUrl?: string;
  
  // Status
  isActive: boolean;
  
  // Statistics
  totalProducts: number;
  totalSales: number;
  totalRevenue: number;
  
  // Timestamps
  approvedAt: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  lastLoginAt?: Timestamp;
  
  // System
  firebaseUid: string;
  applicationId: string; // Reference to original application
}

// Application Management Functions
export async function submitArtistApplication(
  applicationData: Omit<ArtistApplication, 'id' | 'status' | 'submittedAt'>
): Promise<string> {
  try {
    const applicationId = `app_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const application: ArtistApplication = {
      ...applicationData,
      id: applicationId,
      status: 'pending',
      submittedAt: serverTimestamp() as Timestamp,
    };
    
    const applicationRef = doc(db, 'artist_applications', applicationId);
    await setDoc(applicationRef, application);
    
    return applicationId;
  } catch (error) {
    console.error('Error submitting artist application:', error);
    throw new Error('Failed to submit application');
  }
}

export async function getArtistApplication(applicationId: string): Promise<ArtistApplication | null> {
  try {
    const applicationRef = doc(db, 'artist_applications', applicationId);
    const snapshot = await getDoc(applicationRef);
    
    if (snapshot.exists()) {
      return {
        id: snapshot.id,
        ...snapshot.data()
      } as ArtistApplication;
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching artist application:', error);
    throw new Error('Failed to fetch application');
  }
}

export async function getPendingApplications(): Promise<ArtistApplication[]> {
  try {
    const applicationsRef = collection(db, 'artist_applications');
    const q = query(
      applicationsRef, 
      where('status', '==', 'pending'),
      orderBy('submittedAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as ArtistApplication));
  } catch (error) {
    console.error('Error fetching pending applications:', error);
    throw new Error('Failed to fetch pending applications');
  }
}

export async function getAllApplications(): Promise<ArtistApplication[]> {
  try {
    const applicationsRef = collection(db, 'artist_applications');
    const q = query(applicationsRef, orderBy('submittedAt', 'desc'));
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as ArtistApplication));
  } catch (error) {
    console.error('Error fetching applications:', error);
    throw new Error('Failed to fetch applications');
  }
}

export async function approveArtistApplication(
  applicationId: string,
  reviewerUid: string,
  commissionRate: number = 10
): Promise<string> {
  try {
    const application = await getArtistApplication(applicationId);
    if (!application) {
      throw new Error('Application not found');
    }
    
    if (application.status !== 'pending') {
      throw new Error('Application is not pending');
    }
    
    // Create artist profile
    const artistId = `artist_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const artist: Artist = {
      id: artistId,
      firstName: application.firstName,
      lastName: application.lastName,
      email: application.email,
      phone: application.phone,
      businessName: application.businessName,
      website: application.website,
      socialMedia: application.socialMedia,
      artStyle: application.artStyle,
      artMediums: application.artMediums,
      commissionRate,
      stripeOnboardingComplete: false,
      isActive: true,
      totalProducts: 0,
      totalSales: 0,
      totalRevenue: 0,
      approvedAt: serverTimestamp() as Timestamp,
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp,
      firebaseUid: application.firebaseUid || '',
      applicationId: applicationId,
    };
    
    // Save artist profile
    const artistRef = doc(db, 'artists', artistId);
    await setDoc(artistRef, artist);
    
    // Create admin user for the artist if they have a Firebase UID
    if (application.firebaseUid) {
      await createArtistAdminUser(
        application.firebaseUid,
        {
          email: application.email,
          firstName: application.firstName,
          lastName: application.lastName,
          artistId: artistId,
        },
        reviewerUid
      );
    }
    
    // Update application status
    const applicationRef = doc(db, 'artist_applications', applicationId);
    await updateDoc(applicationRef, {
      status: 'approved',
      reviewedAt: serverTimestamp(),
      reviewedBy: reviewerUid,
    });
    
    return artistId;
  } catch (error) {
    console.error('Error approving artist application:', error);
    throw new Error('Failed to approve application');
  }
}

export async function rejectArtistApplication(
  applicationId: string,
  reviewerUid: string,
  rejectionReason: string
): Promise<void> {
  try {
    const applicationRef = doc(db, 'artist_applications', applicationId);
    await updateDoc(applicationRef, {
      status: 'rejected',
      reviewedAt: serverTimestamp(),
      reviewedBy: reviewerUid,
      rejectionReason,
    });
  } catch (error) {
    console.error('Error rejecting artist application:', error);
    throw new Error('Failed to reject application');
  }
}

// Artist Management Functions
export async function getArtist(artistId: string): Promise<Artist | null> {
  try {
    const artistRef = doc(db, 'artists', artistId);
    const snapshot = await getDoc(artistRef);
    
    if (snapshot.exists()) {
      return {
        id: snapshot.id,
        ...snapshot.data()
      } as Artist;
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching artist:', error);
    throw new Error('Failed to fetch artist');
  }
}

export async function getArtistByEmail(email: string): Promise<Artist | null> {
  try {
    const artistsRef = collection(db, 'artists');
    const q = query(artistsRef, where('email', '==', email), where('isActive', '==', true));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return null;
    }
    
    const artistDoc = querySnapshot.docs[0];
    return {
      id: artistDoc.id,
      ...artistDoc.data()
    } as Artist;
  } catch (error) {
    console.error('Error getting artist by email:', error);
    return null;
  }
}

export async function getArtistByFirebaseUid(firebaseUid: string): Promise<Artist | null> {
  try {
    const artistsRef = collection(db, 'artists');
    const q = query(artistsRef, where('firebaseUid', '==', firebaseUid), where('isActive', '==', true));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return null;
    }
    
    const artistDoc = querySnapshot.docs[0];
    return {
      id: artistDoc.id,
      ...artistDoc.data()
    } as Artist;
  } catch (error) {
    console.error('Error getting artist by Firebase UID:', error);
    return null;
  }
}

export async function getAllArtists(): Promise<Artist[]> {
  try {
    const artistsRef = collection(db, 'artists');
    const q = query(artistsRef, where('isActive', '==', true), orderBy('createdAt', 'desc'));
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Artist));
  } catch (error) {
    console.error('Error fetching artists:', error);
    throw new Error('Failed to fetch artists');
  }
}

export async function updateArtist(
  artistId: string, 
  updates: Partial<Omit<Artist, 'id' | 'createdAt' | 'approvedAt' | 'applicationId'>>
): Promise<void> {
  try {
    const artistRef = doc(db, 'artists', artistId);
    await updateDoc(artistRef, {
      ...updates,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error('Error updating artist:', error);
    throw new Error('Failed to update artist');
  }
}

export async function deactivateArtist(artistId: string): Promise<void> {
  try {
    const artistRef = doc(db, 'artists', artistId);
    await updateDoc(artistRef, {
      isActive: false,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error('Error deactivating artist:', error);
    throw new Error('Failed to deactivate artist');
  }
}

export async function uploadArtistProfileImage(artistId: string, file: File): Promise<string> {
  try {
    // Create a unique filename
    const fileName = `${Date.now()}-${file.name}`;
    const fileRef = ref(storage, `profile-images/${artistId}/${fileName}`);
    
    // Upload the file
    await uploadBytes(fileRef, file);
    
    // Get the download URL
    const downloadURL = await getDownloadURL(fileRef);
    
    // Update the artist's profile image URL in Firestore
    const artistRef = doc(db, 'artists', artistId);
    await updateDoc(artistRef, {
      profileImageUrl: downloadURL,
      updatedAt: serverTimestamp(),
    });
    
    return downloadURL;
  } catch (error) {
    console.error('Error uploading profile image:', error);
    throw new Error('Failed to upload profile image');
  }
}

export async function deleteArtistProfileImage(artistId: string, imageUrl: string): Promise<void> {
  try {
    // Extract the file path from the URL to delete from storage
    const urlParts = imageUrl.split('/');
    const fileName = urlParts[urlParts.length - 1].split('?')[0];
    const fileRef = ref(storage, `profile-images/${artistId}/${fileName}`);
    
    // Delete the file from storage
    await deleteObject(fileRef);
    
    // Update the artist's profile to remove the image URL
    const artistRef = doc(db, 'artists', artistId);
    await updateDoc(artistRef, {
      profileImageUrl: null,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error('Error deleting profile image:', error);
    throw new Error('Failed to delete profile image');
  }
}
