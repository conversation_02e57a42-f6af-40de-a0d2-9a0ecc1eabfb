import { 
  signInWithEmailAndPassword, 
  signOut, 
  onAuthStateChanged, 
  User,
  GoogleAuthProvider,
  signInWithPopup,
  multiFactor,
  PhoneAuthProvider,
  RecaptchaVerifier,
  PhoneMultiFactorGenerator
} from 'firebase/auth';
import { auth } from '../firebase/config';
import { isUserAdmin, updateAdminLastLogin, AdminUser } from './admin-firestore';

export const signInAdmin = async (email: string, password: string): Promise<AdminUser> => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    // Check if user is admin in Firestore
    const adminUser = await isUserAdmin(user);
    if (!adminUser) {
      await signOut(auth);
      throw new Error('Unauthorized - Admin access required');
    }
    
    // Update last login time
    await updateAdminLastLogin(user.uid);
    
    return adminUser;
  } catch (error: any) {
    console.error('Error signing in:', error);
    
    // Handle MFA requirement
    if (error.code === 'auth/multi-factor-auth-required') {
      throw {
        code: 'auth/multi-factor-auth-required',
        resolver: error.resolver,
        message: 'Multi-factor authentication required'
      };
    }
    
    throw error;
  }
};

export const signInAdminWithGoogle = async (): Promise<AdminUser> => {
  try {
    const provider = new GoogleAuthProvider();
    
    // Force account selection and password prompt every time
    provider.setCustomParameters({
      prompt: 'select_account consent', // Forces account selection and consent screen
      login_hint: '', // Clear any previous login hints
      hd: '', // Clear any hosted domain hints
    });
    
    // Add additional scopes to force fresh consent
    provider.addScope('email');
    provider.addScope('profile');
    
    // Clear any existing Firebase auth state
    await signOut(auth);
    
    const userCredential = await signInWithPopup(auth, provider);
    const user = userCredential.user;
    
    // Check if user is admin in Firestore
    const adminUser = await isUserAdmin(user);
    if (!adminUser) {
      await signOut(auth);
      throw new Error('Unauthorized - Admin access required');
    }
    
    // Update last login time
    await updateAdminLastLogin(user.uid);
    
    return adminUser;
  } catch (error: any) {
    console.error('Error signing in with Google:', error);
    
    // Handle MFA requirement for Google sign-in
    if (error.code === 'auth/multi-factor-auth-required') {
      throw {
        code: 'auth/multi-factor-auth-required',
        resolver: error.resolver,
        message: 'Multi-factor authentication required'
      };
    }
    
    throw error;
  }
};

export const verifyMfaCode = async (resolver: any, verificationCode: string, verificationId: string): Promise<AdminUser> => {
  try {
    const cred = PhoneAuthProvider.credential(verificationId, verificationCode);
    const multiFactorAssertion = PhoneMultiFactorGenerator.assertion(cred);
    const userCredential = await resolver.resolveSignIn(multiFactorAssertion);
    const user = userCredential.user;
    
    // Check if user is admin in Firestore
    const adminUser = await isUserAdmin(user);
    if (!adminUser) {
      await signOut(auth);
      throw new Error('Unauthorized - Admin access required');
    }
    
    // Update last login time
    await updateAdminLastLogin(user.uid);
    
    return adminUser;
  } catch (error) {
    console.error('Error verifying MFA code:', error);
    throw error;
  }
};

export const initiateMfa = async (resolver: any, recaptchaContainer: string) => {
  try {
    const recaptchaVerifier = new RecaptchaVerifier(auth, recaptchaContainer, {
      'size': 'invisible',
      'callback': () => {
        // reCAPTCHA solved
      }
    });

    const phoneInfoOptions = {
      multiFactorHint: resolver.hints[0],
      session: resolver.session
    };

    const phoneAuthProvider = new PhoneAuthProvider(auth);
    const verificationId = await phoneAuthProvider.verifyPhoneNumber(phoneInfoOptions, recaptchaVerifier);
    
    return verificationId;
  } catch (error) {
    console.error('Error initiating MFA:', error);
    throw error;
  }
};

export const signOutAdmin = () => signOut(auth);

export const isAdmin = async (user: User | null): Promise<AdminUser | null> => {
  if (!user) return null;
  return await isUserAdmin(user);
};

export const onAdminAuthStateChanged = (callback: (admin: AdminUser | null) => void) => {
  return onAuthStateChanged(auth, async (user) => {
    if (user) {
      const adminUser = await isUserAdmin(user);
      callback(adminUser);
    } else {
      callback(null);
    }
  });
};