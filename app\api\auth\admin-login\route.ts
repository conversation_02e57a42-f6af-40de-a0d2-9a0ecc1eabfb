import { NextRequest, NextResponse } from 'next/server';
import { signInWithEmailAndPassword } from 'firebase/auth';
import { auth } from '@/lib/firebase/config';
import { createAdminSession } from '@/lib/auth/middleware';
import { isUserAdmin, updateAdminLastLogin } from '@/lib/auth/admin-firestore';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    // Sign in with Firebase Auth
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Check if user is admin in Firestore
    const adminUser = await isUserAdmin(user);
    if (!adminUser) {
      await auth.signOut();
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    // Update last login time
    await updateAdminLastLogin(user.uid);

    // Create JWT session token
    const sessionToken = createAdminSession(adminUser);

    // Create response with secure cookie
    const response = NextResponse.json({
      success: true,
      user: {
        uid: user.uid,
        email: user.email,
        role: adminUser.role,
        permissions: adminUser.permissions,
        requiresMFA: false // Will be updated based on MFA status
      }
    });

    // Set secure HTTP-only cookie
    response.cookies.set('admin-token', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60, // 24 hours
      path: '/'
    });

    return response;

  } catch (error: any) {
    console.error('Admin login error:', error);
    
    // Handle specific Firebase Auth errors
    if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    if (error.code === 'auth/multi-factor-auth-required') {
      // Handle MFA requirement
      return NextResponse.json({
        requiresMFA: true,
        mfaResolver: error.resolver?.resolverId // This will need to be handled client-side
      });
    }

    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 500 }
    );
  }
}
