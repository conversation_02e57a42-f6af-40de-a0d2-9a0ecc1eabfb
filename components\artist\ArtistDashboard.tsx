'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { AdminUser } from '@/lib/auth/admin-firestore';
import { getArtistByFirebaseUid } from '@/lib/firebase/artists';
import { getProductsByArtistId } from '@/lib/firebase/artist-products';
import { helvetica, editorialNew } from '@/app/fonts';
import cx from 'classnames';
import ArtistProfileSettings from './ArtistProfileSettings';
import ArtistProductManagement from './ArtistProductManagement';
import ArtistOrders from './ArtistOrders';
import ArtistInvoices from './ArtistInvoices';
import AnalyticsDashboard from './AnalyticsDashboard';

interface ArtistDashboardProps {
  user: AdminUser;
  signOut: () => void;
}

export default function ArtistDashboard({ user, signOut }: ArtistDashboardProps) {
  const [artistProfile, setArtistProfile] = useState<any>(null);
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'products' | 'orders' | 'invoices' | 'analytics' | 'profile'>('analytics');
  const [showProfileSettings, setShowProfileSettings] = useState(false);

  useEffect(() => {
    loadArtistProfile();
  }, []);

  const loadArtistProfile = async () => {
    try {
      console.log('Loading artist profile for user:', user.uid);
      console.log('User artistId:', user.artistId);
      
      if (user.artistId) {
        const profile = await getArtistByFirebaseUid(user.uid);
        console.log('Artist profile loaded:', profile);
        setArtistProfile(profile);
        
        if (profile) {
          const artistProducts = await getProductsByArtistId(profile.id);
          console.log('Artist products loaded:', artistProducts);
          setProducts(artistProducts);
        } else {
          console.log('No artist profile found for Firebase UID:', user.uid);
        }
      } else {
        console.log('No artistId found in user data');
      }
    } catch (error) {
      console.error('Error loading artist profile:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className={cx(editorialNew.className, "text-2xl font-bold")}>
                Artist Dashboard
              </h1>
              <p className={cx(helvetica.className, "text-gray-600 mt-1")}>
                Welcome back, {user.displayName}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Link 
                href="/"
                className="text-gray-600 hover:text-gray-800"
              >
                View Store
              </Link>
              <button
                onClick={() => setShowProfileSettings(true)}
                className="bg-blue-100 text-blue-700 px-4 py-2 rounded hover:bg-blue-200 transition-colors"
              >
                Edit Profile
              </button>
              <button
                onClick={signOut}
                className="bg-gray-100 text-gray-700 px-4 py-2 rounded hover:bg-gray-200 transition-colors"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'products', name: 'My Products', icon: '🎨' },
              { id: 'orders', name: 'Orders', icon: '📦' },
              { id: 'invoices', name: 'Invoices', icon: '📧' },
              { id: 'analytics', name: 'Analytics', icon: '📊' },
              { id: 'profile', name: 'Profile', icon: '👤' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={cx(
                  "flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm",
                  activeTab === tab.id
                    ? "border-black text-black"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                )}
              >
                <span>{tab.icon}</span>
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="max-w-7xl mx-auto px-4 pb-8">
        {activeTab === 'products' && (
          <ArtistProductManagement user={user} />
        )}

        {activeTab === 'orders' && artistProfile && (
          <ArtistOrders artistId={artistProfile.id} />
        )}

        {activeTab === 'invoices' && artistProfile && (
          <ArtistInvoices artistId={artistProfile.id} />
        )}

        {activeTab === 'analytics' && artistProfile && (
          <AnalyticsDashboard artistId={artistProfile.id} />
        )}

        {activeTab === 'profile' && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className={cx(editorialNew.className, "text-xl font-semibold")}>
                Profile Information
              </h2>
              <button
                onClick={() => setShowProfileSettings(true)}
                className="bg-black text-white px-4 py-2 rounded hover:bg-gray-800"
              >
                Edit Profile
              </button>
            </div>
            
            {artistProfile ? (
              <div className="space-y-6">
                {/* Profile Image */}
                <div className="flex items-center space-x-6">
                  <div className="h-24 w-24 rounded-full overflow-hidden bg-gray-200">
                    {artistProfile.profileImageUrl ? (
                      <img
                        src={artistProfile.profileImageUrl}
                        alt="Profile"
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="h-full w-full flex items-center justify-center text-gray-400 text-2xl">
                        👤
                      </div>
                    )}
                  </div>
                  <div>
                    <h3 className={cx(editorialNew.className, "text-lg font-semibold")}>
                      {artistProfile.businessName || `${artistProfile.firstName} ${artistProfile.lastName}`}
                    </h3>
                    <p className={cx(helvetica.className, "text-gray-600")}>{artistProfile.email}</p>
                  </div>
                </div>

                {/* Profile Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                        Bio
                      </label>
                      <p className={cx(helvetica.className, "text-gray-900")}>
                        {artistProfile.bio || 'No bio provided'}
                      </p>
                    </div>
                    <div>
                      <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                        Website
                      </label>
                      <p className={cx(helvetica.className, "text-gray-900")}>
                        {artistProfile.website ? (
                          <a href={artistProfile.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                            {artistProfile.website}
                          </a>
                        ) : (
                          'Not provided'
                        )}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                        Art Style
                      </label>
                      <p className={cx(helvetica.className, "text-gray-900")}>
                        {artistProfile.artStyle || 'Not specified'}
                      </p>
                    </div>
                    <div>
                      <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-1")}>
                        Art Mediums
                      </label>
                      <p className={cx(helvetica.className, "text-gray-900")}>
                        {artistProfile.artMediums?.join(', ') || 'Not specified'}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Social Media */}
                {artistProfile.socialMedia && (
                  <div>
                    <label className={cx(helvetica.className, "block text-sm font-medium text-gray-700 mb-3")}>
                      Social Media
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {Object.entries(artistProfile.socialMedia)
                        .filter(([platform, handle]) => handle)
                        .map(([platform, handle]) => (
                          <div key={platform}>
                            <span className={cx(helvetica.className, "text-sm font-medium text-gray-500 capitalize")}>
                              {platform}:
                            </span>
                            <p className={cx(helvetica.className, "text-gray-900")}>{handle as string}</p>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <p>Loading profile information...</p>
            )}
          </div>
        )}
      </div>

      {/* Profile Settings Modal */}
      {showProfileSettings && (
        <ArtistProfileSettings
          user={user}
          onClose={() => {
            setShowProfileSettings(false);
            loadArtistProfile(); // Refresh data after edit
          }}
        />
      )}
    </div>
  );
}
