'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { helvetica, editorialNew } from '@/app/fonts';
import { FileText, Calendar, DollarSign, MapPin, User, Mail, Phone, Truck } from 'lucide-react';
import ContactInfoForm from '@/components/checkout/contact-info-form';
import GooglePlacesInput from '@/components/checkout/google-places-input';
import AddressForm from '@/components/checkout/address-form';
import InvoiceShippingStep from '@/components/checkout/invoice-shipping-step';
import StripePaymentForm from '@/components/ui/stripe-payment-form';
import cx from 'classnames';

interface Invoice {
  id: string;
  customerName: string;
  customerEmail: string;
  title: string;
  description: string;
  amount: number;
  totalAmount: number;
  dueDate: string | null;
  notes: string;
  status: string;
  createdAt: any;
  includeShipping: boolean;
  shippingCost: number;
  shippingNotes: string;
}

interface ContactInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

interface Address {
  line1: string;
  line2: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

interface ShippingOption {
  id: string;
  name: string;
  description: string;
  price: number;
  estimatedDays: string;
}

export default function PayInvoicePage() {
  const [showLoading, setShowLoading] = useState(false);
  const params = useParams();
  const invoiceId = params.invoiceId as string;
  
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(1); // 1: Info, 2: Shipping, 3: Payment
  const [isProcessing, setIsProcessing] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);

  // Form data
  const [contactInfo, setContactInfo] = useState<ContactInfo>({
    firstName: '',
    lastName: '',
    email: '',
    phone: ''
  });

  const [shippingAddress, setShippingAddress] = useState<Address>({
    line1: '',
    line2: '',
    city: '',
    state: '',
    postalCode: '',
    country: 'US'
  });

  const [shippingOptions, setShippingOptions] = useState<ShippingOption[]>([]);
  const [selectedShipping, setSelectedShipping] = useState<ShippingOption | null>(null);
  const [shippingCalculated, setShippingCalculated] = useState(false);

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (invoiceId) {
      loadInvoice();
    }
  }, [invoiceId]);

  const loadInvoice = async () => {
    try {
      const response = await fetch(`/api/invoices/${invoiceId}`);
      const data = await response.json();

      if (data.success) {
        setInvoice(data.invoice);
        // Pre-fill customer email if available
        if (data.invoice.customerEmail) {
          setContactInfo(prev => ({ ...prev, email: data.invoice.customerEmail }));
        }
      } else {
        setError(data.error || 'Invoice not found');
      }
    } catch (err) {
      console.error('Error loading invoice:', err);
      setError('Failed to load invoice');
    } finally {
      setLoading(false);
    }
  };

  const handleContactInfoChange = (newContactInfo: ContactInfo) => {
    setContactInfo(newContactInfo);
  };

  const handleAddressChange = (updates: Partial<Address>) => {
    setShippingAddress(prev => ({ ...prev, ...updates }));
  };

  const handleGooglePlacesSelect = (addressData: any) => {
    setShippingAddress(prev => ({
      ...prev,
      line1: addressData.line1 || prev.line1,
      city: addressData.city || prev.city,
      state: addressData.state || prev.state,
      postalCode: addressData.postalCode || prev.postalCode,
      country: addressData.country || prev.country
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Contact info validation
    if (!contactInfo.firstName.trim()) newErrors.firstName = 'First name is required';
    if (!contactInfo.lastName.trim()) newErrors.lastName = 'Last name is required';
    if (!contactInfo.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(contactInfo.email)) {
      newErrors.email = 'Please enter a valid email';
    }
    if (!contactInfo.phone.trim()) newErrors.phone = 'Phone number is required';

    // Address validation
    if (!shippingAddress.line1.trim()) newErrors.line1 = 'Address is required';
    if (!shippingAddress.city.trim()) newErrors.city = 'City is required';
    if (!shippingAddress.state.trim() && shippingAddress.country === 'US') newErrors.state = 'State is required';
    if (!shippingAddress.postalCode.trim()) newErrors.postalCode = 'Postal code is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleContinueToShipping = () => {
    if (!validateForm()) return;
    // Skip shipping step if shipping is included in invoice
    if (invoice?.includeShipping) {
      handleContinueToPaymentDirect();
    } else {
      setCurrentStep(2);
    }
  };

  const handleContinueToPaymentDirect = async () => {
    setShowLoading(true);
    setIsProcessing(true);
    try {
      // Create payment intent for the invoice (shipping already included)
      const response = await fetch('/api/invoices/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          invoiceId,
          contactInfo,
          shippingAddress,
          // No selectedShipping needed when shipping is included in invoice
        }),
      });

      const data = await response.json();

      if (data.success) {
        setClientSecret(data.clientSecret);
        setCurrentStep(invoice?.includeShipping ? 2 : 3);
        setShowLoading(false);
      } else {
        setError(data.error || 'Failed to create payment intent');
        setShowLoading(false);
      }
    } catch (error) {
      console.error('Error creating payment intent:', error);
      setError('Failed to create payment intent');
      setShowLoading(false);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleContinueToPayment = async () => {
    if (!selectedShipping) {
      alert('Please select a shipping option');
      return;
    }

    setShowLoading(true);
    setIsProcessing(true);
    try {
      // Create payment intent for the invoice with shipping
      const response = await fetch('/api/invoices/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          invoiceId,
          contactInfo,
          shippingAddress,
          selectedShipping,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setClientSecret(data.clientSecret);
        setCurrentStep(3);
        setShowLoading(false); // Hide loading when successfully moving to payment step
      } else {
        setError(data.error || 'Failed to create payment intent');
        setShowLoading(false);
      }
    } catch (error) {
      console.error('Error creating payment intent:', error);
      setError('Failed to create payment intent');
      setShowLoading(false);
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePaymentSuccess = async (paymentIntentId: string) => {
    try {
      // Show loading popup for payment confirmation
      setShowLoading(true);
      setIsProcessing(true);

      // Confirm the invoice payment
      const response = await fetch('/api/invoices/confirm-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          invoiceId,
          paymentIntentId,
          contactInfo,
          shippingAddress,
          selectedShipping,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Redirect to success page
        window.location.href = `/pay/${invoiceId}/success`;
      } else {
        throw new Error(data.error || 'Failed to confirm payment');
      }
    } catch (error) {
      console.error('Error confirming payment:', error);
      setShowLoading(false);
      alert('Payment succeeded but confirmation failed. Please contact support.');
    } finally {
      setIsProcessing(false);
      // Don't hide loading here - let the redirect handle it
    }
  };

  const handlePaymentError = (error: string) => {
    setShowLoading(false);
    alert('Payment failed: ' + error);
  };

  if (loading) {
    return (
      <div className={cx(helvetica.className, "min-h-screen bg-gray-50 flex items-center justify-center")}>
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
          <p className="mt-4 text-gray-600">Loading invoice...</p>
        </div>
      </div>
    );
  }

  if (error || !invoice) {
    return (
      <div className={cx(helvetica.className, "min-h-screen bg-gray-50 flex items-center justify-center")}>
        <div className="text-center max-w-md mx-auto px-4">
          <h1 className={cx(editorialNew.className, "text-2xl mb-4")}>Invoice Not Found</h1>
          <p className="text-gray-600 mb-6">{error || 'The requested invoice could not be found.'}</p>
        </div>
      </div>
    );
  }

  if (invoice.status === 'paid') {
    return (
      <div className={cx(helvetica.className, "min-h-screen bg-gray-50 flex items-center justify-center")}>
        <div className="text-center max-w-md mx-auto px-4">
          <h1 className={cx(editorialNew.className, "text-2xl mb-4")}>Invoice Already Paid</h1>
          <p className="text-gray-600">This invoice has already been paid.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cx(helvetica.className, "min-h-screen bg-gray-50")}> 
      {showLoading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="bg-white rounded-lg p-8 shadow-lg flex flex-col items-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mb-4"></div>
            <p className="text-lg font-medium">Processing payment...</p>
          </div>
        </div>
      )}
      {/* Processing Overlay */}
      {isProcessing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className="bg-white p-8 rounded-lg shadow-lg text-center max-w-md mx-4">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-black mb-4"></div>
            <h3 className={cx(editorialNew.className, "text-xl font-normal mb-2")}>
              {currentStep === 1 ? 'Preparing Payment...' : 'Processing Payment...'}
            </h3>
            <p className="text-gray-600 text-sm">
              Please don't close this window.
            </p>
          </div>
        </div>
      )}

      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Invoice Header */}
        <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
          <div className="flex items-center mb-4">
            <FileText size={24} className="mr-3" />
            <h1 className={cx(editorialNew.className, "text-2xl font-normal")}>
              {invoice.title}
            </h1>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <p className="text-gray-600 mb-2">Description:</p>
              <p className="whitespace-pre-wrap">{invoice.description}</p>
              {invoice.notes && (
                <>
                  <p className="text-gray-600 mt-4 mb-2">Notes:</p>
                  <p className="whitespace-pre-wrap text-sm">{invoice.notes}</p>
                </>
              )}
            </div>
            
            <div className="text-right">
              <div className="space-y-2">
                {invoice.includeShipping ? (
                  <>
                    <div className="text-sm text-gray-600 space-y-1">
                      <div className="flex justify-end">
                        <span className="mr-2">Base Amount:</span>
                        <span>${invoice.amount.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-end">
                        <span className="mr-2">Shipping:</span>
                        <span>${invoice.shippingCost.toFixed(2)}</span>
                      </div>
                      {invoice.shippingNotes && (
                        <div className="text-xs text-gray-500 mt-2 text-left">
                          <p className="font-medium">Shipping Details:</p>
                          <p>{invoice.shippingNotes}</p>
                        </div>
                      )}
                    </div>
                    <div className="border-t pt-2">
                      <div className="text-3xl font-bold">
                        ${invoice.totalAmount.toFixed(2)}
                      </div>
                      <div className="text-sm text-gray-600">Total Amount</div>
                    </div>
                  </>
                ) : (
                  <div className="text-3xl font-bold mb-2">
                    ${invoice.amount.toFixed(2)}
                  </div>
                )}
              </div>
              {invoice.dueDate && (
                <div className="flex items-center justify-end text-sm text-gray-600 mt-2">
                  <Calendar size={16} className="mr-1" />
                  Due: {new Date(invoice.dueDate).toLocaleDateString()}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Step Indicator */}
        <div className="flex justify-center mb-8">
          <div className="flex items-center space-x-4">
            <div className={`flex items-center ${currentStep >= 1 ? 'text-black' : 'text-gray-400'}`}>
              <span className={`w-8 h-8 rounded-full flex items-center justify-center text-sm mr-2 ${
                currentStep >= 1 ? 'bg-black text-white' : 'bg-gray-300'
              }`}>1</span>
              Customer Info
            </div>
            {!invoice.includeShipping && (
              <div className={`flex items-center ${currentStep >= 2 ? 'text-black' : 'text-gray-400'}`}>
                <span className={`w-8 h-8 rounded-full flex items-center justify-center text-sm mr-2 ${
                  currentStep >= 2 ? 'bg-black text-white' : 'bg-gray-300'
                }`}>2</span>
                Shipping
              </div>
            )}
            <div className={`flex items-center ${currentStep >= (invoice.includeShipping ? 2 : 3) ? 'text-black' : 'text-gray-400'}`}>
              <span className={`w-8 h-8 rounded-full flex items-center justify-center text-sm mr-2 ${
                currentStep >= (invoice.includeShipping ? 2 : 3) ? 'bg-black text-white' : 'bg-gray-300'
              }`}>{invoice.includeShipping ? '2' : '3'}</span>
              Payment
            </div>
          </div>
        </div>

        {/* Step Content */}
        {currentStep === 1 && (
          <div className="space-y-6">
            <ContactInfoForm
              contactInfo={contactInfo}
              onContactInfoChange={handleContactInfoChange}
              errors={errors}
            />

            <div className="bg-white p-6 rounded-lg border">
              <GooglePlacesInput
                onAddressSelect={handleGooglePlacesSelect}
                placeholder="Start typing your shipping address..."
              />
            </div>

            <AddressForm
              address={shippingAddress}
              onAddressChange={handleAddressChange}
              errors={errors}
              title="Shipping Address"
            />

            <button
              onClick={handleContinueToShipping}
              disabled={isProcessing}
              className="w-full bg-black text-white font-medium py-3 px-6 rounded-lg hover:bg-gray-800 disabled:opacity-50 transition-colors"
            >
              {invoice.includeShipping ? 'Continue to Payment' : 'Continue to Shipping'}
            </button>
          </div>
        )}

        {currentStep === 2 && (
          <div className="space-y-6">
            <InvoiceShippingStep 
              customerInfo={{
                email: contactInfo.email,
                phone: contactInfo.phone,
                shippingAddress: {
                  firstName: contactInfo.firstName,
                  lastName: contactInfo.lastName,
                  line1: shippingAddress.line1,
                  line2: shippingAddress.line2,
                  city: shippingAddress.city,
                  state: shippingAddress.state,
                  postalCode: shippingAddress.postalCode,
                  country: shippingAddress.country,
                },
                billingAddress: shippingAddress, // Use same as shipping for invoice
                sameAsBilling: true
              }}
              invoiceAmount={invoice?.amount || 0}
              shippingOptions={shippingOptions}
              setShippingOptions={setShippingOptions}
              selectedShipping={selectedShipping}
              setSelectedShipping={setSelectedShipping}
              onComplete={handleContinueToPayment}
              onBack={() => setCurrentStep(1)}
              isLoading={isProcessing}
              setIsLoading={setIsProcessing}
            />
          </div>
        )}

        {((currentStep === 3 && !invoice.includeShipping) || (currentStep === 2 && invoice.includeShipping)) && clientSecret && (
          <div className="space-y-6">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-lg font-medium mb-4">Payment</h3>
              
              {/* Order Summary */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium mb-3">Order Summary</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Invoice Amount:</span>
                    <span>${invoice?.amount.toFixed(2)}</span>
                  </div>
                  {invoice.includeShipping ? (
                    <div className="flex justify-between">
                      <span>Shipping (Included):</span>
                      <span>${invoice.shippingCost.toFixed(2)}</span>
                    </div>
                  ) : selectedShipping && (
                    <div className="flex justify-between">
                      <span>Shipping ({selectedShipping.name}):</span>
                      <span>
                        {selectedShipping.price === 0 ? 'FREE' : `$${selectedShipping.price.toFixed(2)}`}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between font-medium border-t pt-2">
                    <span>Total:</span>
                    <span>
                      ${invoice.includeShipping 
                        ? invoice.totalAmount.toFixed(2) 
                        : ((invoice?.amount || 0) + (selectedShipping?.price || 0)).toFixed(2)
                      }
                    </span>
                  </div>
                </div>
              </div>

              <StripePaymentForm
                clientSecret={clientSecret}
                onPaymentSuccess={handlePaymentSuccess}
                onPaymentError={handlePaymentError}
                isProcessing={isProcessing}
                setIsProcessing={setIsProcessing}
              />
            </div>

            <button
              onClick={() => setCurrentStep(invoice.includeShipping ? 1 : 2)}
              disabled={isProcessing}
              className="bg-gray-200 text-black font-medium py-3 px-6 rounded-lg hover:bg-gray-300 disabled:opacity-50 transition-colors"
            >
              {invoice.includeShipping ? 'Back to Customer Info' : 'Back to Shipping'}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
