import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { getStorage } from 'firebase/storage';
import { getAnalytics } from 'firebase/analytics';

const firebaseConfig = {
  apiKey: "AIzaSyAQcsu53n9bjVEAyJIfXEWMy9FvTanNmNg",
  authDomain: "milos-jungle.firebaseapp.com",
  projectId: "milos-jungle",
  storageBucket: "milos-jungle.firebasestorage.app", // Updated to match your actual bucket
  messagingSenderId: "1078158771916",
  appId: "1:1078158771916:web:b912c35e6ddc5f1e70e469",
  measurementId: "G-TC63N14Q9D"
};

const app = initializeApp(firebaseConfig);
export const db = getFirestore(app);
export const auth = getAuth(app);
export const storage = getStorage(app);

// Analytics - only initialize on client side
export const analytics = typeof window !== 'undefined' ? getAnalytics(app) : null;


