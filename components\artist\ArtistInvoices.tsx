'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { helvetica, editorialNew } from '@/app/fonts';
import cx from 'classnames';
import { Copy, Check, Send, Eye } from 'lucide-react';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

interface Invoice {
  id: string;
  customerName?: string;
  customerEmail?: string;
  title: string;
  description: string;
  amount: number;
  totalAmount?: number;
  dueDate: string;
  notes: string;
  status: 'pending' | 'paid' | 'overdue';
  createdAt: any;
  includeShipping: boolean;
  shippingCost?: number;
  shippingNotes?: string;
}

interface ArtistInvoicesProps {
  artistId: string;
}

export default function ArtistInvoices({ artistId }: ArtistInvoicesProps) {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [copiedInvoice, setCopiedInvoice] = useState<string | null>(null);

  useEffect(() => {
    loadInvoices();
  }, [artistId]);

  const loadInvoices = async () => {
    try {
      const invoicesRef = collection(db, 'invoices');
      const q = query(
        invoicesRef,
        where('artistId', '==', artistId),
        orderBy('createdAt', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      const invoicesData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Invoice[];
      
      setInvoices(invoicesData);
    } catch (error) {
      console.error('Error loading invoices:', error);
    } finally {
      setLoading(false);
    }
  };

  const copyPaymentLink = async (invoiceId: string) => {
    const link = `${window.location.origin}/pay/${invoiceId}`;
    try {
      await navigator.clipboard.writeText(link);
      setCopiedInvoice(invoiceId);
      setTimeout(() => setCopiedInvoice(null), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const formatDate = (date: any) => {
    if (!date) return 'No due date';
    const dateObj = date.toDate ? date.toDate() : new Date(date);
    return dateObj.toLocaleDateString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto mb-4"></div>
        <p className="text-gray-600">Loading invoices...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className={cx(editorialNew.className, "text-xl font-semibold")}>
            Invoice Management
          </h2>
          <p className={cx(helvetica.className, "text-gray-600 mt-1")}>
            Manage your custom invoices and payment links
          </p>
        </div>
        <Link 
          href="/admin/create-invoice"
          className="bg-black text-white px-4 py-2 rounded hover:bg-gray-800 transition-colors flex items-center"
        >
          <span className="mr-2">📧</span>
          Create New Invoice
        </Link>
      </div>

      {invoices.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="text-center py-12">
            <div className="text-4xl mb-4">📧</div>
            <h3 className={cx(editorialNew.className, "text-lg font-medium text-gray-900 mb-2")}>
              No Invoices Yet
            </h3>
            <p className={cx(helvetica.className, "text-gray-600 mb-6")}>
              Create custom invoices and send payment links to your customers for custom orders and commissioned work.
            </p>
            <Link 
              href="/admin/create-invoice"
              className="inline-flex items-center bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors"
            >
              <span className="mr-2">📧</span>
              Create Your First Invoice
            </Link>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className={cx(editorialNew.className, "text-lg font-medium")}>
              Your Invoices ({invoices.length})
            </h3>
          </div>
          
          <div className="divide-y divide-gray-200">
            {invoices.map((invoice) => (
              <div key={invoice.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h4 className={cx(editorialNew.className, "font-medium text-gray-900")}>
                        {invoice.title}
                      </h4>
                      <span className={cx(
                        "px-2 py-1 text-xs font-medium rounded-full",
                        getStatusColor(invoice.status)
                      )}>
                        {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                      </span>
                    </div>
                    
                    <p className={cx(helvetica.className, "text-gray-600 text-sm mb-2")}>
                      {invoice.description}
                    </p>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      {invoice.customerName && (
                        <span>Customer: {invoice.customerName}</span>
                      )}
                      {invoice.customerEmail && (
                        <span>Email: {invoice.customerEmail}</span>
                      )}
                      <span>Due: {formatDate(invoice.dueDate)}</span>
                    </div>
                  </div>
                  
                  <div className="text-right ml-6">
                    <div className={cx(editorialNew.className, "text-lg font-semibold text-gray-900")}>
                      ${(invoice.totalAmount || invoice.amount).toFixed(2)}
                    </div>
                    {invoice.includeShipping && invoice.shippingCost && (
                      <div className="text-xs text-gray-500">
                        Includes shipping: ${invoice.shippingCost.toFixed(2)}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-3 mt-4">
                  <button
                    onClick={() => copyPaymentLink(invoice.id)}
                    className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 transition-colors"
                  >
                    {copiedInvoice === invoice.id ? (
                      <>
                        <Check size={14} />
                        <span className="text-xs">Copied!</span>
                      </>
                    ) : (
                      <>
                        <Copy size={14} />
                        <span className="text-xs">Copy Link</span>
                      </>
                    )}
                  </button>
                  
                  <Link
                    href={`/pay/${invoice.id}`}
                    target="_blank"
                    className="flex items-center space-x-1 text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    <Eye size={14} />
                    <span className="text-xs">Preview</span>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
