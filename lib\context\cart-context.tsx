'use client';

import React, { createContext, useContext, useReducer, useEffect } from 'react';

interface CartItem {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  price: number;
  quantity: number;
  maxStock?: number; // Maximum available stock for this item
  artistId?: string; // Track which artist this item belongs to
  variations?: Record<string, string>; // Selected variation values (variationId -> valueId)
  variationText?: string; // Human-readable variation text for display
  priceModifier?: number; // Additional cost from variations
}

interface ShippingRate {
  name: string;
  description: string;
  price: number;
  estimatedDays: string;
}

interface ShippingAddress {
  name?: string;
  line1?: string;
  line2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country: string;
}

interface CartState {
  items: CartItem[];
  subtotal: number;
  shipping: ShippingRate | null;
  shippingAddress: ShippingAddress | null;
  total: number;
  itemCount: number;
}

type CartAction =
  | { type: 'ADD_ITEM'; payload: Omit<CartItem, 'quantity'> }
  | { type: 'REMOVE_ITEM'; payload: string }
  | { type: 'UPDATE_QUANTITY'; payload: { id: string; quantity: number } }
  | { type: 'SET_SHIPPING'; payload: { rate: ShippingRate | null; address?: ShippingAddress } }
  | { type: 'CLEAR_CART' }
  | { type: 'LOAD_CART'; payload: CartItem[] };

const CartContext = createContext<{
  state: CartState;
  dispatch: React.Dispatch<CartAction>;
} | null>(null);

function cartReducer(state: CartState, action: CartAction): CartState {
  switch (action.type) {
    case 'ADD_ITEM': {
      const existingItem = state.items.find(item => item.id === action.payload.id);

      if (existingItem) {
        const newQuantity = existingItem.quantity + 1;
        const maxAllowed = existingItem.maxStock || Infinity;
        
        // Don't exceed available stock
        if (newQuantity > maxAllowed) {
          return state; // Don't add if it would exceed stock
        }
        
        const updatedItems = state.items.map(item =>
          item.id === action.payload.id
            ? { ...item, quantity: newQuantity }
            : item
        );
        return calculateTotals({ ...state, items: updatedItems });
      } else {
        const maxAllowed = action.payload.maxStock || Infinity;
        
        // Don't add if no stock available
        if (maxAllowed === 0) {
          return state;
        }
        
        const newItems = [...state.items, { ...action.payload, quantity: 1 }];
        return calculateTotals({ ...state, items: newItems });
      }
    }

    case 'REMOVE_ITEM': {
      const newItems = state.items.filter(item => item.id !== action.payload);
      return calculateTotals({ ...state, items: newItems });
    }

    case 'UPDATE_QUANTITY': {
      if (action.payload.quantity <= 0) {
        const newItems = state.items.filter(item => item.id !== action.payload.id);
        return calculateTotals({ ...state, items: newItems });
      }

      const updatedItems = state.items.map(item => {
        if (item.id === action.payload.id) {
          const maxAllowed = item.maxStock || Infinity;
          const requestedQuantity = action.payload.quantity;
          
          // Cap quantity at available stock
          const finalQuantity = Math.min(requestedQuantity, maxAllowed);
          
          return { ...item, quantity: finalQuantity };
        }
        return item;
      });
      return calculateTotals({ ...state, items: updatedItems });
    }

    case 'SET_SHIPPING': {
      return calculateTotals({
        ...state,
        shipping: action.payload.rate,
        shippingAddress: action.payload.address || state.shippingAddress
      });
    }

    case 'CLEAR_CART':
      return { items: [], subtotal: 0, shipping: null, shippingAddress: null, total: 0, itemCount: 0 };

    case 'LOAD_CART':
      return calculateTotals({ ...state, items: action.payload });

    default:
      return state;
  }
}

function calculateTotals(state: CartState): CartState {
  const subtotal = state.items.reduce((sum, item) => {
    const itemPrice = item.price + (item.priceModifier || 0);
    return sum + (itemPrice * item.quantity);
  }, 0);
  const shippingCost = state.shipping?.price || 0;
  const total = subtotal + shippingCost;
  const itemCount = state.items.reduce((sum, item) => sum + item.quantity, 0);
  return { ...state, subtotal, total, itemCount };
}

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(cartReducer, {
    items: [],
    subtotal: 0,
    shipping: null,
    shippingAddress: null,
    total: 0,
    itemCount: 0,
  });

  // Load cart from localStorage on mount
  useEffect(() => {
    const savedCart = localStorage.getItem('milos-Menagerie-cart');
    if (savedCart) {
      try {
        const cartItems = JSON.parse(savedCart);
        dispatch({ type: 'LOAD_CART', payload: cartItems });
      } catch (error) {
        console.error('Error loading cart from localStorage:', error);
      }
    }
  }, []);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('milos-Menagerie-cart', JSON.stringify(state.items));
  }, [state.items]);

  return (
    <CartContext.Provider value={{ state, dispatch }}>
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}