import { NextRequest, NextResponse } from 'next/server';
import { collection, getDocs, doc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

export async function POST(request: NextRequest) {
  try {
    const { artistId } = await request.json();
    
    if (!artistId) {
      return NextResponse.json({ error: 'Artist ID is required' }, { status: 400 });
    }
    
    // Get all custom orders
    const customOrdersRef = collection(db, 'custom_orders');
    const customOrdersSnapshot = await getDocs(customOrdersRef);
    
    const updates: Promise<void>[] = [];
    
    customOrdersSnapshot.forEach((orderDoc) => {
      const data = orderDoc.data();
      
      // Only update if artistId is missing
      if (!data.artistId) {
        const orderRef = doc(db, 'custom_orders', orderDoc.id);
        updates.push(updateDoc(orderRef, {
          artistId: artistId
        }));
      }
    });
    
    await Promise.all(updates);
    
    return NextResponse.json({ 
      success: true, 
      message: `Updated ${updates.length} custom orders with artistId`,
      updatedCount: updates.length
    });
    
  } catch (error) {
    console.error('Error updating custom orders:', error);
    return NextResponse.json({ 
      error: 'Failed to update custom orders',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
