'use client';

import { useState, useEffect } from 'react';
import { getArtistProductAnalytics, ProductAnalytics } from '@/lib/firebase/analytics';
import { getProductsByArtistId, ArtistProduct } from '@/lib/firebase/artist-products';
import { getAllProducts } from '@/lib/firebase/products';
import { Eye, MousePointer, ShoppingCart, Share2, TrendingUp, Clock, Users } from 'lucide-react';

interface AnalyticsDashboardProps {
  artistId: string;
}

export default function AnalyticsDashboard({ artistId }: AnalyticsDashboardProps) {
  const [analytics, setAnalytics] = useState<ProductAnalytics[]>([]);
  const [products, setProducts] = useState<ArtistProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState<'7d' | '30d' | '90d' | 'all'>('30d');

  useEffect(() => {
    loadAnalyticsData();
  }, [artistId]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      // Get analytics data for the artist
      const analyticsData = await getArtistProductAnalytics(artistId);
      
      // Get both artist products and admin products for this artist
      const [artistProducts, allProducts] = await Promise.all([
        getProductsByArtistId(artistId),
        getAllProducts() // Add this to get admin products too
      ]);
      
      // Filter admin products that might belong to this artist (if any)
      // For now, we'll just use artist products, but this structure allows expansion
      const allArtistProducts = artistProducts;
      
      setAnalytics(analyticsData);
      setProducts(allArtistProducts);
    } catch (error) {
      console.error('Error loading analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Calculate totals
  const totals = analytics.reduce(
    (acc, item) => ({
      views: acc.views + item.totalViews,
      clicks: acc.clicks + item.totalClicks,
      addToCarts: acc.addToCarts + item.totalAddToCarts,
      shares: acc.shares + item.totalShares,
      purchases: acc.purchases + item.totalPurchases
    }),
    { views: 0, clicks: 0, addToCarts: 0, shares: 0, purchases: 0 }
  );

  const averageConversionRate = analytics.length > 0 
    ? analytics.reduce((acc, item) => acc + item.conversionRate, 0) / analytics.length 
    : 0;

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    if (ms < 60000) return `${Math.round(ms / 1000)}s`;
    return `${Math.round(ms / 60000)}m`;
  };

  const getProductName = (productId: string) => {
    const product = products.find(p => p.id === productId);
    return product?.name || 'Unknown Product';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold font-editorial">Product Analytics</h2>
        <select
          value={selectedPeriod}
          onChange={(e) => setSelectedPeriod(e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-md font-helvetica"
        >
          <option value="7d">Last 7 days</option>
          <option value="30d">Last 30 days</option>
          <option value="90d">Last 90 days</option>
          <option value="all">All time</option>
        </select>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 font-helvetica">Total Views</p>
              <p className="text-2xl font-bold font-helvetica">{totals.views.toLocaleString()}</p>
            </div>
            <Eye className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 font-helvetica">Total Clicks</p>
              <p className="text-2xl font-bold font-helvetica">{totals.clicks.toLocaleString()}</p>
            </div>
            <MousePointer className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 font-helvetica">Add to Carts</p>
              <p className="text-2xl font-bold font-helvetica">{totals.addToCarts.toLocaleString()}</p>
            </div>
            <ShoppingCart className="w-8 h-8 text-orange-500" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 font-helvetica">Conversion Rate</p>
              <p className="text-2xl font-bold font-helvetica">{averageConversionRate.toFixed(1)}%</p>
            </div>
            <TrendingUp className="w-8 h-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Product Performance Table */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold font-editorial">Product Performance</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="text-left p-4 font-helvetica font-medium text-gray-700">Product</th>
                <th className="text-left p-4 font-helvetica font-medium text-gray-700">Views</th>
                <th className="text-left p-4 font-helvetica font-medium text-gray-700">Clicks</th>
                <th className="text-left p-4 font-helvetica font-medium text-gray-700">Add to Cart</th>
                <th className="text-left p-4 font-helvetica font-medium text-gray-700">Shares</th>
                <th className="text-left p-4 font-helvetica font-medium text-gray-700">Avg. View Time</th>
                <th className="text-left p-4 font-helvetica font-medium text-gray-700">Conversion</th>
              </tr>
            </thead>
            <tbody>
              {analytics.map((item) => (
                <tr key={item.productId} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="p-4 font-helvetica">{getProductName(item.productId)}</td>
                  <td className="p-4 font-helvetica">
                    <div className="flex items-center">
                      <Eye className="w-4 h-4 text-blue-500 mr-2" />
                      {item.totalViews.toLocaleString()}
                    </div>
                  </td>
                  <td className="p-4 font-helvetica">
                    <div className="flex items-center">
                      <MousePointer className="w-4 h-4 text-green-500 mr-2" />
                      {item.totalClicks.toLocaleString()}
                    </div>
                  </td>
                  <td className="p-4 font-helvetica">
                    <div className="flex items-center">
                      <ShoppingCart className="w-4 h-4 text-orange-500 mr-2" />
                      {item.totalAddToCarts.toLocaleString()}
                    </div>
                  </td>
                  <td className="p-4 font-helvetica">
                    <div className="flex items-center">
                      <Share2 className="w-4 h-4 text-purple-500 mr-2" />
                      {item.totalShares.toLocaleString()}
                    </div>
                  </td>
                  <td className="p-4 font-helvetica">
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 text-gray-500 mr-2" />
                      {formatDuration(item.averageViewDuration)}
                    </div>
                  </td>
                  <td className="p-4 font-helvetica">
                    <div className="flex items-center">
                      <TrendingUp className="w-4 h-4 text-red-500 mr-2" />
                      {item.conversionRate.toFixed(1)}%
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {analytics.length === 0 && (
        <div className="text-center py-8">
          <div className="text-gray-500 mb-4">
            <TrendingUp className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-semibold font-editorial mb-2">No Analytics Data Yet</h3>
          <p className="text-gray-600 font-helvetica">
            Once customers start viewing your products, analytics data will appear here.
          </p>
        </div>
      )}
    </div>
  );
}
