import { Dispatch, SetStateAction, useCallback, useRef, useEffect } from "react";
import FocusTrap from "focus-trap-react";
import { AnimatePresence, motion } from "framer-motion";
import Leaflet from "./leaflet";
import useMediaQuery from "@/lib/hooks/use-media-query";

export default function Modal({
  children,
  showModal,
  setShowModal,
}: {
  children: React.ReactNode;
  showModal: boolean;
  setShowModal: Dispatch<SetStateAction<boolean>>;
}) {
  const desktopModalRef = useRef<HTMLDivElement | null>(null);
  const { isMobile } = useMediaQuery();

  const setDesktopModalRef = useCallback((node: HTMLDivElement | null) => {
    desktopModalRef.current = node;
  }, []);

  const onKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        setShowModal(false);
      }
    },
    [setShowModal],
  );

  useEffect(() => {
    document.addEventListener("keydown", onKeyDown);
    return () => document.removeEventListener("keydown", onKeyDown);
  }, [onKeyDown]);

  return (
    <AnimatePresence>
      {showModal && (
        <>
          {isMobile && (
            <Leaflet setShow={setShowModal}>{children}</Leaflet>
          )}
          {!isMobile && (
            <FocusTrap focusTrapOptions={{ initialFocus: false }}>
              <motion.div
                ref={desktopModalRef}
                key="desktop-modal"
                className="fixed inset-0 z-40 hidden min-h-screen items-center justify-center md:flex"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                onMouseDown={(e) => {
                  if (desktopModalRef.current === e.target) {
                    setShowModal(false);
                  }
                }}
              >
                <motion.div
                  key="desktop-modal-container"
                  className="max-h-fit w-full max-w-2xl overflow-hidden border border-gray-200 bg-white shadow-xl md:rounded-2xl"
                  initial={{ scale: 0.95 }}
                  animate={{ scale: 1 }}
                  exit={{ scale: 0.95, opacity: 0 }}
                >
                  {children}
                </motion.div>
              </motion.div>
            </FocusTrap>
          )}
        </>
      )}
    </AnimatePresence>
  );
}
