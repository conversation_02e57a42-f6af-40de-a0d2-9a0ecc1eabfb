import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
});

interface OrderItem {
  name: string;
  description: string;
  quantity: number;
  amount: number;
}

interface ShippingAddress {
  name: string;
  line1: string;
  line2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
}

export async function POST(req: NextRequest) {
  try {
    const { sessionId, paymentIntentId, orderData } = await req.json();

    // Handle both old checkout sessions and new payment intents
    let customerEmail: string;
    let customerName: string;
    let orderAmount: number;
    let orderItems: any[] = [];
    let shippingAddress: any;

    if (paymentIntentId && orderData) {
      // New payment intent flow
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

      customerEmail = orderData.customerInfo.email;
      customerName = `${orderData.customerInfo.shippingAddress.firstName} ${orderData.customerInfo.shippingAddress.lastName}`;
      orderAmount = orderData.amount;

      // Convert cart items to order items format
      orderItems = orderData.items || [];

      shippingAddress = {
        name: customerName,
        line1: orderData.customerInfo.shippingAddress.line1,
        line2: orderData.customerInfo.shippingAddress.line2,
        city: orderData.customerInfo.shippingAddress.city,
        state: orderData.customerInfo.shippingAddress.state,
        postal_code: orderData.customerInfo.shippingAddress.postalCode,
        country: orderData.customerInfo.shippingAddress.country,
      };
    } else if (sessionId) {
      // Legacy checkout session flow
      const session = await stripe.checkout.sessions.retrieve(sessionId, {
        expand: ['line_items', 'customer_details', 'shipping_details']
      });

      customerEmail = session.customer_details?.email || '';
      customerName = session.customer_details?.name || '';
      orderAmount = session.amount_total || 0;
      orderItems = session.line_items?.data || [];
      shippingAddress = (session as any).shipping_details?.address;
    } else {
      return NextResponse.json(
        { error: 'Either sessionId or paymentIntentId with orderData is required' },
        { status: 400 }
      );
    }

    // Calculate totals and prepare order details
    let productSubtotal = 0;
    let shippingCost = 0;
    let orderId = '';
    let formattedItems: any[] = [];

    if (paymentIntentId && orderData) {
      // New payment intent flow
      productSubtotal = orderData.subtotal || 0;
      shippingCost = orderData.selectedShipping?.price || 0;
      orderId = orderData.orderId || paymentIntentId;
      formattedItems = orderItems.map((item: any) => ({
        name: item.title || item.name,
        quantity: item.quantity || 1,
        amount: item.price || 0
      }));
    } else {
      // Legacy checkout session flow - separate shipping items from product items
      const shippingKeywords = ['shipping', 'delivery', 'freight'];

      const productItems = orderItems.filter((item: any) => {
        const itemName = (item.description || '').toLowerCase();
        return !shippingKeywords.some(keyword => itemName.includes(keyword));
      });

      const shippingItems = orderItems.filter((item: any) => {
        const itemName = (item.description || '').toLowerCase();
        return shippingKeywords.some(keyword => itemName.includes(keyword));
      });

      productSubtotal = productItems.reduce((sum: number, item: any) => sum + (item.amount_total / 100), 0);
      shippingCost = shippingItems.reduce((sum: number, item: any) => sum + (item.amount_total / 100), 0);
      orderId = sessionId;
      formattedItems = productItems.map((item: any) => ({
        name: item.description,
        quantity: item.quantity,
        amount: item.amount_total / 100
      }));
    }

    // Extract order details
    const orderDetails = {
      orderId,
      customerEmail,
      customerName,
      total: orderAmount / (paymentIntentId ? 1 : 100), // Payment intents are already in dollars
      subtotal: productSubtotal,
      shippingCost: shippingCost,
      currency: 'USD',
      paymentStatus: 'paid',
      items: formattedItems,
      shippingItems: [], // Initialize as empty array for new payment flow
      shippingAddress,
      orderDate: new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    };

    // Create email transporter
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: Number(process.env.SMTP_PORT),
      secure: Number(process.env.SMTP_PORT) === 465,
      auth: {
        user: process.env.SMTP_USER!,
        pass: process.env.SMTP_PASS!,
      },
    });

    // Send email to customer
    if (orderDetails.customerEmail) {
      const customerEmailContent = generateCustomerReceiptEmail(orderDetails);
      
      await transporter.sendMail({
        from: process.env.SMTP_USER,
        to: orderDetails.customerEmail,
        subject: `Order Confirmation - Milo's Menagerie #${orderDetails.orderId.slice(-8)}`,
        html: customerEmailContent.html,
        text: customerEmailContent.text,
      });
    }

    // Send email to business
    const businessEmailContent = generateBusinessNotificationEmail(orderDetails);
    
    await transporter.sendMail({
      from: process.env.SMTP_USER,
      to: '<EMAIL>',
      subject: `New Order Received - #${orderDetails.orderId.slice(-8)}`,
      html: businessEmailContent.html,
      text: businessEmailContent.text,
    });

    return NextResponse.json({ 
      success: true, 
      message: 'Receipt emails sent successfully',
      orderId: orderDetails.orderId
    });

  } catch (error) {
    console.error('Error sending receipt emails:', error);
    return NextResponse.json(
      { error: 'Failed to send receipt emails' },
      { status: 500 }
    );
  }
}

function extractShippingAddress(session: any): ShippingAddress | null {
  // First try to get from shipping details
  if (session.shipping?.address) {
    return {
      name: session.shipping.name || session.customer_details?.name || 'Customer',
      line1: session.shipping.address.line1,
      line2: session.shipping.address.line2,
      city: session.shipping.address.city,
      state: session.shipping.address.state,
      postal_code: session.shipping.address.postal_code,
      country: session.shipping.address.country
    };
  }

  // Fallback to metadata if we stored the address there
  if (session.metadata?.has_shipping_address === 'true') {
    return {
      name: session.metadata.shipping_name,
      line1: session.metadata.shipping_line1,
      line2: session.metadata.shipping_line2,
      city: session.metadata.shipping_city,
      state: session.metadata.shipping_state,
      postal_code: session.metadata.shipping_postal_code,
      country: session.metadata.shipping_country
    };
  }

  return null;
}

function generateCustomerReceiptEmail(order: any) {
  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <style>
          body { font-family: 'Helvetica', Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 20px; margin-bottom: 30px; }
          .logo { font-size: 24px; font-weight: bold; color: #000; }
          .order-number { font-size: 18px; color: #666; margin-top: 10px; }
          .section { margin-bottom: 30px; }
          .section-title { font-size: 18px; font-weight: bold; color: #000; margin-bottom: 15px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
          .item { display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #eee; }
          .item:last-child { border-bottom: none; }
          .total-row { display: flex; justify-content: space-between; padding: 10px 0; font-weight: bold; }
          .address { background: #f9f9f9; padding: 15px; border-radius: 5px; }
          .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">Milo's Menagerie</div>
            <div class="order-number">Order #${order.orderId.slice(-8)}</div>
            <div style="color: #666; margin-top: 5px;">${order.orderDate}</div>
          </div>

          <div class="section">
            <div class="section-title">Thank You for Your Order!</div>
            <p>Hi ${order.customerName || 'there'},</p>
            <p>We've received your order and can't wait to bring your items to life! Production typically takes 3 days, after which your order will ship directly to you.</p>
          </div>

          <div class="section">
            <div class="section-title">Order Items</div>
            ${order.items.map((item: any) => `
              <div class="item">
                <div>
                  <strong>${item.name}</strong><br>
                  <span style="color: #666;">Qty: ${item.quantity}</span>
                </div>
                <div>$${item.amount.toFixed(2)}</div>
              </div>
            `).join('')}
          </div>

          <div class="section">
            <div class="section-title">Order Summary</div>
            <div style="display: flex; justify-content: space-between; padding: 5px 0;">
              <span>Subtotal:</span>
              <span>$${order.subtotal.toFixed(2)}</span>
            </div>
            <div style="display: flex; justify-content: space-between; padding: 5px 0;">
              <span>Shipping${order.shippingItems && order.shippingItems.length > 0 ? ` (${order.shippingItems[0].name})` : ''}:</span>
              <span>${order.shippingCost === 0 ? 'FREE' : `$${order.shippingCost.toFixed(2)}`}</span>
            </div>
            <div class="total-row" style="border-top: 1px solid #ddd; margin-top: 10px; padding-top: 10px;">
              <span>Total:</span>
              <span>$${order.total.toFixed(2)} ${order.currency}</span>
            </div>
          </div>

          ${order.shippingAddress ? `
          <div class="section">
            <div class="section-title">Shipping Address</div>
            <div class="address">
              ${order.shippingAddress.name}<br>
              ${order.shippingAddress.line1}<br>
              ${order.shippingAddress.line2 ? order.shippingAddress.line2 + '<br>' : ''}
              ${order.shippingAddress.city}, ${order.shippingAddress.state} ${order.shippingAddress.postal_code}<br>
              ${order.shippingAddress.country}
            </div>
          </div>
          ` : ''}

          <div class="footer">
            <p>Questions about your order? Contact <NAME_EMAIL></p>
            <p>Thank you for supporting Milo's Menagerie!</p>
          </div>
        </div>
      </body>
    </html>
  `;

  const text = `
MILO'S MENAGERIE - ORDER CONFIRMATION
Order #${order.orderId.slice(-8)}
${order.orderDate}

Hi ${order.customerName || 'there'},

Thank you for your order! We've received it and we're excited to start creating your 3D printed items.

ORDER ITEMS:
${order.items.map((item: any) => `- ${item.name} (Qty: ${item.quantity}) - $${item.amount.toFixed(2)}`).join('\n')}

ORDER SUMMARY:
Subtotal: $${order.subtotal.toFixed(2)}
Shipping${order.shippingItems.length > 0 ? ` (${order.shippingItems[0].name})` : ''}: ${order.shippingCost === 0 ? 'FREE' : `$${order.shippingCost.toFixed(2)}`}
Total: $${order.total.toFixed(2)} ${order.currency}

${order.shippingAddress ? `
SHIPPING ADDRESS:
${order.shippingAddress.name}
${order.shippingAddress.line1}
${order.shippingAddress.line2 ? order.shippingAddress.line2 : ''}
${order.shippingAddress.city}, ${order.shippingAddress.state} ${order.shippingAddress.postal_code}
${order.shippingAddress.country}
` : ''}

Questions about your order? Contact <NAME_EMAIL>
Thank you for supporting Milo's Menagerie!
  `;

  return { html, text };
}

function generateBusinessNotificationEmail(order: any) {
  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <style>
          body { font-family: 'Helvetica', Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #000; color: white; padding: 20px; text-align: center; }
          .alert { background: #f0f9ff; border: 1px solid #0ea5e9; padding: 15px; margin: 20px 0; border-radius: 5px; }
          .section { margin-bottom: 25px; }
          .section-title { font-size: 16px; font-weight: bold; color: #000; margin-bottom: 10px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
          .item { background: #f9f9f9; padding: 10px; margin: 5px 0; border-radius: 3px; }
          .customer-info { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; }
          .total { background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 NEW ORDER RECEIVED!</h1>
            <p>Order #${order.orderId.slice(-8)} • ${order.orderDate}</p>
          </div>

          <div class="alert">
            <strong>Action Required:</strong> A new order has been placed and payment has been confirmed. Please begin processing this order.
          </div>

          <div class="section">
            <div class="section-title">Customer Information</div>
            <div class="customer-info">
              <strong>Name:</strong> ${order.customerName || 'Not provided'}<br>
              <strong>Email:</strong> ${order.customerEmail || 'Not provided'}<br>
              <strong>Payment Status:</strong> ${order.paymentStatus}
            </div>
          </div>

          <div class="section">
            <div class="section-title">Items to Create</div>
            ${order.items.map((item: any) => `
              <div class="item">
                <strong>${item.name}</strong><br>
                Quantity: ${item.quantity} | Amount: $${item.amount.toFixed(2)}
              </div>
            `).join('')}
          </div>

          ${order.shippingAddress ? `
          <div class="section">
            <div class="section-title">Ship To</div>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 5px;">
              <strong>${order.shippingAddress.name}</strong><br>
              ${order.shippingAddress.line1}<br>
              ${order.shippingAddress.line2 ? order.shippingAddress.line2 + '<br>' : ''}
              ${order.shippingAddress.city}, ${order.shippingAddress.state} ${order.shippingAddress.postal_code}<br>
              ${order.shippingAddress.country}
            </div>
          </div>
          ` : ''}

          ${order.shippingItems && order.shippingItems.length > 0 ? `
          <div class="section">
            <div class="section-title">Shipping Method</div>
            ${order.shippingItems.map((item: any) => `
              <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin: 5px 0;">
                <strong>${item.name}</strong><br>
                Cost: $${item.amount.toFixed(2)}
              </div>
            `).join('')}
          </div>
          ` : ''}

          <div class="section">
            <div class="section-title">Order Total</div>
            <div class="total">
              Subtotal: $${order.subtotal.toFixed(2)}<br>
              Shipping: $${order.shippingCost.toFixed(2)}${order.shippingItems.length > 0 ? ` (${order.shippingItems[0].name})` : ''}<br>
              <strong>Total Paid: $${order.total.toFixed(2)} ${order.currency}</strong>
            </div>
          </div>

          <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 30px;">
            <p><strong>Next Steps:</strong></p>
            <ol>
              <li>Begin 3D printing the ordered items</li>
              <li>Send customer a production update email</li>
              <li>Package and ship when ready</li>
              <li>Send tracking information to customer</li>
            </ol>
          </div>
        </div>
      </body>
    </html>
  `;

  const text = `
🎉 NEW ORDER RECEIVED!
Order #${order.orderId.slice(-8)} • ${order.orderDate}

CUSTOMER INFORMATION:
Name: ${order.customerName || 'Not provided'}
Email: ${order.customerEmail || 'Not provided'}
Payment Status: ${order.paymentStatus}

ITEMS TO CREATE:
${order.items.map((item: any) => `- ${item.name} (Qty: ${item.quantity}) - $${item.amount.toFixed(2)}`).join('\n')}

${order.shippingItems && order.shippingItems.length > 0 ? `
SHIPPING METHOD:
${order.shippingItems.map((item: any) => `- ${item.name} - $${item.amount.toFixed(2)}`).join('\n')}
` : ''}

${order.shippingAddress ? `
SHIP TO:
${order.shippingAddress.name}
${order.shippingAddress.line1}
${order.shippingAddress.line2 ? order.shippingAddress.line2 : ''}
${order.shippingAddress.city}, ${order.shippingAddress.state} ${order.shippingAddress.postal_code}
${order.shippingAddress.country}
` : ''}

ORDER TOTAL:
Subtotal: $${order.subtotal.toFixed(2)}
Shipping: $${order.shippingCost.toFixed(2)}${order.shippingItems.length > 0 ? ` (${order.shippingItems[0].name})` : ''}
Total Paid: $${order.total.toFixed(2)} ${order.currency}

NEXT STEPS:
1. Begin 3D printing the ordered items
2. Send customer a production update email
3. Package and ship when ready
4. Send tracking information to customer
  `;

  return { html, text };
}
