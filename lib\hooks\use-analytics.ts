import { useEffect, useRef, useState } from 'react';
import { trackAnalyticsEvent, generateSessionId, getDeviceType, AnalyticsEventType } from '@/lib/firebase/analytics';
import { auth } from '@/lib/firebase/config';

// Session storage key
const SESSION_ID_KEY = 'analytics_session_id';

/**
 * Hook for tracking product analytics
 */
export const useProductAnalytics = (productId: string, artistId?: string) => {
  const [sessionId] = useState(() => {
    // Get existing session ID or create new one
    if (typeof window !== 'undefined') {
      let storedSessionId = sessionStorage.getItem(SESSION_ID_KEY);
      if (!storedSessionId) {
        storedSessionId = generateSessionId();
        sessionStorage.setItem(SESSION_ID_KEY, storedSessionId);
      }
      return storedSessionId;
    }
    return generateSessionId();
  });

  const [startTime] = useState(() => Date.now());
  const [userId, setUserId] = useState<string | undefined>();
  const hasTrackedView = useRef(false);

  // Track user authentication state
  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      setUserId(user?.uid);
    });
    return unsubscribe;
  }, []);

  // Track page view when component mounts and artistId is available
  useEffect(() => {
    if (!hasTrackedView.current && productId && artistId) {
      console.log('🎯 Auto-tracking product view with artistId:', artistId);
      trackEvent('product_view');
      hasTrackedView.current = true;
    }
  }, [productId, artistId]);

  // Track page exit when component unmounts
  useEffect(() => {
    return () => {
      if (hasTrackedView.current) {
        const duration = Date.now() - startTime;
        trackEvent('page_exit', { duration });
      }
    };
  }, []);

  const trackEvent = async (eventType: AnalyticsEventType, additionalData?: any) => {
    try {
      console.log('🎯 Hook tracking event:', eventType, 'for product:', productId, 'with artistId:', artistId);
      
      const duration = eventType === 'page_exit' || eventType === 'product_view' 
        ? Date.now() - startTime 
        : undefined;

      await trackAnalyticsEvent({
        productId,
        eventType,
        sessionId,
        userId,
        duration: additionalData?.duration || duration,
        metadata: {
          artistId,
          referrer: typeof window !== 'undefined' ? document.referrer : undefined,
          userAgent: typeof window !== 'undefined' ? navigator.userAgent : undefined,
          deviceType: getDeviceType(),
          ...additionalData
        }
      });
    } catch (error) {
      // Silently fail to avoid disrupting user experience
      console.warn('❌ Analytics tracking failed:', error);
    }
  };

  return {
    trackEvent,
    sessionId,
    userId
  };
};

/**
 * Hook for tracking page view duration
 */
export const useViewDuration = () => {
  const startTime = useRef<number>(Date.now());
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsVisible(!document.hidden);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  const getDuration = () => {
    return Date.now() - startTime.current;
  };

  const resetTimer = () => {
    startTime.current = Date.now();
  };

  return {
    getDuration,
    resetTimer,
    isVisible,
    startTime: startTime.current
  };
};
